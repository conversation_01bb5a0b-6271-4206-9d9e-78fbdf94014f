import { Controller, Get, HttpStatus } from '@nestjs/common';
import {
  HealthCheck,
  HealthCheckService,
  HttpHealthIndicator,
  PrismaHealthIndicator,
  DiskHealthIndicator,
  MemoryHealthIndicator,
} from '@nestjs/terminus';
import { RedisHealthIndicator } from './redis.health';
import { PrismaService } from '../database/prisma/prisma.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private http: HttpHealthIndicator,
    private db: PrismaHealthIndicator,
    private disk: DiskHealthIndicator,
    private memory: MemoryHealthIndicator,
    private redis: RedisHealthIndicator,
    private prisma: PrismaService,
  ) {}

  @Get('live')
  @ApiOperation({
    summary: 'Liveness probe - checks if the application is running',
  })
  @ApiResponse({ status: HttpStatus.OK, description: 'Application is alive' })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'Application is not alive',
  })
  async checkLiveness() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      pid: process.pid,
    };
  }

  @Get('ready')
  @HealthCheck()
  @ApiOperation({
    summary:
      'Readiness probe - checks if the application is ready to serve traffic',
  })
  @ApiResponse({ status: HttpStatus.OK, description: 'Application is ready' })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'Application is not ready',
  })
  async checkReadiness() {
    return this.health.check([
      () => this.db.pingCheck('database', this.prisma),
      () => this.redis.isHealthy('redis'),
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024), // 150MB
      () => this.memory.checkRSS('memory_rss', 300 * 1024 * 1024), // 300MB
    ]);
  }

  @Get('startup')
  @HealthCheck()
  @ApiOperation({
    summary:
      'Startup probe - checks if the application has started successfully',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Application has started',
  })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'Application is still starting',
  })
  async checkStartup() {
    return this.health.check([
      () => this.db.pingCheck('database', this.prisma),
      () => this.redis.isHealthy('redis', { timeout: 5000 }),
    ]);
  }

  @Get()
  @HealthCheck()
  @ApiOperation({ summary: 'Full health check - comprehensive health status' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'All health checks passed',
  })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'One or more health checks failed',
  })
  async check() {
    return this.health.check([
      // Database health
      () => this.db.pingCheck('database', this.prisma),

      // Redis health
      () => this.redis.isHealthy('redis'),

      // External service health (example)
      () =>
        this.http.pingCheck(
          'external-api',
          process.env.EXTERNAL_API_URL || 'https://api.example.com/health',
        ),

      // Disk health
      () =>
        this.disk.checkStorage('storage', {
          path: '/',
          thresholdPercent: 0.9, // 90% threshold
        }),

      // Memory health
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024), // 150MB
      () => this.memory.checkRSS('memory_rss', 300 * 1024 * 1024), // 300MB
    ]);
  }

  @Get('metrics')
  @ApiOperation({ summary: 'Application metrics for monitoring' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Metrics retrieved successfully',
  })
  async getMetrics() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers,
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      nodejs: {
        version: process.version,
        pid: process.pid,
        platform: process.platform,
        arch: process.arch,
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        port: process.env.PORT,
      },
    };
  }

  // Add the missing nested health endpoints that are being requested
  @Get('health')
  @HealthCheck()
  @ApiOperation({ summary: 'Duplicate health check endpoint' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'All health checks passed',
  })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'One or more health checks failed',
  })
  async checkHealth() {
    return this.health.check([
      () => this.db.pingCheck('database', this.prisma),
      () => this.redis.isHealthy('redis'),
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
    ]);
  }

  @Get('health/db')
  @HealthCheck()
  @ApiOperation({ summary: 'Database health check only' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Database health check passed',
  })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'Database health check failed',
  })
  async checkDatabaseHealth() {
    return this.health.check([
      () => this.db.pingCheck('database', this.prisma),
    ]);
  }

  @Get('health/redis')
  @HealthCheck()
  @ApiOperation({ summary: 'Redis health check only' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Redis health check passed',
  })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'Redis health check failed',
  })
  async checkRedisHealth() {
    return this.health.check([
      () => this.redis.isHealthy('redis'),
    ]);
  }

  @Get('health/auth')
  @HealthCheck()
  @ApiOperation({ summary: 'Auth system health check' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Auth system health check passed',
  })
  @ApiResponse({
    status: HttpStatus.SERVICE_UNAVAILABLE,
    description: 'Auth system health check failed',
  })
  async checkAuthHealth() {
    return this.health.check([
      () => this.db.pingCheck('database', this.prisma),
      () => this.redis.isHealthy('redis'),
    ]);
  }
}
