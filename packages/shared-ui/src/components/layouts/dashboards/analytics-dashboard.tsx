import { ReactNode, useState } from "react";
import { motion } from "framer-motion";
import {
  BarChart3,
  TrendingUp,
  Users,
  DollarSign,
  Activity,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  MoreHorizontal
} from "lucide-react";

import { LuminarCard } from '../../ui/display';
import { Button } from '../../ui/actions';
import { LuminarInput, LuminarSelect } from '../../ui/forms';
import { LuminarBadge } from '../../ui/display';
import { ThemeToggle } from '../../ui/utilities';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps,
  ComponentSize
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface AnalyticsDashboardProps {
  className?: string;
  title?: string;
  subtitle?: string;
  headerActions?: ReactNode;
  sidebar?: ReactNode;
  metrics?: MetricCard[];
  charts?: ChartSection[];
  tables?: TableSection[];
  customSections?: ReactNode[];
  showFilters?: boolean;
  showRefresh?: boolean;
  showExport?: boolean;
}

export interface MetricCard {
  id: string;
  title: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon?: React.ComponentType<{ className?: string }>;
  description?: string;
}

export interface ChartSection {
  id: string;
  title: string;
  description?: string;
  component: ReactNode;
  size?: ComponentSize | 'md' | 'lg' | 'xl';
  actions?: ReactNode;
}

export interface TableSection {
  id: string;
  title: string;
  description?: string;
  component: ReactNode;
  showSearch?: boolean;
  actions?: ReactNode;
}

const defaultMetrics: MetricCard[] = [
  {
    id: 'users',
    title: 'Total Users',
    value: '24,567',
    change: '+12.3%',
    changeType: 'positive',
    icon: Users,
    description: 'Active users this month'
  },
  {
    id: 'revenue',
    title: 'Revenue',
    value: '$127,543',
    change: '+8.2%',
    changeType: 'positive',
    icon: DollarSign,
    description: 'Monthly recurring revenue'
  },
  {
    id: 'conversion',
    title: 'Conversion Rate',
    value: '3.42%',
    change: '-0.4%',
    changeType: 'negative',
    icon: TrendingUp,
    description: 'Visitor to customer conversion'
  },
  {
    id: 'activity',
    title: 'Active Sessions',
    value: '1,234',
    change: '+5.1%',
    changeType: 'positive',
    icon: Activity,
    description: 'Currently active user sessions'
  }
];

export function AnalyticsDashboard({
  className,
  title = "Analytics Dashboard",
  subtitle = "Monitor your key performance metrics and insights",
  headerActions,
  sidebar,
  metrics = defaultMetrics,
  charts = [],
  tables = [],
  customSections = [],
  showFilters = true,
  showRefresh = true,
  showExport = true,
  ...props
}: AnalyticsDashboardProps) {
  const [dateRange, setDateRange] = useState("7d");
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const getGridCols = (size: string = 'md') => {
    switch (size) {
      case 'sm': return 'lg:col-span-1';
      case 'md': return 'lg:col-span-2';
      case 'lg': return 'lg:col-span-3';
      case 'xl': return 'lg:col-span-4';
      default: return 'lg:col-span-2';
    }
  };

  return (
    <div className={cn(
      "min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50",
      "dark:from-gray-900 dark:via-blue-950 dark:to-indigo-950",
      "transition-colors duration-300",
      className
    )} {...props}>
      
      {/* Header */}
      <div className="border-b border-gray-200/20 dark:border-gray-700/20 backdrop-blur-lg bg-white/10 dark:bg-gray-900/10">
        <div className="px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                {title}
              </h1>
              {subtitle && (
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  {subtitle}
                </p>
              )}
            </div>
            
            <div className="flex items-center gap-3">
              {showFilters && (
                <div className="flex items-center gap-2">
                  <LuminarSelect
                    value={dateRange}
                    onChange={(e) => setDateRange(e.target.value)}
                    options={[
                      { value: '1d', label: 'Last 24 hours' },
                      { value: '7d', label: 'Last 7 days' },
                      { value: '30d', label: 'Last 30 days' },
                      { value: '90d', label: 'Last 90 days' },
                      { value: 'custom', label: 'Custom Range' }
                    ]}
                    className="w-40"
                  />
                  <Button variant="outline" size="sm">
                    <Filter className="w-4 h-4" />
                  </Button>
                </div>
              )}
              
              {showRefresh && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={refreshing}
                >
                  <RefreshCw className={cn("w-4 h-4", refreshing && "animate-spin")} />
                </Button>
              )}
              
              {showExport && (
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                </Button>
              )}
              
              {headerActions}
              <ThemeToggle />
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-1">
        {/* Sidebar */}
        {sidebar && (
          <div className="hidden lg:block w-64 border-r border-gray-200/20 dark:border-gray-700/20">
            {sidebar}
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1 p-4 sm:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto space-y-6">
            
            {/* Metrics Grid */}
            {metrics.length > 0 && (
              <motion.div
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                {metrics.map((metric, index) => (
                  <motion.div
                    key={metric.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                  >
                    <LuminarCard className="p-6 hover:shadow-lg transition-all">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          {metric.icon && (
                            <div className="p-2 rounded-lg bg-blue-500/10 dark:bg-blue-400/10">
                              <metric.icon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                            </div>
                          )}
                          <div>
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                              {metric.title}
                            </p>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          {metric.value}
                        </p>
                        
                        {metric.change && (
                          <div className="flex items-center gap-2">
                            <LuminarBadge
                              variant={
                                metric.changeType === 'positive' ? 'success' :
                                metric.changeType === 'negative' ? 'error' : 'default'
                              }
                              size="sm"
                            >
                              {metric.change}
                            </LuminarBadge>
                            {metric.description && (
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {metric.description}
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    </LuminarCard>
                  </motion.div>
                ))}
              </motion.div>
            )}

            {/* Charts Section */}
            {charts.length > 0 && (
              <motion.div
                className="grid grid-cols-1 lg:grid-cols-4 gap-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                {charts.map((chart, index) => (
                  <motion.div
                    key={chart.id}
                    className={getGridCols(chart.size)}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 + index * 0.1, duration: 0.6 }}
                  >
                    <LuminarCard className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {chart.title}
                          </h3>
                          {chart.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {chart.description}
                            </p>
                          )}
                        </div>
                        {chart.actions}
                      </div>
                      {chart.component}
                    </LuminarCard>
                  </motion.div>
                ))}
              </motion.div>
            )}

            {/* Tables Section */}
            {tables.length > 0 && (
              <motion.div
                className="space-y-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.6 }}
              >
                {tables.map((table, index) => (
                  <motion.div
                    key={table.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 + index * 0.1, duration: 0.6 }}
                  >
                    <LuminarCard className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {table.title}
                          </h3>
                          {table.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {table.description}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          {table.showSearch && (
                            <LuminarInput
                              placeholder="Search..."
                              className="w-64"
                              size="sm"
                            />
                          )}
                          {table.actions}
                        </div>
                      </div>
                      {table.component}
                    </LuminarCard>
                  </motion.div>
                ))}
              </motion.div>
            )}

            {/* Custom Sections */}
            {customSections.length > 0 && (
              <motion.div
                className="space-y-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.6 }}
              >
                {customSections.map((section, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 + index * 0.1, duration: 0.6 }}
                  >
                    {section}
                  </motion.div>
                ))}
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}