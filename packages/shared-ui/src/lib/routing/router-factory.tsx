/**
 * Router factory for creating consistent TanStack Router instances
 */

import { createRouter, AnyRouter } from '@tanstack/react-router'
import { RouterConfig, RouteContext, RouteAnalytics } from './types'
import { createRouteGuardMiddleware } from './guards'

/**
 * Default error component
 */
export function DefaultErrorComponent({ error, reset }: { error: Error; reset: () => void }) {
  const isDev = import.meta.env.DEV
  
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/20 rounded-full mb-4">
          <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-4">
          Something went wrong
        </h2>
        
        <p className="text-gray-600 dark:text-gray-300 text-center mb-6">
          {isDev && error.message ? error.message : 'An unexpected error occurred. Please try again.'}
        </p>
        
        {isDev && error.stack && (
          <details className="mb-6">
            <summary className="text-sm text-gray-500 dark:text-gray-400 cursor-pointer hover:text-gray-700 dark:hover:text-gray-200">
              Technical Details
            </summary>
            <pre className="mt-2 text-xs text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 p-3 rounded overflow-auto max-h-40">
              {error.stack}
            </pre>
          </details>
        )}
        
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={reset}
            className="flex-1 flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Try Again
          </button>
          
          <button
            onClick={() => window.location.href = '/'}
            className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md transition-colors"
          >
            Go Home
          </button>
        </div>
        
        <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-4">
          If this problem persists, please contact support.
        </p>
      </div>
    </div>
  )
}

/**
 * Default loading component
 */
export function DefaultLoadingComponent() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400"></div>
          <div className="animate-ping absolute top-0 left-0 h-12 w-12 border border-blue-300 dark:border-blue-600 rounded-full opacity-20"></div>
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400 animate-pulse">
          Loading...
        </p>
      </div>
    </div>
  )
}

/**
 * Default not found component
 */
export function DefaultNotFoundComponent() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full text-center">
        <div className="animate-in zoom-in duration-500">
          <h1 className="text-6xl font-bold mb-4 text-primary animate-pulse">
            404
          </h1>
          <p className="text-xl text-muted-foreground mb-8">Page not found</p>
          <button
            className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            onClick={() => window.location.href = '/'}
          >
            Go Home
          </button>
        </div>
      </div>
    </div>
  )
}

/**
 * Create a route analytics tracker
 */
export function createRouteAnalytics(): RouteAnalytics {
  return {
    trackPageView: (path: string, title?: string) => {
      // Track page view in analytics service
      console.log(`Page view: ${path}`, { title })
      
      // Example: Google Analytics
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('config', 'GA_MEASUREMENT_ID', {
          page_path: path,
          page_title: title
        })
      }
      
      // Example: Custom analytics
      if (typeof window !== 'undefined' && (window as any).__analyticsStore) {
        (window as any).__analyticsStore.trackPageView(path, title)
      }
    },
    
    trackNavigation: (from: string, to: string, method?: string) => {
      console.log(`Navigation: ${from} -> ${to}`, { method })
      
      // Track navigation in analytics service
      if (typeof window !== 'undefined' && (window as any).__analyticsStore) {
        (window as any).__analyticsStore.trackNavigation(from, to, method)
      }
    },
    
    trackError: (error: Error, route: string) => {
      console.error(`Route error in ${route}:`, error)
      
      // Track error in monitoring service
      if (typeof window !== 'undefined' && (window as any).__errorTracking) {
        (window as any).__errorTracking.captureException(error, {
          route,
          timestamp: new Date().toISOString()
        })
      }
    }
  }
}

/**
 * Create a router with shared configuration
 */
export function createLuminarRouter<TRouteTree extends any>(
  routeTree: TRouteTree,
  config: RouterConfig = {}
): AnyRouter {
  const analytics = createRouteAnalytics()
  
  const router = createRouter({
    routeTree,
    defaultPreload: 'intent', // Fixed: only valid preload values accepted
    defaultPreloadStaleTime: config.defaultPreloadStaleTime || 1000 * 60 * 5, // 5 minutes
    defaultGcTime: config.defaultGcTime || 1000 * 60 * 10, // 10 minutes
    defaultErrorComponent: config.defaultErrorComponent || DefaultErrorComponent,
    defaultPendingComponent: config.defaultPendingComponent || DefaultLoadingComponent,
    defaultNotFoundComponent: config.defaultNotFoundComponent || DefaultNotFoundComponent,
    
    // TODO: Update route change handler for current TanStack Router API
    // Route change tracking is disabled until API is updated
    
    // Route guards
    beforeLoad: config.routeGuards
      ? createRouteGuardMiddleware(config.routeGuards, config.context || {})
      : undefined,
    
    // Context
    context: config.context || {}
  })
  
  // Add router to window for debugging (development only)
  if (import.meta.env.DEV) {
    (window as any).__router = router
  }
  
  return router
}

/**
 * Announce route change for accessibility
 */
function announceRouteChange(pathname: string) {
  const announcer = document.getElementById('route-announcer')
  if (announcer) {
    announcer.textContent = `Navigated to ${pathname}`
  }
}

/**
 * Update document title based on route
 */
function updateDocumentTitle(pathname: string, context?: RouteContext) {
  // This would typically use route metadata to set the title
  // For now, we'll use a simple format
  const segments = pathname.split('/').filter(Boolean)
  const title = segments.length > 0 
    ? segments[segments.length - 1].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    : 'Home'
  
  document.title = `${title} - Luminar`
}

/**
 * Create router provider component
 */
export function createRouterProvider(router: AnyRouter) {
  return function RouterProvider({ children }: { children: React.ReactNode }) {
    return (
      <>
        {children}
        
        {/* Screen reader announcements */}
        <div
          id="route-announcer"
          aria-live="polite"
          aria-atomic="true"
          className="sr-only"
        />
        
        {/* Development tools */}
        {import.meta.env.DEV && (
          <div className="fixed bottom-4 right-4 z-50">
            <details className="bg-gray-800 text-white p-2 rounded text-xs">
              <summary>Route Info</summary>
              <pre className="mt-2 max-w-xs overflow-auto">
                {JSON.stringify(
                  {
                    pathname: window.location.pathname,
                    search: window.location.search,
                    hash: window.location.hash
                  },
                  null,
                  2
                )}
              </pre>
            </details>
          </div>
        )}
      </>
    )
  }
}

/**
 * Router configuration presets
 */
export const RouterPresets = {
  /**
   * Default configuration for most applications
   */
  default: {
    defaultPreload: 'intent' as const,
    defaultPreloadStaleTime: 1000 * 60 * 5,
    defaultGcTime: 1000 * 60 * 10,
    defaultErrorComponent: DefaultErrorComponent,
    defaultPendingComponent: DefaultLoadingComponent,
    defaultNotFoundComponent: DefaultNotFoundComponent
  },
  
  /**
   * Performance-optimized configuration
   */
  performance: {
    defaultPreload: false as const,
    defaultPreloadStaleTime: 1000 * 60 * 2,
    defaultGcTime: 1000 * 60 * 5,
    defaultErrorComponent: DefaultErrorComponent,
    defaultPendingComponent: DefaultLoadingComponent,
    defaultNotFoundComponent: DefaultNotFoundComponent
  },
  
  /**
   * Development configuration with more logging
   */
  development: {
    defaultPreload: 'intent' as const,
    defaultPreloadStaleTime: 1000 * 30,
    defaultGcTime: 1000 * 60 * 2,
    defaultErrorComponent: DefaultErrorComponent,
    defaultPendingComponent: DefaultLoadingComponent,
    defaultNotFoundComponent: DefaultNotFoundComponent
  },
  
  /**
   * Production configuration optimized for performance
   */
  production: {
    defaultPreload: false as const,
    defaultPreloadStaleTime: 1000 * 60 * 10,
    defaultGcTime: 1000 * 60 * 30,
    defaultErrorComponent: DefaultErrorComponent,
    defaultPendingComponent: DefaultLoadingComponent,
    defaultNotFoundComponent: DefaultNotFoundComponent
  }
} as const

/**
 * Get router configuration based on environment
 */
export function getRouterConfig(preset: keyof typeof RouterPresets = 'default'): RouterConfig {
  const baseConfig = RouterPresets[preset]
  const environment = import.meta.env.MODE
  
  if (import.meta.env.DEV) {
    return { ...baseConfig, ...RouterPresets.development }
  }
  
  if (import.meta.env.PROD) {
    return { ...baseConfig, ...RouterPresets.production }
  }
  
  return baseConfig
}