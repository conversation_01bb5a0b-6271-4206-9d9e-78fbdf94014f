import { ReactNode, useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Lock,
  Volume2,
  VolumeX,
  Phone,
  Video,
  Search,
  AtSign,
  Smile,
  Paperclip,
  Send,
  MoreHorizontal,
  Settings,
  UserPlus,
  Bell,
  BellOff,
  Pin,
  MessageSquare,
  Plus,
  Edit3,
  Trash2,
  Reply,
  Share,
  Star,
  Download,
  Hash,
  X
} from "lucide-react";

import { LuminarCard } from '../../ui/display';
import { Button } from '../../ui/actions';
import { LuminarInput, LuminarTextarea } from '../../ui/forms';
import { LuminarBadge, LuminarAvatar } from '../../ui/display';
import { ThemeToggle } from '../../ui/utilities';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface TeamChatProps {
  className?: string;
  workspace?: Workspace;
  channels?: Channel[];
  directMessages?: DirectMessage[];
  currentChannel?: string;
  currentUser?: User;
  messages?: TeamMessage[];
  members?: Member[];
  onSendMessage?: (content: string, channelId: string) => void;
  onChannelSelect?: (channelId: string) => void;
  onDMSelect?: (userId: string) => void;
  loading?: boolean;
  showMemberList?: boolean;
  onToggleMemberList?: () => void;
}

export interface Workspace {
  id: string;
  name: string;
  avatar?: string;
  plan: 'free' | 'pro' | 'enterprise';
}

export interface Channel {
  id: string;
  name: string;
  description?: string;
  type: 'public' | 'private' | 'voice';
  unreadCount?: number;
  active?: boolean;
  isMuted?: boolean;
  members?: number;
}

export interface DirectMessage {
  id: string;
  user: User;
  unreadCount?: number;
  lastMessage?: string;
  isOnline?: boolean;
}

export interface User {
  id: string;
  name: string;
  username: string;
  avatar?: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  role?: string;
  title?: string;
}

export interface Member extends User {
  isAdmin?: boolean;
  joinedAt: Date;
}

export interface TeamMessage {
  id: string;
  user: User;
  content: string;
  timestamp: Date;
  edited?: boolean;
  thread?: TeamMessage[];
  reactions?: Reaction[];
  attachments?: MessageAttachment[];
  mentions?: string[];
  isPinned?: boolean;
  replyTo?: string;
}

export interface Reaction {
  emoji: string;
  count: number;
  users: string[];
  hasReacted?: boolean;
}

export interface MessageAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  thumbnail?: string;
}

const defaultWorkspace: Workspace = {
  id: '1',
  name: 'My Workspace',
  plan: 'pro'
};

const defaultChannels: Channel[] = [
  { id: '1', name: 'general', type: 'public', active: true, members: 12 },
  { id: '2', name: 'random', type: 'public', members: 8 },
  { id: '3', name: 'design', type: 'private', members: 5 },
  { id: '4', name: 'dev-team', type: 'private', unreadCount: 3, members: 7 },
  { id: '5', name: 'Daily Standup', type: 'voice', members: 15 },
];

export function TeamChat({
  className,
  workspace = defaultWorkspace,
  channels = defaultChannels,
  directMessages = [],
  currentChannel = '1',
  currentUser,
  messages = [],
  members = [],
  onSendMessage,
  onChannelSelect,
  onDMSelect,
  loading = false,
  showMemberList = true,
  onToggleMemberList,
  ...props
}: TeamChatProps) {
  const [inputValue, setInputValue] = useState("");
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const currentChannelData = channels.find(c => c.id === currentChannel);

  const handleSend = () => {
    if (!inputValue.trim()) return;
    onSendMessage?.(inputValue, currentChannel);
    setInputValue("");
    setReplyingTo(null);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'busy': return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className={cn(
      "min-h-screen bg-gray-100 dark:bg-gray-900 flex",
      className
    )} {...props}>
      
      {/* Workspace Sidebar */}
      <div className="w-16 bg-gray-800 dark:bg-gray-950 flex flex-col items-center py-3 space-y-2">
        <LuminarAvatar
          src={workspace.avatar}
          alt={workspace.name}
          fallback={workspace.name.charAt(0)}
          size="md"
          className="bg-blue-500 text-white hover:rounded-lg transition-all cursor-pointer"
        />
        
        <div className="w-8 h-px bg-gray-600" />
        
        <Button
          variant="ghost"
          size="sm"
          className="w-12 h-12 rounded-full bg-gray-700 hover:bg-green-600 hover:rounded-lg transition-all"
        >
          <Plus className="w-5 h-5 text-white" />
        </Button>
      </div>

      {/* Channels Sidebar */}
      <div className="w-60 bg-gray-200 dark:bg-gray-800 border-r border-gray-300 dark:border-gray-700 flex flex-col">
        {/* Workspace Header */}
        <div className="p-4 border-b border-gray-300 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="font-bold text-gray-900 dark:text-white">
              {workspace.name}
            </h2>
            <Button variant="ghost" size="sm">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
          <LuminarBadge variant="default" size="sm" className="mt-1">
            {workspace.plan}
          </LuminarBadge>
        </div>

        {/* Search */}
        <div className="p-3">
          <LuminarInput
            placeholder="Search channels..."
            icon={Search}
            size="sm"
          />
        </div>

        {/* Channels */}
        <div className="flex-1 overflow-y-auto px-3">
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wide">
              </h3>
              <Button variant="ghost" size="sm">
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            
            {channels.map((channel) => (
              <button
                key={channel.id}
                onClick={() => onChannelSelect?.(channel.id)}
                className={cn(
                  "w-full flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-300 dark:hover:bg-gray-700 transition-colors",
                  channel.active && "bg-blue-500 text-white hover:bg-blue-600"
                )}
              >
                {channel.type === 'voice' ? (
                  <Volume2 className="w-4 h-4" />
                ) : channel.type === 'private' ? (
                  <Lock className="w-4 h-4" />
                ) : (
                  <Hash className="w-4 h-4" />
                )}
                
                <span className="flex-1 text-left text-sm font-medium truncate">
                  {channel.name}
                </span>
                
                {channel.unreadCount && (
                  <LuminarBadge variant="error" size="sm">
                    {channel.unreadCount}
                  </LuminarBadge>
                )}
                
                {channel.isMuted && (
                  <BellOff className="w-3 h-3 opacity-50" />
                )}
              </button>
            ))}
          </div>

          {/* Direct Messages */}
          {directMessages.length > 0 && (
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wide">
                  Direct Messages
                </h3>
                <Button variant="ghost" size="sm">
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
              
              {directMessages.map((dm) => (
                <button
                  key={dm.id}
                  onClick={() => onDMSelect?.(dm.user.id)}
                  className="w-full flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-300 dark:hover:bg-gray-700 transition-colors"
                >
                  <div className="relative">
                    <LuminarAvatar
                      src={dm.user.avatar}
                      alt={dm.user.name}
                      fallback={dm.user.name.charAt(0)}
                      size="sm"
                    />
                    <div className={cn(
                      "absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800",
                      getStatusColor(dm.user.status)
                    )} />
                  </div>
                  
                  <div className="flex-1 text-left min-w-0">
                    <p className="text-sm font-medium truncate">{dm.user.name}</p>
                    {dm.lastMessage && (
                      <p className="text-xs text-gray-500 truncate">{dm.lastMessage}</p>
                    )}
                  </div>
                  
                  {dm.unreadCount && (
                    <LuminarBadge variant="error" size="sm">
                      {dm.unreadCount}
                    </LuminarBadge>
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* User Profile */}
        {currentUser && (
          <div className="p-3 border-t border-gray-300 dark:border-gray-700">
            <div className="flex items-center gap-2">
              <div className="relative">
                <LuminarAvatar
                  src={currentUser.avatar}
                  alt={currentUser.name}
                  fallback={currentUser.name.charAt(0)}
                  size="sm"
                />
                <div className={cn(
                  "absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800",
                  getStatusColor(currentUser.status)
                )} />
              </div>
              
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{currentUser.name}</p>
                <p className="text-xs text-gray-500">@{currentUser.username}</p>
              </div>
              
              <Button variant="ghost" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Channel Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {currentChannelData?.type === 'voice' ? (
                <Volume2 className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              ) : currentChannelData?.type === 'private' ? (
                <Lock className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              ) : (
                <Hash className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              )}
              
              <div>
                <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                  {currentChannelData?.name}
                </h1>
                {currentChannelData?.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {currentChannelData.description}
                  </p>
                )}
              </div>
              
              <LuminarBadge variant="default" size="sm">
                {currentChannelData?.members} members
              </LuminarBadge>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm">
                <Phone className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Video className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <UserPlus className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Search className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={onToggleMemberList}>
                <MoreHorizontal className="w-4 h-4" />
              </Button>
              <ThemeToggle />
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message, index) => {
            const prevMessage = messages[index - 1];
            const isGrouped = prevMessage?.user.id === message.user.id && 
              (message.timestamp.getTime() - prevMessage.timestamp.getTime()) < 5 * 60 * 1000;

            return (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className={cn(
                  "group hover:bg-gray-50 dark:hover:bg-gray-800/50 px-3 py-2 rounded transition-colors",
                  isGrouped && "mt-1"
                )}
              >
                <div className="flex gap-3">
                  {!isGrouped && (
                    <LuminarAvatar
                      src={message.user.avatar}
                      alt={message.user.name}
                      fallback={message.user.name.charAt(0)}
                      size="sm"
                    />
                  )}
                  
                  {isGrouped && <div className="w-8" />}
                  
                  <div className="flex-1 min-w-0">
                    {!isGrouped && (
                      <div className="flex items-baseline gap-2 mb-1">
                        <span className="font-semibold text-gray-900 dark:text-white">
                          {message.user.name}
                        </span>
                        {message.user.role && (
                          <LuminarBadge variant="default" size="sm">
                            {message.user.role}
                          </LuminarBadge>
                        )}
                        <span className="text-xs text-gray-500">
                          {formatTime(message.timestamp)}
                        </span>
                        {message.edited && (
                          <span className="text-xs text-gray-400">(edited)</span>
                        )}
                        {message.isPinned && (
                          <Pin className="w-3 h-3 text-yellow-500" />
                        )}
                      </div>
                    )}
                    
                    <div className="prose prose-sm max-w-none dark:prose-invert">
                      <p className="whitespace-pre-wrap">{message.content}</p>
                    </div>
                    
                    {message.attachments && message.attachments.length > 0 && (
                      <div className="mt-2 space-y-2">
                        {message.attachments.map((attachment) => (
                          <LuminarCard key={attachment.id} className="p-3 max-w-sm">
                            <div className="flex items-center gap-3">
                              {attachment.thumbnail ? (
                                <img
                                  src={attachment.thumbnail}
                                  alt={attachment.name}
                                  className="w-12 h-12 object-cover rounded"
                                />
                              ) : (
                                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center">
                                  <Paperclip className="w-6 h-6" />
                                </div>
                              )}
                              <div className="flex-1 min-w-0">
                                <p className="font-medium truncate">{attachment.name}</p>
                                <p className="text-sm text-gray-500">{attachment.size} bytes</p>
                              </div>
                              <Button variant="ghost" size="sm">
                                <Download className="w-4 h-4" />
                              </Button>
                            </div>
                          </LuminarCard>
                        ))}
                      </div>
                    )}
                    
                    {message.reactions && message.reactions.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {message.reactions.map((reaction, idx) => (
                          <Button
                            key={idx}
                            variant="default"
                            size="sm"
                            className={cn(
                              "h-6 px-2 text-xs",
                              reaction.hasReacted && "bg-blue-100 dark:bg-blue-900 border-blue-300 dark:border-blue-700"
                            )}
                          >
                            {reaction.emoji} {reaction.count}
                          </Button>
                        ))}
                      </div>
                    )}
                  </div>
                  
                  {/* Message Actions */}
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center gap-1">
                    <Button variant="ghost" size="sm">
                      <Smile className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => setReplyingTo(message.id)}>
                      <Reply className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <MessageSquare className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            );
          })}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
          {replyingTo && (
            <div className="mb-3 p-2 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Replying to message
              </span>
              <Button variant="ghost" size="sm" onClick={() => setReplyingTo(null)}>
                <X className="w-4 h-4" />
              </Button>
            </div>
          )}
          
          <div className="flex items-end gap-3">
            <div className="flex-1">
              <LuminarTextarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={`Message #${currentChannelData?.name}`}
                className="min-h-[2.5rem] max-h-32 resize-none"
                disabled={loading}
              />
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm">
                <Paperclip className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={() => setShowEmojiPicker(!showEmojiPicker)}>
                <Smile className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <AtSign className="w-4 h-4" />
              </Button>
              <Button
                onClick={handleSend}
                disabled={loading || !inputValue.trim()}
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Member List Sidebar */}
      {showMemberList && (
        <div className="w-60 bg-gray-100 dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 p-4">
          <h3 className="font-semibold text-gray-900 dark:text-white mb-4">
            Members ({members.length})
          </h3>
          
          <div className="space-y-2">
            {members.map((member) => (
              <div key={member.id} className="flex items-center gap-3 p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
                <div className="relative">
                  <LuminarAvatar
                    src={member.avatar}
                    alt={member.name}
                    fallback={member.name.charAt(0)}
                    size="sm"
                  />
                  <div className={cn(
                    "absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800",
                    getStatusColor(member.status)
                  )} />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <p className="text-sm font-medium truncate">{member.name}</p>
                    {member.isAdmin && (
                      <LuminarBadge variant="default" size="sm">Admin</LuminarBadge>
                    )}
                  </div>
                  {member.title && (
                    <p className="text-xs text-gray-500 truncate">{member.title}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}