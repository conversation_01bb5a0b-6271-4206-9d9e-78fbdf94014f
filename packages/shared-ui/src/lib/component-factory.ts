import React, { JSX } from 'react';
import { motion, MotionProps } from 'framer-motion';
import { cn } from './utils';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../types/component-props';
import { defaultComponentProps } from '../types/component-props';
  type GlassVariant, 
  type ComponentSize, 
  type GlassConfig,
  getGlassClasses,
  componentSizes,
  animationPresets 

// Base component props interface
export interface BaseComponentProps {
  variant?: GlassVariant;
  size?: ComponentSize;
  glass?: boolean;
  animated?: boolean;
  interactive?: boolean;
  className?: string;
  children?: React.ReactNode;
}

// Extended props for motion components
export interface MotionComponentProps extends BaseComponentProps {
  initial?: MotionProps['initial'];
  animate?: MotionProps['animate'];
  exit?: MotionProps['exit'];
  transition?: MotionProps['transition'];
  whileHover?: MotionProps['whileHover'];
  whileTap?: MotionProps['whileTap'];
  whileFocus?: MotionProps['whileFocus'];
}

// Component factory function
export function createGlassComponent<T extends React.ElementType>(
  Component: T,
  defaultProps: Partial<BaseComponentProps> = {}
) {
  const GlassComponent = React.forwardRef<
    any,
    React.ComponentPropsWithoutRef<T> & MotionComponentProps
  >(function GlassComponent({ 
    variant = defaultProps.variant || 'default',
    size = defaultProps.size || 'md',
    glass = defaultProps.glass ?? true,
    animated = defaultProps.animated ?? false,
    interactive = defaultProps.interactive ?? true,
    className,
    children,
    // Motion props
    initial,
    animate,
    exit,
    transition,
    whileHover,
    whileTap,
    whileFocus,
    // Destructure to avoid passing to DOM
    ...restProps
  }, ref) {
    // Get glass classes if enabled
    const glassClasses = glass ? getGlassClasses(variant) : '';
    
    // Get size classes
    const sizeClasses = (componentSizes as any)[size] || componentSizes.md;
    
    // Build animation props
    const animationProps: MotionProps = {
      initial: initial || (animated ? (animationPresets.fadeIn as any).initial : undefined),
      animate: animate || (animated ? (animationPresets.fadeIn as any).animate : undefined),
      exit: exit || (animated ? (animationPresets.fadeIn as any).exit : undefined),
      transition: transition || (animated ? { duration: 0.3 } : undefined),
      whileHover: whileHover || (interactive ? animationPresets.hover as any : undefined),
      whileTap: whileTap || (interactive ? animationPresets.tap as any : undefined),
      whileFocus: whileFocus || (interactive ? animationPresets.focus as any : undefined),
    };
    
    // Combine all classes
    const combinedClassName = cn(
      glassClasses,
      (sizeClasses as any).padding,
      (sizeClasses as any).text,
      (sizeClasses as any).radius,
      interactive && 'transition-all duration-300',
      className
    );

    // Create motion component if animations are enabled
    if (animated || whileHover || whileTap || whileFocus) {
      const MotionComponent = motion(Component as any);
      return React.createElement(
        MotionComponent,
        {
          ref,
          className: combinedClassName,
          ...animationProps,
          ...restProps,
        },
        children
      );
    }

    // Return regular component
    return React.createElement(
      Component as any,
      {
        ref,
        className: combinedClassName,
        ...restProps,
      },
      children
    );
  });
  
  return GlassComponent;
}

// Higher-order component for adding glass effects
export function withGlassEffect<P extends object>(
  Component: React.ComponentType<P>,
  config: GlassConfig = { variant: 'default' }
) {
  return React.forwardRef<any, P & BaseComponentProps>(function WithGlassEffect(props, ref) {
    const { variant = config.variant, className, ...restProps } = props as any;
    const glassClasses = getGlassClasses(variant, config);
    
    return React.createElement(
      Component,
      {
        ref,
        className: cn(glassClasses, className),
        ...restProps as P,
      }
    );
  });
}

// Utility for creating glass button variants
export function createGlassButton(
  variant: GlassVariant = 'button',
  size: ComponentSize = 'md'
) {
  return createGlassComponent('button', {
    variant,
    size,
    glass: true,
    animated: true,
    interactive: true,
  });
}

// Utility for creating glass card variants
export function createGlassCard(
  variant: GlassVariant = 'card',
  size: ComponentSize = 'md'
) {
  return createGlassComponent('div', {
    variant,
    size,
    glass: true,
    animated: false,
    interactive: false,
  });
}