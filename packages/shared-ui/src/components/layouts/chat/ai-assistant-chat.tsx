import { ReactNode, useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Send,
  <PERSON>c<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MoreHorizontal,
  Copy,
  <PERSON>humbsUp,
  ThumbsDown,
  RefreshCw,
  Trash2,
  Edit3,
  Download,
  Share,
  Zap,
  Brain,
  History,
  Settings,
  Plus,
  Search
} from "lucide-react";

import { LuminarCard } from '../../ui/display';
import { Button } from '../../ui/actions';
import { LuminarInput, LuminarTextarea } from '../../ui/forms';
import { LuminarBadge, LuminarAvatar } from '../../ui/display';
import { ThemeToggle } from '../../ui/utilities';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface AIAssistantChatProps {
  className?: string;
  title?: string;
  subtitle?: string;
  messages?: Message[];
  onSendMessage?: (content: string, attachments?: File[]) => void;
  loading?: boolean;
  placeholder?: string;
  showSidebar?: boolean;
  conversations?: Conversation[];
  onConversationSelect?: (id: string) => void;
  onNewConversation?: () => void;
  currentConversationId?: string;
  maxTokens?: number;
  usedTokens?: number;
  model?: string;
  onModelChange?: (model: string) => void;
  availableModels?: AIModel[];
  showTokenCounter?: boolean;
  allowAttachments?: boolean;
  allowVoice?: boolean;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  attachments?: Attachment[];
  isStreaming?: boolean;
  error?: string;
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
  };
}

export interface Conversation {
  id: string;
  title: string;
  lastMessage?: string;
  timestamp: Date;
  messageCount: number;
  model?: string;
}

export interface Attachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url?: string;
}

export interface AIModel {
  id: string;
  name: string;
  description: string;
  maxTokens: number;
  costPer1K?: number;
}

const defaultModels: AIModel[] = [
  { id: 'gpt-4', name: 'GPT-4', description: 'Most capable model', maxTokens: 8192 },
  { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Fast and efficient', maxTokens: 4096 },
  { id: 'claude-3', name: 'Claude 3', description: 'Excellent reasoning', maxTokens: 200000 },
];

export function AIAssistantChat({
  className,
  title = "AI Assistant",
  subtitle = "Your intelligent conversation partner",
  messages = [],
  onSendMessage,
  loading = false,
  placeholder = "Ask me anything...",
  showSidebar = true,
  conversations = [],
  onConversationSelect,
  onNewConversation,
  currentConversationId,
  maxTokens = 4096,
  usedTokens = 0,
  model = 'gpt-4',
  onModelChange,
  availableModels = defaultModels,
  showTokenCounter = true,
  allowAttachments = true,
  allowVoice = true,
  ...props
}: AIAssistantChatProps) {
  const [inputValue, setInputValue] = useState("");
  const [isRecording, setIsRecording] = useState(false);
  const [attachments, setAttachments] = useState<File[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSend = () => {
    if (!inputValue.trim() && attachments.length === 0) return;
    
    onSendMessage?.(inputValue, attachments);
    setInputValue("");
    setAttachments([]);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAttachments([...attachments, ...files]);
  };

  const removeAttachment = (index: number) => {
    setAttachments(attachments.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={cn(
      "min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50",
      "dark:from-gray-900 dark:via-blue-950 dark:to-indigo-950",
      "flex transition-colors duration-300",
      className
    )} {...props}>
      
      {/* Sidebar */}
      {showSidebar && (
        <div className="w-80 border-r border-gray-200/20 dark:border-gray-700/20 bg-white/10 dark:bg-gray-900/10 backdrop-blur-lg">
          <div className="p-4 border-b border-gray-200/20 dark:border-gray-700/20">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              </h2>
              <Button size="sm" onClick={onNewConversation}>
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            
            <LuminarInput
              placeholder="Search conversations..."
              icon={Search}
              size="sm"
            />
          </div>
          
          <div className="p-4 space-y-2 max-h-[calc(100vh-200px)] overflow-y-auto">
            {conversations.map((conversation) => (
              <motion.div
                key={conversation.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant={currentConversationId === conversation.id ? "default" : "ghost"}
                  className="w-full justify-start text-left h-auto p-3"
                  onClick={() => onConversationSelect?.(conversation.id)}
                >
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{conversation.title}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                      {conversation.lastMessage}
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <span className="text-xs text-gray-400">
                        {conversation.messageCount} messages
                      </span>
                      {conversation.model && (
                        <LuminarBadge variant="default" size="sm">
                          {conversation.model}
                        </LuminarBadge>
                      )}
                    </div>
                  </div>
                </Button>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b border-gray-200/20 dark:border-gray-700/20 bg-white/10 dark:bg-gray-900/10 backdrop-blur-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-500/10 dark:bg-blue-400/10">
                <Brain className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {title}
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {subtitle}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {/* Model Selector */}
              <select
                value={model}
                onChange={(e) => onModelChange?.(e.target.value)}
                className="px-3 py-1 text-sm bg-white/20 dark:bg-gray-800/20 border border-gray-200/20 dark:border-gray-700/20 rounded-lg backdrop-blur-sm"
              >
                {availableModels.map((m) => (
                  <option key={m.id} value={m.id}>
                    {m.name}
                  </option>
                ))}
              </select>
              
              {/* Token Counter */}
              {showTokenCounter && (
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <span className={cn(
                    usedTokens > maxTokens * 0.8 ? "text-orange-500" : 
                    usedTokens > maxTokens * 0.9 ? "text-red-500" : ""
                  )}>
                    {usedTokens}
                  </span>
                  /{maxTokens} tokens
                </div>
              )}
              
              <Button variant="ghost" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
              
              <ThemeToggle />
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          <AnimatePresence>
            {messages.map((message, index) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
                className={cn(
                  "flex gap-3",
                  message.role === 'user' ? "justify-end" : "justify-start"
                )}
              >
                {message.role === 'assistant' && (
                  <LuminarAvatar
                    size="sm"
                    fallback="AI"
                    className="bg-blue-500 text-white"
                  />
                )}
                
                <div className={cn(
                  "max-w-[70%] space-y-2",
                  message.role === 'user' ? "items-end" : "items-start"
                )}>
                  <LuminarCard className={cn(
                    "p-4",
                    message.role === 'user' 
                      ? "bg-blue-500 text-white ml-auto"
                      : "bg-white/60 dark:bg-gray-800/60",
                    message.error && "border-red-500 bg-red-50 dark:bg-red-950"
                  )}>
                    {message.error ? (
                      <div className="text-red-600 dark:text-red-400">
                        <p className="font-medium">Error</p>
                        <p className="text-sm">{message.error}</p>
                      </div>
                    ) : (
                      <div className="prose prose-sm max-w-none dark:prose-invert">
                        {message.isStreaming ? (
                          <div className="flex items-center gap-2">
                            <span>{message.content}</span>
                            <div className="w-2 h-4 bg-current animate-pulse" />
                          </div>
                        ) : (
                          <p className="whitespace-pre-wrap">{message.content}</p>
                        )}
                      </div>
                    )}
                    
                    {message.attachments && message.attachments.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {message.attachments.map((attachment) => (
                          <div key={attachment.id} className="flex items-center gap-2 p-2 bg-gray-100 dark:bg-gray-700 rounded">
                            <Paperclip className="w-4 h-4" />
                            <span className="text-sm">{attachment.name}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </LuminarCard>
                  
                  <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>{message.timestamp.toLocaleTimeString()}</span>
                    
                    {message.role === 'assistant' && !message.error && (
                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="sm">
                          <Copy className="w-3 h-3" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <ThumbsUp className="w-3 h-3" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <ThumbsDown className="w-3 h-3" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <RefreshCw className="w-3 h-3" />
                        </Button>
                      </div>
                    )}
                    
                    {message.metadata && (
                      <div className="flex items-center gap-2">
                        {message.metadata.model && (
                          <LuminarBadge variant="default" size="sm">
                            {message.metadata.model}
                          </LuminarBadge>
                        )}
                        {message.metadata.tokens && (
                          <span>{message.metadata.tokens} tokens</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                
                {message.role === 'user' && (
                  <LuminarAvatar
                    size="sm"
                    fallback="You"
                    className="bg-gray-500 text-white"
                  />
                )}
              </motion.div>
            ))}
          </AnimatePresence>
          
          {loading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex gap-3"
            >
              <LuminarAvatar
                size="sm"
                fallback="AI"
                className="bg-blue-500 text-white"
              />
              <LuminarCard className="p-4 bg-white/60 dark:bg-gray-800/60">
                <div className="flex items-center gap-2">
                  <div className="flex gap-1">
                    {[0, 1, 2].map((i) => (
                      <motion.div
                        key={i}
                        className="w-2 h-2 bg-gray-400 rounded-full"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ 
                          duration: 0.8, 
                          repeat: Infinity, 
                          delay: i * 0.2 
                        }}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-500">Thinking...</span>
                </div>
              </LuminarCard>
            </motion.div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="border-t border-gray-200/20 dark:border-gray-700/20 bg-white/10 dark:bg-gray-900/10 backdrop-blur-lg p-4">
          {/* Attachments Preview */}
          {attachments.length > 0 && (
            <div className="mb-3 flex flex-wrap gap-2">
              {attachments.map((file, index) => (
                <div key={index} className="flex items-center gap-2 px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
                  <Paperclip className="w-4 h-4" />
                  <span className="text-sm">{file.name}</span>
                  <span className="text-xs text-gray-500">{formatFileSize(file.size)}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeAttachment(index)}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}
          
          <div className="flex items-end gap-3">
            <div className="flex-1">
              <LuminarTextarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={placeholder}
                className="min-h-[2.5rem] max-h-32 resize-none"
                disabled={loading}
              />
            </div>
            
            <div className="flex items-center gap-2">
              {allowAttachments && (
                <>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    className="hidden"
                    onChange={handleFileSelect}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Paperclip className="w-4 h-4" />
                  </Button>
                </>
              )}
              
              {allowVoice && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsRecording(!isRecording)}
                  className={cn(isRecording && "text-red-500")}
                >
                  {isRecording ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                </Button>
              )}
              
              <Button
                onClick={handleSend}
                disabled={loading || (!inputValue.trim() && attachments.length === 0)}
                className="px-4"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}