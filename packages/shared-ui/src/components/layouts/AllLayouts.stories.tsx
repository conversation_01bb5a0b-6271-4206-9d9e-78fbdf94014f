import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import React from 'react';
import { 
  Layout, 
  Smartphone, 
  Monitor, 
  Tablet, 
  Grid3x3, 
  MessageCircle, 
  BarChart3, 
  Store, 
  Users, 
  Search, 
  Settings,
  Home,
  Package,
  Globe,
  Zap,
  Brain,
  Bot,
  User,
  ShoppingCart,
  DollarSign,
  Activity,
  Bell,
  Star,
  Heart,
  Eye,
  Download,
  Upload,
  Edit,
  Trash2,
  Plus,
  Filter,
  Calendar,
  Clock,
  Tag,
  Shield,
  Lock,
  Unlock,
  Key,
  Mail,
  Phone,
  Video,
  Mic,
  Camera,
  File,
  Folder,
  Archive,
  Bookmark,
  Flag,
  Target,
  Compass,
  Map,
  Navigation,
  Anchor,
  Layers,
  Grid,
  Square,
  Circle,
  Triangle,
  Hexagon,
  Diamond,
  Sparkles,
  Sun,
  Moon,
  Cloud,
  Umbrella,
  Snowflake,
  Flame,
  Droplet,
  Leaf,
  Flower,
  Trees,
  Mountain,
  Waves,
  Wind,
  Zap,
  Rainbow,
  Sunrise,
  Sunset,
  Stars,
  Star,
  Rocket,
  Satellite,
  Telescope,
  Microscope,
  Dna,
  Atom,
  Magnet,
  Battery,
  Plug,
  Wifi,
  Bluetooth,
  Radio,
  Headphones,
  Speaker,
  Volume,
  VolumeX,
  Play,
  Pause,
  Square,
  SkipBack,
  SkipForward,
  Rewind,
  FastForward,
  Shuffle,
  Repeat,
  Repeat1,
  RotateCcw,
  RotateCw,
  FlipHorizontal,
  FlipVertical,
  Maximize,
  Minimize,
  Expand,
  Shrink,
  Move,
  Copy,
  Scissors,
  Clipboard,
  Clipboard,
  Paperclip,
  Link,
  ExternalLink,
  Unlink,
  Hash,
  AtSign,
  Percent,
  DollarSign,
  Euro,
  Hash,
  Hash,
  Hash,
  Hash,
  Bitcoin,
  CreditCard,
  Wallet,
  Coins,
  Banknote,
  Receipt,
  Calculator,
  Calculator,
  PiggyBank,
  Vault,
  Lock,
  Award,
  Trophy,
  Medal,
  Crown,
  Gem,
  Gift,
  Cake,
  PartyPopper,
  Circle,
  Sparkles,
  Sparkles,
  Sparkle,
  Flame,
  Flashlight,
  Lamp,
  Lightbulb,
  Lightbulb,
  Flame,
  Flame as CandleIcon,
  Zap as FlashlightIcon,
  Lightbulb as LampIcon,
  Lightbulb as LightbulbIcon2,
  Lightbulb as LanternIcon,
  Flame as TorchIcon
} from 'lucide-react';

const meta: Meta = {
  title: 'Layouts/Overview',
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A comprehensive overview of all available layout components in the Luminar UI library.'
      }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

const LayoutCard: React.FC<{
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  useCases: string[];
  preview?: React.ReactNode;
}> = ({ title, description, icon, features, useCases, preview }) => (
  <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
    <div className="flex items-start gap-4 mb-4">
      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400">
        {icon}
      </div>
      <div className="flex-1">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{title}</h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm">{description}</p>
      </div>
    </div>
    
    {preview && (
      <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
        {preview}
      </div>
    )}
    
    <div className="space-y-4">
      <div>
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Key Features</h4>
        <ul className="space-y-1">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
              {feature}
            </li>
          ))}
        </ul>
      </div>
      
      <div>
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Use Cases</h4>
        <div className="flex flex-wrap gap-1">
          {useCases.map((useCase, index) => (
            <span key={index} className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded">
              {useCase}
            </span>
          ))}
        </div>
      </div>
    </div>
  </div>
);

const ResponsivePreview: React.FC<{ 
  title: string; 
  children: React.ReactNode; 
  sizes: Array<{ name: string; width: string; height: string }> 
}> = ({ title, children, sizes }) => (
  <div className="space-y-4">
    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{title}</h3>
    <div className="flex flex-wrap gap-4">
      {sizes.map((size, index) => (
        <div key={index} className="space-y-2">
          <div className="text-sm text-gray-600 dark:text-gray-400 text-center">{size.name}</div>
          <div 
            className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-800"
            style={{ width: size.width, height: size.height }}
          >
            <div className="scale-[0.3] origin-top-left w-[333.33%] h-[333.33%] overflow-hidden">
              {children}
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

export const Overview: Story = {
  render: () => (
    <div className="p-8 max-w-7xl mx-auto space-y-12">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white">Layout Components</h1>
        <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
          Comprehensive layout components for building modern applications. From simple dashboards to complex multi-panel interfaces.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <LayoutCard
          title="SaaS App Shell"
          description="Complete application shell with sidebar navigation, header, and main content area."
          icon={<Layout className="w-5 h-5" />}
          features={[
            'Collapsible sidebar navigation',
            'User profile dropdown',
            'Notification system',
            'Breadcrumb navigation',
            'Responsive design'
          ]}
          useCases={['Admin panels', 'Business apps', 'SaaS platforms', 'Internal tools']}
          preview={
            <div className="flex items-center gap-2">
              <div className="w-2 h-8 bg-blue-500 rounded"></div>
              <div className="flex-1 space-y-1">
                <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded w-3/4"></div>
              </div>
            </div>
          }
        />
        
        <LayoutCard
          title="E-commerce Layout"
          description="Shopping-focused layout with product grids, cart, and category navigation."
          icon={<Store className="w-5 h-5" />}
          features={[
            'Product grid display',
            'Shopping cart integration',
            'Category navigation',
            'Search functionality',
            'Mobile-first design'
          ]}
          useCases={['Online stores', 'Marketplaces', 'Product catalogs', 'Retail apps']}
          preview={
            <div className="grid grid-cols-3 gap-1">
              <div className="aspect-square bg-gray-200 dark:bg-gray-600 rounded"></div>
              <div className="aspect-square bg-gray-200 dark:bg-gray-600 rounded"></div>
              <div className="aspect-square bg-gray-200 dark:bg-gray-600 rounded"></div>
            </div>
          }
        />
        
        <LayoutCard
          title="AI Assistant Chat"
          description="Conversational interface optimized for AI interactions with smart features."
          icon={<Bot className="w-5 h-5" />}
          features={[
            'Message history',
            'Typing indicators',
            'File attachments',
            'Quick suggestions',
            'Capability showcase'
          ]}
          useCases={['AI chatbots', 'Customer support', 'Virtual assistants', 'Help systems']}
          preview={
            <div className="space-y-2">
              <div className="flex gap-2">
                <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                <div className="flex-1 h-3 bg-gray-200 dark:bg-gray-600 rounded"></div>
              </div>
              <div className="flex gap-2 justify-end">
                <div className="w-3/4 h-3 bg-blue-100 dark:bg-blue-900 rounded"></div>
                <div className="w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
              </div>
            </div>
          }
        />
        
        <LayoutCard
          title="Team Chat"
          description="Collaborative chat interface with channels, direct messages, and team features."
          icon={<MessageCircle className="w-5 h-5" />}
          features={[
            'Channel management',
            'Direct messaging',
            'User presence',
            'File sharing',
            'Team organization'
          ]}
          useCases={['Team communication', 'Project collaboration', 'Remote work', 'Community chat']}
          preview={
            <div className="flex gap-2">
              <div className="w-6 space-y-1">
                <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="h-2 bg-blue-500 rounded"></div>
              </div>
              <div className="flex-1 space-y-1">
                <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded w-2/3"></div>
              </div>
            </div>
          }
        />
        
        <LayoutCard
          title="Admin Dashboard"
          description="Comprehensive admin interface with user management and system monitoring."
          icon={<Users className="w-5 h-5" />}
          features={[
            'User management',
            'System alerts',
            'Activity monitoring',
            'Statistics overview',
            'Quick actions'
          ]}
          useCases={['Admin panels', 'System monitoring', 'User management', 'Content moderation']}
          preview={
            <div className="grid grid-cols-2 gap-2">
              <div className="h-4 bg-blue-100 dark:bg-blue-900 rounded"></div>
              <div className="h-4 bg-green-100 dark:bg-green-900 rounded"></div>
              <div className="h-4 bg-orange-100 dark:bg-orange-900 rounded"></div>
              <div className="h-4 bg-purple-100 dark:bg-purple-900 rounded"></div>
            </div>
          }
        />
        
        <LayoutCard
          title="Analytics Dashboard"
          description="Data visualization dashboard with charts, metrics, and insights."
          icon={<BarChart3 className="w-5 h-5" />}
          features={[
            'Interactive charts',
            'Real-time metrics',
            'Time range selection',
            'Data export',
            'Drill-down capabilities'
          ]}
          useCases={['Business intelligence', 'Data analysis', 'Reporting', 'Performance monitoring']}
          preview={
            <div className="space-y-2">
              <div className="flex gap-1">
                <div className="w-2 h-4 bg-blue-500 rounded"></div>
                <div className="w-2 h-6 bg-blue-500 rounded"></div>
                <div className="w-2 h-3 bg-blue-500 rounded"></div>
                <div className="w-2 h-5 bg-blue-500 rounded"></div>
              </div>
              <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded"></div>
            </div>
          }
        />
        
        <LayoutCard
          title="Content Layouts"
          description="Specialized layouts for content management, articles, and media."
          icon={<File className="w-5 h-5" />}
          features={[
            'Article layouts',
            'Media galleries',
            'Content editing',
            'Comment systems',
            'SEO optimization'
          ]}
          useCases={['Blogs', 'News sites', 'Documentation', 'Content management']}
          preview={
            <div className="space-y-2">
              <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded"></div>
              <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded w-4/5"></div>
              <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded w-3/5"></div>
            </div>
          }
        />
        
        <LayoutCard
          title="Navigation Layouts"
          description="Advanced navigation patterns including mega menus and command bars."
          icon={<Navigation className="w-5 h-5" />}
          features={[
            'Mega menus',
            'Command palette',
            'Breadcrumbs',
            'Wizard flows',
            'Search integration'
          ]}
          useCases={['Complex navigation', 'Search interfaces', 'Multi-step processes', 'Command systems']}
          preview={
            <div className="space-y-2">
              <div className="flex gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded"></div>
                <div className="w-3 h-3 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="w-3 h-3 bg-gray-200 dark:bg-gray-600 rounded"></div>
              </div>
              <div className="h-1 bg-gray-200 dark:bg-gray-600 rounded"></div>
            </div>
          }
        />
        
        <LayoutCard
          title="Landing Page"
          description="Marketing-focused layouts for product pages and conversion optimization."
          icon={<Globe className="w-5 h-5" />}
          features={[
            'Hero sections',
            'Feature highlights',
            'CTA optimization',
            'Social proof',
            'Responsive design'
          ]}
          useCases={['Product pages', 'Marketing sites', 'Portfolio', 'Company pages']}
          preview={
            <div className="space-y-2">
              <div className="h-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded"></div>
              <div className="grid grid-cols-3 gap-1">
                <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-600 rounded"></div>
              </div>
            </div>
          }
        />
      </div>
    </div>
  )
};

export const ResponsiveShowcase: Story = {
  render: () => (
    <div className="p-8 max-w-7xl mx-auto space-y-12">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white">Responsive Layouts</h1>
        <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
          All layout components are built with responsive design principles, automatically adapting to different screen sizes.
        </p>
      </div>
      
      <div className="space-y-12">
        <ResponsivePreview
          title="SaaS Application Shell"
          sizes={[
            { name: 'Mobile', width: '120px', height: '180px' },
            { name: 'Tablet', width: '180px', height: '135px' },
            { name: 'Desktop', width: '240px', height: '135px' }
          ]}
        >
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
            <div className="w-64 bg-white dark:bg-gray-800 shadow-sm">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-500 rounded"></div>
                  <div className="font-semibold text-gray-900 dark:text-white">App Name</div>
                </div>
              </div>
              <nav className="p-4 space-y-2">
                <div className="flex items-center gap-3 px-3 py-2 rounded-lg bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300">
                  <Home className="w-4 h-4" />
                  <span className="text-sm">Dashboard</span>
                </div>
                <div className="flex items-center gap-3 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300">
                  <Users className="w-4 h-4" />
                  <span className="text-sm">Users</span>
                </div>
                <div className="flex items-center gap-3 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300">
                  <Settings className="w-4 h-4" />
                  <span className="text-sm">Settings</span>
                </div>
              </nav>
            </div>
            <div className="flex-1 flex flex-col">
              <header className="bg-white dark:bg-gray-800 shadow-sm p-4">
                <div className="flex items-center justify-between">
                  <h1 className="text-xl font-semibold text-gray-900 dark:text-white">Dashboard</h1>
                  <div className="flex items-center gap-2">
                    <button className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700">
                      <Bell className="w-4 h-4" />
                    </button>
                    <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                  </div>
                </div>
              </header>
              <main className="flex-1 p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                        <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Users</p>
                        <p className="text-2xl font-semibold text-gray-900 dark:text-white">1,234</p>
                      </div>
                    </div>
                  </div>
                </div>
              </main>
            </div>
          </div>
        </ResponsivePreview>
        
        <ResponsivePreview
          title="E-commerce Layout"
          sizes={[
            { name: 'Mobile', width: '120px', height: '180px' },
            { name: 'Tablet', width: '180px', height: '135px' },
            { name: 'Desktop', width: '240px', height: '135px' }
          ]}
        >
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <header className="bg-white dark:bg-gray-800 shadow-sm p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="text-xl font-bold text-gray-900 dark:text-white">Store</div>
                  <nav className="hidden md:flex items-center gap-4">
                    <a href="#" className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Electronics</a>
                    <a href="#" className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Clothing</a>
                    <a href="#" className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">Home</a>
                  </nav>
                </div>
                <div className="flex items-center gap-2">
                  <button className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700">
                    <Search className="w-4 h-4" />
                  </button>
                  <button className="relative p-2 rounded-lg bg-gray-100 dark:bg-gray-700">
                    <ShoppingCart className="w-4 h-4" />
                    <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                  </button>
                </div>
              </div>
            </header>
            <main className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[1, 2, 3, 4].map((item) => (
                  <div key={item} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                    <div className="aspect-square bg-gray-200 dark:bg-gray-700 rounded-lg mb-3"></div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">Product {item}</h3>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">$99.99</p>
                    <button className="w-full mt-2 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                      Add to Cart
                    </button>
                  </div>
                ))}
              </div>
            </main>
          </div>
        </ResponsivePreview>
        
        <ResponsivePreview
          title="Analytics Dashboard"
          sizes={[
            { name: 'Mobile', width: '120px', height: '180px' },
            { name: 'Tablet', width: '180px', height: '135px' },
            { name: 'Desktop', width: '240px', height: '135px' }
          ]}
        >
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Analytics</h1>
              <div className="flex items-center gap-2 mt-2">
                <button className="px-3 py-1 bg-blue-500 text-white rounded">7d</button>
                <button className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded">30d</button>
                <button className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded">3m</button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <Users className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Users</p>
                    <p className="text-xl font-semibold text-gray-900 dark:text-white">12,345</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                    <DollarSign className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Revenue</p>
                    <p className="text-xl font-semibold text-gray-900 dark:text-white">$45,678</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">User Growth</h3>
              <div className="h-64 bg-gray-100 dark:bg-gray-700 rounded flex items-end justify-center gap-1 p-4">
                <div className="w-8 h-12 bg-blue-500 rounded-t"></div>
                <div className="w-8 h-16 bg-blue-500 rounded-t"></div>
                <div className="w-8 h-20 bg-blue-500 rounded-t"></div>
                <div className="w-8 h-24 bg-blue-500 rounded-t"></div>
                <div className="w-8 h-28 bg-blue-500 rounded-t"></div>
                <div className="w-8 h-32 bg-blue-500 rounded-t"></div>
              </div>
            </div>
          </div>
        </ResponsivePreview>
      </div>
    </div>
  )
};

export const UsageGuide: Story = {
  render: () => (
    <div className="p-8 max-w-4xl mx-auto space-y-12">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white">Layout Usage Guide</h1>
        <p className="text-xl text-gray-600 dark:text-gray-400">
          Best practices and guidelines for using layout components effectively
        </p>
      </div>
      
      <div className="space-y-8">
        <section>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Choosing the Right Layout</h2>
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">🏢 Business Applications</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-3">
                For admin panels, dashboards, and internal tools, use the SaaS App Shell with appropriate dashboard layouts.
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <strong>Recommended:</strong> SaaS App Shell + Admin Dashboard + Analytics Dashboard
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">🛒 E-commerce</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-3">
                For online stores and marketplaces, use the E-commerce Layout with product-focused components.
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <strong>Recommended:</strong> E-commerce Layout + Product Grids + Shopping Cart
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">🤖 AI Applications</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-3">
                For AI-powered applications and chatbots, use the AI Assistant Chat with appropriate AI components.
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <strong>Recommended:</strong> AI Assistant Chat + AI Message Components + Smart Features
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">👥 Collaboration</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-3">
                For team communication and project management, use the Team Chat layout with collaboration features.
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <strong>Recommended:</strong> Team Chat + Channel Management + File Sharing
              </div>
            </div>
          </div>
        </section>
        
        <section>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Implementation Tips</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">✅ Do</h3>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Use consistent spacing and typography</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Implement proper loading states</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Test responsive behavior on all devices</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Use semantic HTML for accessibility</span>
                </li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">❌ Don't</h3>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Mix different layout patterns in one view</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Override default responsive behavior</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Ignore keyboard navigation</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">Use fixed pixel values for breakpoints</span>
                </li>
              </ul>
            </div>
          </div>
        </section>
        
        <section>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Performance Considerations</h2>
          <div className="bg-blue-50 dark:bg-blue-900 p-6 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Zap className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Fast Loading</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Layouts are optimized for minimal bundle size and fast initial rendering</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Smartphone className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Mobile First</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">All layouts are designed mobile-first for optimal performance on all devices</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Activity className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Smooth Interactions</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Animations and transitions are optimized for 60fps performance</p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
};