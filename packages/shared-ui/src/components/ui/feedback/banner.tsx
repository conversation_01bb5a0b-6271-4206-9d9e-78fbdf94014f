import React from 'react';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface BannerProps {
  children: React.ReactNode;
  variant?: 'info' | 'success' | 'warning' | 'error' | 'neutral';
  size?: ComponentSize | 'xs' | 'sm' | 'md' | 'lg';
  closable?: boolean;
  onClose?: () => void;
  className?: string;
}

export function LuminarBanner({ 
  children, 
  variant = 'info', 
  size = 'md', 
  closable = false, 
  onClose,
  className = '' 
}: BannerProps) {
  const sizeClasses = {
    xs: 'px-2 py-1.5 text-xs',
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-6 py-4 text-lg'
  };

  const variantClasses = {
    info: 'bg-blue-50/80 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100 border-blue-200/60 dark:border-blue-700/60',
    success: 'bg-green-50/80 dark:bg-green-900/20 text-green-900 dark:text-green-100 border-green-200/60 dark:border-green-700/60',
    warning: 'bg-yellow-50/80 dark:bg-yellow-900/20 text-yellow-900 dark:text-yellow-100 border-yellow-200/60 dark:border-yellow-700/60',
    error: 'bg-red-50/80 dark:bg-red-900/20 text-red-900 dark:text-red-100 border-red-200/60 dark:border-red-700/60',
    neutral: 'bg-gray-50/80 dark:bg-gray-900/20 text-foreground border-border'
  };

  const glassClasses = getAdaptiveGlassClasses('card', 'md', variant === 'neutral' ? undefined : variant);

  return (
    <div className={`${glassClasses} ${sizeClasses[size]} ${variantClasses[variant]} rounded-lg border flex items-center justify-between ${className}`}>
      <div className="flex-1">
        {children}
      </div>
      {closable && (
        <button
          onClick={onClose}
          className="ml-4 p-1 hover:bg-black/5 dark:hover:bg-white/5 rounded-full transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  );
}