import React from 'react';
import { motion } from 'framer-motion';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface GaugeChartProps {
  value: number;
  min?: number;
  max?: number;
  label?: string;
  unit?: string;
  size?: number;
  segments?: Array<{
    value: number;
    color: string;
    label?: string;
  }>;
  showValue?: boolean;
  showLabels?: boolean;
  animate?: boolean;
  animationDuration?: number;
  className?: string;
}

export function LuminarGaugeChart({
  value,
  min = 0,
  max = 100,
  label = '',
  unit = '',
  size = 200,
  segments = [
    { value: 33, color: '#ef4444', label: 'Low' },
    { value: 66, color: '#f59e0b', label: 'Medium' },
    { value: 100, color: '#10b981', label: 'High' }
  ],
  showValue = true,
  showLabels = true,
  animate = true,
  animationDuration = 1.5,
  className = ''
}: GaugeChartProps) {
  const glassClasses = getAdaptiveGlassClasses('card', 'md', 'neutral');

  // Normalize value to 0-100 range
  const normalizedValue = ((value - min) / (max - min)) * 100;
  
  // Gauge dimensions
  const radius = size / 2 - 20;
  const centerX = size / 2;
  const centerY = size / 2;
  const startAngle = -135; // Start from bottom left
  const endAngle = 135; // End at bottom right
  const angleRange = endAngle - startAngle;

  // Calculate value angle
  const valueAngle = startAngle + (normalizedValue / 100) * angleRange;

  // Helper to convert angle to radians
  const toRadians = (angle: number) => (angle * Math.PI) / 180;

  // Helper to get point on arc
  const getArcPoint = (angle: number, r: number = radius) => {
    const angleRad = toRadians(angle);
    return {
      x: centerX + r * Math.cos(angleRad),
      y: centerY + r * Math.sin(angleRad)
    };
  };

  // Create arc path
  const createArc = (startAngle: number, endAngle: number, r: number = radius) => {
    const start = getArcPoint(startAngle, r);
    const end = getArcPoint(endAngle, r);
    const largeArc = endAngle - startAngle > 180 ? 1 : 0;
    
    return `M ${start.x} ${start.y} A ${r} ${r} 0 ${largeArc} 1 ${end.x} ${end.y}`;
  };

  // Get current segment color
  const getCurrentColor = () => {
    for (const segment of segments) {
      if (normalizedValue <= segment.value) {
        return segment.color;
      }
    }
    return segments[segments.length - 1].color;
  };

  const currentColor = getCurrentColor();

  return (
    <div className={`${glassClasses} p-4 rounded-lg border ${className}`}>
      <div className="flex flex-col items-center">
        <svg width={size} height={size}>
          <defs>
            <linearGradient id="gaugeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor={currentColor} stopOpacity="0.2" />
              <stop offset="100%" stopColor={currentColor} stopOpacity="0.8" />
            </linearGradient>
            <filter id="gaugeShadow">
              <feDropShadow dx="0" dy="2" stdDeviation="3" floodOpacity="0.1"/>
            </filter>
          </defs>

          {/* Background segments */}
          {segments.map((segment, index) => {
            const segmentStartAngle = index === 0 ? startAngle : 
              startAngle + (segments[index - 1].value / 100) * angleRange;
            const segmentEndAngle = startAngle + (segment.value / 100) * angleRange;
            
            return (
              <motion.path
                key={index}
                d={createArc(segmentStartAngle, segmentEndAngle, radius - 10)}
                fill="none"
                stroke={segment.color}
                strokeWidth="20"
                opacity="0.2"
                strokeLinecap="round"
                initial={{ pathLength: 0 }}
                animate={{ pathLength: 1 }}
                transition={{
                  duration: animationDuration * 0.5,
                  delay: index * 0.1,
                  ease: "easeOut"
                }}
              />
            );
          })}

          {/* Value arc */}
          <motion.path
            d={createArc(startAngle, valueAngle, radius - 10)}
            fill="none"
            stroke="url(#gaugeGradient)"
            strokeWidth="20"
            strokeLinecap="round"
            filter="url(#gaugeShadow)"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{
              duration: animationDuration,
              ease: [0.16, 1, 0.3, 1]
            }}
          />

          {/* Needle */}
          <g>
            <motion.line
              x1={centerX}
              y1={centerY}
              x2={getArcPoint(valueAngle, radius - 25).x}
              y2={getArcPoint(valueAngle, radius - 25).y}
              stroke={currentColor}
              strokeWidth="3"
              strokeLinecap="round"
              initial={{ rotate: startAngle }}
              animate={{ rotate: valueAngle }}
              transition={{
                duration: animationDuration,
                ease: [0.16, 1, 0.3, 1]
              }}
              style={{ transformOrigin: `${centerX}px ${centerY}px` }}
            />
            <motion.circle
              cx={centerX}
              cy={centerY}
              r="8"
              fill={currentColor}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{
                delay: animationDuration * 0.8,
                type: "spring",
                stiffness: 300,
                damping: 10
              }}
            />
          </g>

          {/* Tick marks */}
          {[0, 25, 50, 75, 100].map((tick) => {
            const tickAngle = startAngle + (tick / 100) * angleRange;
            const innerPoint = getArcPoint(tickAngle, radius - 35);
            const outerPoint = getArcPoint(tickAngle, radius - 25);
            
            return (
              <motion.g key={tick}>
                <line
                  x1={innerPoint.x}
                  y1={innerPoint.y}
                  x2={outerPoint.x}
                  y2={outerPoint.y}
                  stroke="var(--muted-foreground)"
                  strokeWidth="2"
                  opacity="0.5"
                />
                {showLabels && (
                  <motion.text
                    x={getArcPoint(tickAngle, radius - 45).x}
                    y={getArcPoint(tickAngle, radius - 45).y}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    fontSize="10"
                    fill="currentColor"
                    className="text-muted-foreground"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: tick * 0.01 + animationDuration }}
                  >
                    {Math.round(min + (tick / 100) * (max - min))}
                  </motion.text>
                )}
              </motion.g>
            );
          })}

          {/* Center value display */}
          {showValue && (
            <motion.g
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: animationDuration * 0.8, duration: 0.3 }}
            >
              <text
                x={centerX}
                y={centerY + 20}
                textAnchor="middle"
                fontSize="28"
                fontWeight="bold"
                fill="currentColor"
                className="text-foreground"
              >
                {value.toFixed(1)}
              </text>
              {unit && (
                <text
                  x={centerX}
                  y={centerY + 40}
                  textAnchor="middle"
                  fontSize="14"
                  fill="currentColor"
                  className="text-muted-foreground"
                >
                  {unit}
                </text>
              )}
            </motion.g>
          )}
        </svg>

        {/* Label */}
        {label && (
          <motion.div
            className="mt-4 text-center"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: animationDuration }}
          >
            <h3 className="text-lg font-medium text-foreground">{label}</h3>
          </motion.div>
        )}

        {/* Segment labels */}
        {showLabels && segments.length > 0 && (
          <motion.div
            className="mt-2 flex gap-4 text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: animationDuration + 0.2 }}
          >
            {segments.map((segment, index) => (
              <div key={index} className="flex items-center gap-1">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: segment.color }}
                />
                <span className="text-muted-foreground">{segment.label}</span>
              </div>
            ))}
          </motion.div>
        )}
      </div>
    </div>
  );
}