import { motion, MotionProps } from "framer-motion"
import { LucideIcon } from "lucide-react"
import { cn } from '../../../lib/utils'
import { forwardRef, ButtonHTMLAttributes } from "react"
import { LuminarIcon, AnimationType } from "../utilities/icon"
import { createGlassStyles } from '../../../lib/glass-utils'
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface LuminarIconButtonProps {
  icon: LucideIcon
  variant?: ComponentVariant | "ghost" | "outline" | "glass"
  size?: ComponentSize | "md" | "lg"
  animation?: AnimationType
  iconSize?: number
  label?: string
  labelPosition?: "left" | "right"
  className?: string
  disabled?: boolean
  onClick?: () => void
}

const LuminarIconButton = forwardRef<HTMLButtonElement, LuminarIconButtonProps>(
  ({ 
    icon,
    variant = defaultComponentProps.variant,
    size = defaultComponentProps.size,
    animation = "none",
    iconSize,
    label,
    labelPosition = "right",
    className,
    disabled,
    onClick
  }, ref) => {
    const sizeClasses = {
      xs: "h-6 px-2 text-xs gap-1",
      sm: "h-8 px-3 text-sm gap-1.5",
      md: "h-10 px-4 text-base gap-2",
      lg: "h-12 px-6 text-lg gap-2.5",
    }

    const iconSizes = {
      xs: iconSize || 12,
      sm: iconSize || 16,
      md: iconSize || 20,
      lg: iconSize || 24,
    }

    const variantClasses = {
      default: "bg-primary text-primary-foreground hover:bg-primary/90",
      ghost: "hover:bg-accent hover:text-accent-foreground",
      outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
      glass: createGlassStyles({ element: 'button' }),
    }

    return (
      <motion.button
        ref={ref}
        className={cn(
          "inline-flex items-center justify-center rounded-lg font-medium transition-colors",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          "disabled:pointer-events-none disabled:opacity-50",
          sizeClasses[size],
          variantClasses[variant],
          className
        )}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
        disabled={disabled}
        onClick={onClick}
      >
        {label && labelPosition === "left" && (
          <span>{label}</span>
        )}
        <LuminarIcon
          icon={icon}
          size={iconSizes[size]}
          animation={animation}
          trigger="hover"
        />
        {label && labelPosition === "right" && (
          <span>{label}</span>
        )}
      </motion.button>
    )
  }
)
LuminarIconButton.displayName = "LuminarIconButton"

export { LuminarIconButton }