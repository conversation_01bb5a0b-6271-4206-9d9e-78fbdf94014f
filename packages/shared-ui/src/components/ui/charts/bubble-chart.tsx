import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface BubbleChartData {
  x: number;
  y: number;
  z: number; // size/value
  label: string;
  color?: string;
  category?: string;
}

export interface AnimatedBubbleChartProps {
  data: BubbleChartData[];
  width?: number;
  height?: number;
  xLabel?: string;
  yLabel?: string;
  zLabel?: string;
  showGrid?: boolean;
  showLabels?: boolean;
  maxBubbleSize?: number;
  animate?: boolean;
  animationDuration?: number;
  bubbleStyle?: 'solid' | 'gradient' | 'glass';
  className?: string;
}

export function LuminarAnimatedBubbleChart({
  data,
  width = 600,
  height = 400,
  xLabel = 'X Axis',
  yLabel = 'Y Axis',
  zLabel = 'Size',
  showGrid = true,
  showLabels = false,
  maxBubbleSize = 40,
  animate = true,
  animationDuration = 2,
  bubbleStyle = 'gradient',
  className = ''
}: AnimatedBubbleChartProps) {
  const [hoveredBubble, setHoveredBubble] = useState<number | null>(null);
  const [selectedBubble, setSelectedBubble] = useState<number | null>(null);
  const glassClasses = getAdaptiveGlassClasses('card', 'md', 'neutral');

  // Chart dimensions
  const padding = { top: 20, right: 20, bottom: 60, left: 60 };
  const chartWidth = width - padding.left - padding.right;
  const chartHeight = height - padding.top - padding.bottom;

  // Calculate scales
  const xValues = data.map(d => d.x);
  const yValues = data.map(d => d.y);
  const zValues = data.map(d => d.z);
  
  const xMin = Math.min(...xValues);
  const xMax = Math.max(...xValues);
  const yMin = Math.min(...yValues);
  const yMax = Math.max(...yValues);
  const zMin = Math.min(...zValues);
  const zMax = Math.max(...zValues);

  const xScale = (value: number) => ((value - xMin) / (xMax - xMin)) * chartWidth;
  const yScale = (value: number) => chartHeight - ((value - yMin) / (yMax - yMin)) * chartHeight;
  const zScale = (value: number) => (Math.sqrt(value / zMax) * maxBubbleSize) + 5;

  // Default colors
  const defaultColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#ec4899'];
  const categoryColors: { [key: string]: string } = {};
  let colorIndex = 0;

  data.forEach(bubble => {
    if (bubble.category && !categoryColors[bubble.category]) {
      categoryColors[bubble.category] = defaultColors[colorIndex % defaultColors.length];
      colorIndex++;
    }
  });

  // Sort bubbles by size (largest first) to prevent overlap issues
  const sortedData = [...data].sort((a, b) => b.z - a.z);

  return (
    <div className={`${glassClasses} p-4 rounded-lg border ${className}`}>
      <svg width={width} height={height}>
        <defs>
          {sortedData.map((_, index) => (
            <radialGradient key={`gradient-${index}`} id={`bubbleGradient-${index}`}>
              <stop offset="0%" stopColor="white" stopOpacity="0.3" />
              <stop offset="100%" stopColor="white" stopOpacity="0" />
            </radialGradient>
          ))}
          <filter id="bubbleShadow">
            <feDropShadow dx="0" dy="2" stdDeviation="3" floodOpacity="0.2"/>
          </filter>
          <filter id="bubbleGlow">
            <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        <g transform={`translate(${padding.left},${padding.top})`}>
          {/* Grid */}
          {showGrid && (
            <g className="grid">
              {/* Grid lines similar to scatter plot */}
              {[0, 0.25, 0.5, 0.75, 1].map((tick, i) => {
                const x = tick * chartWidth;
                const y = chartHeight - tick * chartHeight;
                const xValue = xMin + tick * (xMax - xMin);
                const yValue = yMin + tick * (yMax - yMin);
                
                return (
                  <g key={i}>
                    <motion.line
                      x1={x}
                      y1={0}
                      x2={x}
                      y2={chartHeight}
                      stroke="var(--border)"
                      strokeWidth="1"
                      opacity="0.3"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 0.3 }}
                      transition={{ delay: i * 0.05 }}
                    />
                    <motion.line
                      x1={0}
                      y1={y}
                      x2={chartWidth}
                      y2={y}
                      stroke="var(--border)"
                      strokeWidth="1"
                      opacity="0.3"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 0.3 }}
                      transition={{ delay: i * 0.05 }}
                    />
                  </g>
                );
              })}
            </g>
          )}

          {/* Bubbles */}
          {sortedData.map((bubble, index) => {
            const originalIndex = data.indexOf(bubble);
            const x = xScale(bubble.x);
            const y = yScale(bubble.y);
            const r = zScale(bubble.z);
            const color = bubble.color || (bubble.category ? categoryColors[bubble.category] : defaultColors[index % defaultColors.length]);
            const hovered = hoveredBubble === originalIndex;
            const isSelected = selectedBubble === originalIndex;

            return (
              <motion.g key={originalIndex}>
                {/* Shadow/base layer for glass effect */}
                {bubbleStyle === 'glass' && (
                  <motion.circle
                    cx={x}
                    cy={y}
                    r={r}
                    fill={color}
                    opacity="0.2"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{
                      delay: (index / sortedData.length) * animationDuration * 0.5,
                      duration: 0.5,
                      type: "spring",
                      stiffness: 100,
                      damping: 15
                    }}
                  />
                )}

                {/* Main bubble */}
                <motion.circle
                  cx={x}
                  cy={y}
                  r={r}
                  fill={bubbleStyle === 'gradient' ? color : bubbleStyle === 'glass' ? 'transparent' : color}
                  fillOpacity={bubbleStyle === 'glass' ? 0 : 0.8}
                  stroke={bubbleStyle === 'glass' ? color : 'none'}
                  strokeWidth={bubbleStyle === 'glass' ? 2 : 0}
                  initial={{ scale: 0 }}
                  animate={{ 
                    scale: hovered ? 1.1 : isSelected ? 1.15 : 1,
                    filter: hovered || isSelected ? 'url(#bubbleGlow)' : 'url(#bubbleShadow)'
                  }}
                  transition={{
                    scale: {
                      delay: (index / sortedData.length) * animationDuration * 0.5,
                      duration: 0.5,
                      type: "spring",
                      stiffness: 100,
                      damping: 15
                    },
                    filter: {
                      duration: 0.3
                    }
                  }}
                  onMouseEnter={() => setHoveredBubble(originalIndex)}
                  onMouseLeave={() => setHoveredBubble(null)}
                  onClick={() => setSelectedBubble(isSelected ? null : originalIndex)}
                  className="cursor-pointer"
                />

                {/* Gradient overlay for depth */}
                {bubbleStyle === 'gradient' && (
                  <motion.circle
                    cx={x}
                    cy={y}
                    r={r}
                    fill={`url(#bubbleGradient-${index})`}
                    pointerEvents="none"
                    initial={{ scale: 0 }}
                    animate={{ scale: hovered ? 1.1 : isSelected ? 1.15 : 1 }}
                    transition={{
                      scale: {
                        delay: (index / sortedData.length) * animationDuration * 0.5,
                        duration: 0.5,
                        type: "spring",
                        stiffness: 100,
                        damping: 15
                      }
                    }}
                  />
                )}

                {/* Label inside bubble */}
                {showLabels && r > 20 && (
                  <motion.text
                    x={x}
                    y={y}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    fontSize={Math.min(r / 3, 12)}
                    fill={bubbleStyle === 'glass' ? 'currentColor' : 'white'}
                    className="font-medium pointer-events-none"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{
                      delay: (index / sortedData.length) * animationDuration * 0.5 + 0.3,
                      duration: 0.3
                    }}
                  >
                    {bubble.label}
                  </motion.text>
                )}
              </motion.g>
            );
          })}

          {/* Axis labels */}
          <motion.text
            x={chartWidth / 2}
            y={chartHeight + 45}
            textAnchor="middle"
            fontSize="12"
            fill="currentColor"
            className="text-foreground font-medium"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: animationDuration }}
          >
            {xLabel}
          </motion.text>

          <motion.text
            x={-chartHeight / 2}
            y={-40}
            textAnchor="middle"
            fontSize="12"
            fill="currentColor"
            className="text-foreground font-medium"
            transform="rotate(-90)"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: animationDuration }}
          >
            {yLabel}
          </motion.text>
        </g>
      </svg>

      {/* Info panel */}
      <AnimatePresence>
        {(hoveredBubble !== null || selectedBubble !== null) && (
          <motion.div
            className="absolute top-4 right-4 bg-popover text-popover-foreground p-3 rounded-md shadow-lg border max-w-xs"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.2 }}
          >
            {(() => {
              const bubble = data[hoveredBubble !== null ? hoveredBubble : selectedBubble!];
              return (
                <>
                  <div className="font-semibold text-lg mb-2">{bubble.label}</div>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">{xLabel}:</span>
                      <span className="font-medium">{bubble.x.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">{yLabel}:</span>
                      <span className="font-medium">{bubble.y.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">{zLabel}:</span>
                      <span className="font-medium">{bubble.z.toFixed(2)}</span>
                    </div>
                    {bubble.category && (
                      <div className="pt-2 border-t">
                        <span className="text-muted-foreground">Category: </span>
                        <span className="font-medium">{bubble.category}</span>
                      </div>
                    )}
                  </div>
                </>
              );
            })()}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Alias for backward compatibility
export const LuminarBubbleChart = LuminarAnimatedBubbleChart;