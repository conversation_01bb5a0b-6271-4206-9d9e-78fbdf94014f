import React from 'react';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface HeatmapData {
  x: number;
  y: number;
  value: number;
  label?: string;
}

export interface HeatmapProps {
  data: HeatmapData[];
  width?: number;
  height?: number;
  cellSize?: number;
  colorScheme?: 'blue' | 'green' | 'red' | 'purple' | 'orange';
  showLabels?: boolean;
  showValues?: boolean;
  onCellClick?: (cell: HeatmapData) => void;
  className?: string;
}

export function LuminarHeatmap({
  data,
  width = 400,
  height = 300,
  cellSize = 20,
  colorScheme = 'blue',
  showLabels = false,
  showValues = false,
  onCellClick,
  className = ''
}: HeatmapProps) {
  const glassClasses = getAdaptiveGlassClasses('card', 'md', 'neutral');

  // Calculate min and max values for normalization
  const values = data.map(d => d.value);
  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);

  // Color schemes
  const colorSchemes = {
    blue: {
      low: 'rgba(59, 130, 246, 0.1)',
      medium: 'rgba(59, 130, 246, 0.5)',
      high: 'rgba(59, 130, 246, 0.9)'
    },
    green: {
      low: 'rgba(16, 185, 129, 0.1)',
      medium: 'rgba(16, 185, 129, 0.5)',
      high: 'rgba(16, 185, 129, 0.9)'
    },
    red: {
      low: 'rgba(239, 68, 68, 0.1)',
      medium: 'rgba(239, 68, 68, 0.5)',
      high: 'rgba(239, 68, 68, 0.9)'
    },
    purple: {
      low: 'rgba(139, 92, 246, 0.1)',
      medium: 'rgba(139, 92, 246, 0.5)',
      high: 'rgba(139, 92, 246, 0.9)'
    },
    orange: {
      low: 'rgba(245, 158, 11, 0.1)',
      medium: 'rgba(245, 158, 11, 0.5)',
      high: 'rgba(245, 158, 11, 0.9)'
    }
  };

  // Get color based on value
  const getColor = (value: number) => {
    const normalized = (value - minValue) / (maxValue - minValue);
    const scheme = colorSchemes[colorScheme];
    
    if (normalized < 0.33) return scheme.low;
    if (normalized < 0.66) return scheme.medium;
    return scheme.high;
  };

  // Get unique x and y values for grid
  const xValues = [...new Set(data.map(d => d.x))].sort((a, b) => a - b);
  const yValues = [...new Set(data.map(d => d.y))].sort((a, b) => a - b);

  return (
    <div className={`${glassClasses} p-4 rounded-lg border ${className}`}>
      <div className="overflow-auto">
        <svg width={width} height={height}>
          {data.map((cell, index) => (
            <g key={`${cell.x}-${cell.y}`}>
              <rect
                x={cell.x * cellSize}
                y={cell.y * cellSize}
                width={cellSize}
                height={cellSize}
                fill={getColor(cell.value)}
                stroke="var(--border)"
                strokeWidth="0.5"
                className="cursor-pointer hover:stroke-foreground"
                onClick={() => onCellClick?.(cell)}
              />
              {showValues && (
                <text
                  x={cell.x * cellSize + cellSize / 2}
                  y={cell.y * cellSize + cellSize / 2}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  fontSize="10"
                  fill="currentColor"
                  className="text-foreground pointer-events-none"
                >
                  {cell.value.toFixed(1)}
                </text>
              )}
            </g>
          ))}
        </svg>
      </div>
      
      {/* Legend */}
      <div className="mt-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <span className="text-sm text-muted-foreground">Low</span>
          <div className="flex gap-1">
            {[0.1, 0.3, 0.5, 0.7, 0.9].map((opacity, i) => (
              <div
                key={i}
                className="w-4 h-4 rounded-sm"
                style={{ backgroundColor: colorSchemes[colorScheme].high.replace('0.9', opacity.toString()) }}
              />
            ))}
          </div>
          <span className="text-sm text-muted-foreground">High</span>
        </div>
        
        <div className="text-sm text-muted-foreground">
          Range: {minValue.toFixed(1)} - {maxValue.toFixed(1)}
        </div>
      </div>
    </div>
  );
}