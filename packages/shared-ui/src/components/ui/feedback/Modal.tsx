import * as React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { cn } from '../../../lib/utils';
import { createGlassStyles } from '../../lib/glass-utils';
import { Button } from '../../components/ui/actions/button';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface ModalProps {
  open: boolean;
  onClose: () => void;
  children?: React.ReactNode;
  title?: string;
  description?: string;
  size?: ComponentSize | 'md' | 'lg' | 'xl' | 'full';
  closeOnBackdropClick?: boolean;
  showCloseButton?: boolean;
  className?: string;
  contentClassName?: string;
  glassIntensity?: 'soft' | 'standard' | 'hard' | 'intense';
  decorative?: boolean;
}

const sizeClasses = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  full: 'max-w-[95vw] max-h-[95vh]'
};

const Modal = React.forwardRef<HTMLDivElement, ModalProps>(
  ({
    children,
    open,
    onClose,
    title,
    description,
    size = 'md',
    closeOnBackdropClick = true,
    showCloseButton = true,
    className,
    contentClassName,
    glassIntensity = 'intense',
    decorative = true,
    ...props
  }, ref) => {
    const glassClasses = createGlassStyles({
      element: 'modal',
      profile: glassIntensity,
      interactive: false,
      frost: true,
      glow: true
    });

    return (
      <AnimatePresence>
        {open && (
          <motion.div
            className={cn(
              'fixed inset-0 z-50 flex items-center justify-center p-4',
              className
            )}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.15 }}
          >
            {/* Enhanced Backdrop */}
            <motion.div
              className="absolute inset-0 bg-black/60 backdrop-blur-md"
              initial={{ opacity: 0, backdropFilter: 'blur(0px)' }}
              animate={{ opacity: 1, backdropFilter: 'blur(12px)' }}
              exit={{ opacity: 0, backdropFilter: 'blur(0px)' }}
              transition={{ duration: 0.3 }}
              onClick={closeOnBackdropClick ? onClose : undefined}
            />

            {/* Modal Content with 3D Transform */}
            <motion.div
              ref={ref}
              className={cn(
                'relative z-50 w-full',
                sizeClasses[size],
                glassClasses,
                'rounded-xl shadow-2xl border border-white/20 dark:border-white/10',
                contentClassName
              )}
              initial={{
                opacity: 0,
                scale: 0.9,
                rotateX: -15,
                y: 60
              }}
              animate={{
                opacity: 1,
                scale: 1,
                rotateX: 0,
                y: 0
              }}
              exit={{
                opacity: 0,
                scale: 0.95,
                rotateX: 15,
                y: 30
              }}
              transition={{
                type: 'spring',
                stiffness: 300,
                damping: 25,
                opacity: { duration: 0.2 }
              }}
              onClick={(e) => e.stopPropagation()}
              style={{ perspective: '1000px' }}
              {...props}
            >
              {/* Decorative Elements */}
              {decorative && (
                <>
                  {/* Top Gradient Border */}
                  <motion.div
                    className="absolute top-0 left-1/2 -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-transparent via-blue-400/80 to-transparent rounded-full"
                    initial={{ width: 0, opacity: 0 }}
                    animate={{ width: 128, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.6 }}
                  />
                  
                  {/* Corner Accents */}
                  <div className="absolute top-2 right-2 w-2 h-2 bg-blue-400/60 rounded-full animate-pulse" />
                  <div className="absolute bottom-2 left-2 w-2 h-2 bg-purple-400/60 rounded-full animate-pulse" />
                </>
              )}

              {/* Header Section */}
              {(title || description || showCloseButton) && (
                <motion.div
                  className="flex items-start justify-between p-6 pb-4 border-b border-white/10 dark:border-white/5"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <div className="flex-1">
                    {title && (
                      <motion.h2
                        className="text-xl font-semibold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-200 bg-clip-text text-transparent"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.2 }}
                      >
                        {title}
                      </motion.h2>
                    )}
                    {description && (
                      <motion.p
                        className="mt-2 text-sm text-gray-600 dark:text-gray-400"
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.25 }}
                      >
                        {description}
                      </motion.p>
                    )}
                  </div>
                  
                  {showCloseButton && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.3, type: 'spring', stiffness: 500 }}
                    >
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={onClose}
                        className={cn(
                          createGlassStyles({
                            element: 'button',
                            profile: 'soft',
                            interactive: true
                          }),
                          "hover:bg-red-500/20 hover:text-red-600 dark:hover:text-red-400"
                        )}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </motion.div>
                  )}
                </motion.div>
              )}

              {/* Content Area */}
              <motion.div
                className="p-6"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.15 }}
              >
                {children}
              </motion.div>

              {/* Subtle Inner Glow */}
              <motion.div
                className="absolute inset-0 rounded-xl bg-gradient-to-br from-blue-400/5 via-transparent to-purple-400/5 pointer-events-none"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4, duration: 1 }}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }
);
Modal.displayName = 'Modal';

export { Modal };
