import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

interface WebGPURendererProps {
  width?: number;
  height?: number;
  effectType?: 'raytracing' | 'particles' | 'fluid' | 'volumetric';
  intensity?: number;
  className?: string;
}

export const WebGPURenderer: React.FC<WebGPURendererProps> = ({
  width = 400,
  height = 300,
  effectType = 'raytracing',
  intensity = 0.5,
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isSupported, setIsSupported] = useState<boolean | null>(null);
  const [device, setDevice] = useState<GPUDevice | null>(null);

  useEffect(() => {
    const initWebGPU = async () => {
      // Check if WebGPU is supported
      if (!('gpu' in navigator)) {
        setIsSupported(false);
        return;
      }

      const gpu = (navigator as any).gpu;
      if (!gpu) {
        setIsSupported(false);
        return;
      }

      try {
        const adapter = await gpu.requestAdapter();
        if (!adapter) {
          setIsSupported(false);
          return;
        }

        const device = await adapter.requestDevice();
        setDevice(device);
        setIsSupported(true);

        const canvas = canvasRef.current;
        if (!canvas) return;

        const context = canvas.getContext('webgpu');
        if (!context) return;

        const format = gpu.getPreferredCanvasFormat();
        context.configure({
          device,
          format,
        });

        // Render based on effect type
        renderEffect(device, context, effectType, intensity);
      } catch (error) {
        console.error('WebGPU initialization failed:', error);
        setIsSupported(false);
      }
    };

    initWebGPU();
  }, [effectType, intensity]);

  const renderEffect = (device: GPUDevice, context: GPUCanvasContext, type: string, intensity: number) => {
    const shaderCode = getShaderCode(type, intensity);
    
    const shaderModule = device.createShaderModule({
      code: shaderCode
    });

    const renderPipeline = device.createRenderPipeline({
      layout: 'auto',
      vertex: {
        module: shaderModule,
        entryPoint: 'vs_main',
      },
      fragment: {
        module: shaderModule,
        entryPoint: 'fs_main',
        targets: [{
          format: (navigator as any).gpu?.getPreferredCanvasFormat() || 'bgra8unorm',
        }],
      },
      primitive: {
        topology: 'triangle-list',
      },
    });

    const render = () => {
      const commandEncoder = device.createCommandEncoder();
      const textureView = context.getCurrentTexture().createView();

      const renderPassDescriptor: GPURenderPassDescriptor = {
        colorAttachments: [{
          view: textureView,
          clearValue: { r: 0.0, g: 0.0, b: 0.0, a: 1.0 },
          loadOp: 'clear',
          storeOp: 'store',
        }],
      };

      const passEncoder = commandEncoder.beginRenderPass(renderPassDescriptor);
      passEncoder.setPipeline(renderPipeline);
      passEncoder.draw(6, 1, 0, 0);
      passEncoder.end();

      device.queue.submit([commandEncoder.finish()]);
      requestAnimationFrame(render);
    };

    render();
  };

  const getShaderCode = (type: string, intensity: number) => {
    const baseVertex = `
      @vertex
      fn vs_main(@builtin(vertex_index) vertexIndex: u32) -> @builtin(position) vec4<f32> {
        var pos = array<vec2<f32>, 6>(
          vec2<f32>(-1.0, -1.0),
          vec2<f32>( 1.0, -1.0),
          vec2<f32>(-1.0,  1.0),
          vec2<f32>( 1.0, -1.0),
          vec2<f32>( 1.0,  1.0),
          vec2<f32>(-1.0,  1.0),
        );
        return vec4<f32>(pos[vertexIndex], 0.0, 1.0);
      }
    `;

    const fragmentShaders = {
      raytracing: `
        @fragment
        fn fs_main(@builtin(position) fragCoord: vec4<f32>) -> @location(0) vec4<f32> {
          let uv = fragCoord.xy / 400.0;
          let time = ${Date.now() / 1000.0};
          
          // Simple ray-traced sphere with reflections
          let ro = vec3<f32>(0.0, 0.0, 5.0);
          let rd = normalize(vec3<f32>(uv * 2.0 - 1.0, -1.0));
          
          let sphere_pos = vec3<f32>(0.0, 0.0, 0.0);
          let sphere_radius = 1.0;
          
          let oc = ro - sphere_pos;
          let a = dot(rd, rd);
          let b = 2.0 * dot(oc, rd);
          let c = dot(oc, oc) - sphere_radius * sphere_radius;
          let discriminant = b * b - 4.0 * a * c;
          
          if (discriminant < 0.0) {
            return vec4<f32>(0.0, 0.0, 0.0, 1.0);
          }
          
          let t = (-b - sqrt(discriminant)) / (2.0 * a);
          let hit_point = ro + t * rd;
          let normal = normalize(hit_point - sphere_pos);
          
          // Glass-like material with reflections
          let light_dir = normalize(vec3<f32>(1.0, 1.0, 1.0));
          let diffuse = max(dot(normal, light_dir), 0.0);
          
          let reflection = reflect(-light_dir, normal);
          let specular = pow(max(dot(reflection, -rd), 0.0), 32.0);
          
          let glass_color = vec3<f32>(0.8, 0.9, 1.0);
          let final_color = glass_color * diffuse + specular * ${intensity};
          
          return vec4<f32>(final_color, 0.8);
        }
      `,
      particles: `
        @fragment
        fn fs_main(@builtin(position) fragCoord: vec4<f32>) -> @location(0) vec4<f32> {
          let uv = fragCoord.xy / 400.0;
          let time = ${Date.now() / 1000.0};
          
          var color = vec3<f32>(0.0);
          
          // Particle system simulation
          for (var i = 0; i < 20; i++) {
            let fi = f32(i);
            let particle_pos = vec2<f32>(
              sin(time * 0.5 + fi * 0.3) * 0.3,
              cos(time * 0.7 + fi * 0.5) * 0.3
            ) + 0.5;
            
            let dist = length(uv - particle_pos);
            let particle_size = 0.05 * ${intensity};
            
            if (dist < particle_size) {
              let intensity_val = 1.0 - (dist / particle_size);
              color += vec3<f32>(intensity_val * 0.3, intensity_val * 0.6, intensity_val * 1.0);
            }
          }
          
          return vec4<f32>(color, 1.0);
        }
      `,
      fluid: `
        @fragment
        fn fs_main(@builtin(position) fragCoord: vec4<f32>) -> @location(0) vec4<f32> {
          let uv = fragCoord.xy / 400.0;
          let time = ${Date.now() / 1000.0};
          
          // Fluid simulation using noise
          let p = uv * 8.0;
          var n = 0.0;
          var amplitude = 1.0;
          
          for (var i = 0; i < 4; i++) {
            n += sin(p.x + time) * sin(p.y + time * 0.7) * amplitude;
            p *= 2.0;
            amplitude *= 0.5;
          }
          
          let fluid_color = vec3<f32>(0.2, 0.6, 1.0) * (n * 0.5 + 0.5) * ${intensity};
          return vec4<f32>(fluid_color, 0.9);
        }
      `,
      volumetric: `
        @fragment
        fn fs_main(@builtin(position) fragCoord: vec4<f32>) -> @location(0) vec4<f32> {
          let uv = fragCoord.xy / 400.0;
          let time = ${Date.now() / 1000.0};
          
          // Volumetric light effect
          let center = vec2<f32>(0.5, 0.5);
          let ray_dir = normalize(uv - center);
          let dist_to_center = length(uv - center);
          
          var light_intensity = 0.0;
          let num_samples = 32;
          
          for (var i = 0; i < num_samples; i++) {
            let fi = f32(i) / f32(num_samples);
            let sample_pos = center + ray_dir * fi * dist_to_center;
            
            // Sample noise for volumetric effect
            let noise_val = sin(sample_pos.x * 10.0 + time) * sin(sample_pos.y * 10.0 + time * 0.8);
            light_intensity += max(0.0, noise_val) / f32(num_samples);
          }
          
          let volumetric_color = vec3<f32>(1.0, 0.8, 0.6) * light_intensity * ${intensity};
          return vec4<f32>(volumetric_color, light_intensity);
        }
      `
    };

    return baseVertex + '\n' + fragmentShaders[type as keyof typeof fragmentShaders];
  };

  const fallbackContent = () => (
    <div className="flex items-center justify-center h-full bg-gradient-to-br from-purple-500/20 to-blue-500/20 backdrop-blur-md border border-white/20 rounded-lg">
      <div className="text-center space-y-2">
        <div className="text-sm font-medium">WebGPU Preview</div>
        <div className="text-xs text-muted-foreground">
          {isSupported === false 
            ? 'WebGPU not supported - showing fallback'
            : 'Loading advanced effects...'
          }
        </div>
        <div className="w-16 h-16 mx-auto bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-pulse" />
      </div>
    </div>
  );

  return (
    <motion.div 
      className={`relative ${className}`}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      {isSupported ? (
        <canvas
          ref={canvasRef}
          width={width}
          height={height}
          className="rounded-lg border border-white/20 shadow-2xl"
          style={{ width, height }}
        />
      ) : (
        <div style={{ width, height }}>
          {fallbackContent()}
        </div>
      )}
    </motion.div>
  );
};