import { LuminarButton } from '../../components/ui/actions/button-advanced';
import { LuminarCard } from '../../components/ui/display/card';
import { LogOut, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { animationPresets } from '../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../types/component-props';
import { defaultComponentProps } from '../../types/component-props';

interface SignOutModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  loading?: boolean;
  title?: string;
  description?: string;
}

export function SignOutModal({
  open,
  onClose,
  onConfirm,
  loading = false,
  title = "Sign out",
  description = "Are you sure you want to sign out? You'll need to sign in again to access your account."
}: SignOutModalProps) {
  if (!open) return null;

  return (
    <AnimatePresence>
      {open && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />
          
          {/* Modal */}
          <motion.div
            initial={animationPresets.scale.initial}
            animate={animationPresets.scale.animate}
            exit={animationPresets.scale.exit}
            className="relative z-10 w-full max-w-md mx-4"
          >
            <LuminarCard className="p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <div className="flex items-center justify-center">
                    <LogOut className="h-5 w-5 text-destructive" />
                  </div>
                  <h2 className="text-lg font-semibold">{title}</h2>
                </div>
                <button
                  onClick={onClose}
                  disabled={loading}
                  className="h-8 w-8 rounded-md hover:bg-accent flex items-center justify-center transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              
              {/* Content */}
              <p className="text-muted-foreground mb-6">{description}</p>
              
              {/* Actions */}
              <div className="flex justify-end gap-3">
                <LuminarButton
                  variant="outline"
                  onClick={onClose}
                  disabled={loading}
                >
                  Cancel
                </LuminarButton>
                <LuminarButton
                  variant="destructive"
                  onClick={onConfirm}
                  loading={loading}
                  disabled={loading}
                >
                  {loading ? 'Signing out...' : 'Sign out'}
                </LuminarButton>
              </div>
            </LuminarCard>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}