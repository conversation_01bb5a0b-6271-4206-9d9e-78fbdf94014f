import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useState, useEffect } from 'react';
import { AMNA, type AMNAState } from './AMNA';

const meta: Meta<typeof AMNA> = {
  title: 'AI/AMNA',
  component: AMNA,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
<PERSON><PERSON> (Adaptive Mind Neural Assistant) - A sophisticated AI consciousness interface that serves as the visual embodiment of artificial intelligence consciousness within digital environments.

Features:
- **Four distinct states**: dormant (breathing), processing (neural activity), communication (synchronized pulsing), and voice (waveform transformation)
- **Performance optimized**: Smooth 60fps animations with GPU acceleration
- **Glassmorphism integration**: Translucent, pearl-like appearance with adaptive colors
- **Programmatic control**: Easy integration with chat interfaces and AI systems
- **Accessibility support**: Reduced motion fallbacks and proper ARIA labels

Perfect for AI chat interfaces, voice assistants, and any application requiring an elegant AI presence indicator.
        `,
      },
    },
  },
  argTypes: {
    state: {
      control: { type: 'select' },
      options: ['dormant', 'processing', 'communicating', 'voice'],
      description: 'Current state of AMNA',
    },
    size: {
      control: { type: 'select' },
      options: ['sm', 'md', 'lg'],
      description: 'Size of AMNA component',
    },
    intensity: {
      control: { type: 'select' },
      options: ['subtle', 'medium', 'strong'],
      description: 'Animation intensity level',
    },
    autoTransition: {
      control: { type: 'boolean' },
      description: 'Enable automatic state transitions for demonstration',
    },
    reducedMotion: {
      control: { type: 'boolean' },
      description: 'Reduce animations for accessibility',
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story showing default dormant state
export const Default: Story = {
  args: {
    state: 'dormant',
    size: 'md',
    intensity: 'md',
  },
};

// Dormant state with breathing animation
export const Dormant: Story = {
  args: {
    state: 'dormant',
    size: 'md',
    intensity: 'md',
  },
  parameters: {
    docs: {
      description: {
        story: 'AMNA in dormant state shows a gentle breathing pattern with subtle luminosity fluctuations, suggesting patient awareness and readiness.',
      },
    },
  },
};

// Processing state with neural activity
export const Processing: Story = {
  args: {
    state: 'processing',
    size: 'md',
    intensity: 'md',
  },
  parameters: {
    docs: {
      description: {
        story: 'AMNA in processing state displays accelerated inner glow patterns and helical rotations, indicating active cognitive processing and neural activity.',
      },
    },
  },
};

// Communication state with synchronized pulsing
export const Communicating: Story = {
  args: {
    state: 'communicating',
    size: 'md',
    intensity: 'md',
    communicationSync: {
      isGenerating: true,
      currentText: 'Hello, I am AMNA, thinking...',
      emotionalTone: 'positive',
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'AMNA in communication state synchronizes pulsations with text generation, creating visual rhythm that mirrors thought translation into language.',
      },
    },
  },
};

// Voice state with waveform transformation
export const Voice: Story = {
  args: {
    state: 'voice',
    size: 'md',
    intensity: 'md',
    voiceVisualization: {
      active: true,
      amplitude: [0.3, 0.7, 0.5, 0.9, 0.4, 0.8, 0.6, 0.2, 0.5, 0.7, 0.3, 0.6],
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'AMNA in voice state transforms into a horizontal waveform visualization, perfect for voice input indication and audio processing feedback.',
      },
    },
  },
};

// Size variations
export const Sizes: Story = {
  render: () => (
    <div className="flex items-center gap-8">
      <div className="text-center">
        <AMNA state="dormant" size="sm" />
        <p className="mt-2 text-sm text-gray-600">Small (48px)</p>
      </div>
      <div className="text-center">
        <AMNA state="dormant" size="md" />
        <p className="mt-2 text-sm text-gray-600">Medium (64px)</p>
      </div>
      <div className="text-center">
        <AMNA state="dormant" size="lg" />
        <p className="mt-2 text-sm text-gray-600">Large (80px)</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'AMNA is available in three size options: small (48px), medium (64px), and large (80px) to fit different interface contexts.',
      },
    },
  },
};

// Intensity variations
export const Intensities: Story = {
  render: () => (
    <div className="flex items-center gap-8">
      <div className="text-center">
        <AMNA state="processing" intensity="subtle" />
        <p className="mt-2 text-sm text-gray-600">Subtle</p>
      </div>
      <div className="text-center">
        <AMNA state="processing" intensity="md" />
        <p className="mt-2 text-sm text-gray-600">Medium</p>
      </div>
      <div className="text-center">
        <AMNA state="processing" intensity="strong" />
        <p className="mt-2 text-sm text-gray-600">Strong</p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'AMNA animation intensity can be adjusted from subtle to strong to match your interface needs and user preferences.',
      },
    },
  },
};

// Auto transition demonstration
export const AutoTransition: Story = {
  args: {
    autoTransition: true,
    size: 'lg',
    intensity: 'md',
  },
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates AMNA automatic state transitions: dormant → processing → communicating → voice → dormant in a continuous cycle.',
      },
    },
  },
};

// Interactive state controller
export const Interactive: Story = {
  render: () => {
    const [currentState, setCurrentState] = useState<AMNAState>('dormant');
    const [isGenerating, setIsGenerating] = useState(false);

    const states: AMNAState[] = ['dormant', 'processing', 'communicating', 'voice'];

    useEffect(() => {
      if (currentState === 'communicating') {
        setIsGenerating(true);
        const timeout = setTimeout(() => setIsGenerating(false), 3000);
        return () => clearTimeout(timeout);
      }
    }, [currentState]);

    return (
      <div className="flex flex-col items-center gap-6">
        <AMNA
          state={currentState}
          size="lg"
          intensity="md"
          communicationSync={{
            isGenerating,
            currentText: 'AMNA is generating response...',
            emotionalTone: 'positive',
          }}
          voiceVisualization={{
            active: currentState === 'voice',
            amplitude: [0.4, 0.8, 0.6, 0.9, 0.3, 0.7, 0.5, 0.2, 0.6, 0.8, 0.4, 0.7],
          }}
        />
        
        <div className="flex gap-2">
          {states.map((state) => (
            <button
              key={state}
              onClick={() => setCurrentState(state)}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                currentState === state
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {state.charAt(0).toUpperCase() + state.slice(1)}
            </button>
          ))}
        </div>
        
        <p className="text-sm text-gray-600 text-center max-w-md">
          Click the buttons above to switch between different AMNA states and observe the smooth transitions.
        </p>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Interactive demo allowing you to switch between all AMNA states and observe the smooth transitions and animations.',
      },
    },
  },
};

// Chat interface integration example
export const ChatIntegration: Story = {
  render: () => {
    const [messages, setMessages] = useState([
      { role: 'user', content: 'Hello AMNA, how are you?' },
    ]);
    const [isTyping, setIsTyping] = useState(false);
    const [amnaState, setAmnaState] = useState<AMNAState>('dormant');

    const simulateAMNAResponse = () => {
      setIsTyping(true);
      setAmnaState('processing');
      
      setTimeout(() => {
        setAmnaState('communicating');
        setTimeout(() => {
          setMessages(prev => [...prev, { 
            role: 'assistant', 
            content: 'Hello! I\'m AMNA, your Adaptive Mind Neural Assistant. I\'m functioning optimally and ready to help you today!' 
          }]);
          setIsTyping(false);
          setAmnaState('dormant');
        }, 2000);
      }, 1500);
    };

    return (
      <div className="w-full max-w-md mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-4 space-y-4">
          {/* Chat header with AMNA */}
          <div className="flex items-center gap-3 pb-3 border-b">
            <AMNA
              state={amnaState}
              size="sm"
              intensity="md"
              communicationSync={{
                isGenerating: isTyping,
                currentText: 'AMNA is thinking...',
                emotionalTone: 'neutral',
              }}
            />
            <div>
              <h3 className="font-semibold">AMNA</h3>
              <p className="text-xs text-gray-500">
                {amnaState === 'dormant' ? 'Ready' : 
                 amnaState === 'processing' ? 'Processing...' :
                 amnaState === 'communicating' ? 'Responding...' : 'Listening'}
              </p>
            </div>
          </div>

          {/* Messages */}
          <div className="space-y-3 max-h-40 overflow-y-auto">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                    message.role === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {message.content}
                </div>
              </div>
            ))}
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 px-3 py-2 rounded-lg text-sm text-gray-500">
                  AMNA is typing...
                </div>
              </div>
            )}
          </div>

          {/* Input area */}
          <div className="flex gap-2 pt-3 border-t">
            <input
              type="text"
              placeholder="Message AMNA..."
              className="flex-1 px-3 py-2 border rounded-lg text-sm"
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !isTyping) {
                  simulateAMNAResponse();
                }
              }}
            />
            <button
              onClick={simulateAMNAResponse}
              disabled={isTyping}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg text-sm disabled:opacity-50"
            >
              Send
            </button>
          </div>
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Example integration of AMNA with a chat interface, showing how AMNA responds to conversation flow and user interactions.',
      },
    },
  },
};

// Reduced motion accessibility
export const ReducedMotion: Story = {
  args: {
    state: 'processing',
    size: 'md',
    intensity: 'md',
    reducedMotion: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'AMNA with reduced motion for accessibility - provides a static, accessible version for users who prefer less animation.',
      },
    },
  },
};

// Performance monitoring
export const PerformanceDemo: Story = {
  render: () => {
    const [frameRate, setFrameRate] = useState(60);
    const [amnaCount, setAmnaCount] = useState(1);

    useEffect(() => {
      let lastTime = performance.now();
      let frameCount = 0;

      const measureFPS = () => {
        frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - lastTime >= 1000) {
          setFrameRate(Math.round((frameCount * 1000) / (currentTime - lastTime)));
          frameCount = 0;
          lastTime = currentTime;
        }
        
        requestAnimationFrame(measureFPS);
      };

      measureFPS();
    }, []);

    return (
      <div className="space-y-6">
        <div className="text-center">
          <p className="text-lg font-semibold">AMNA Performance Monitor</p>
          <p className="text-sm text-gray-600">FPS: {frameRate}</p>
        </div>

        <div className="flex justify-center">
          <label className="flex items-center gap-2">
            <span className="text-sm">AMNA Count:</span>
            <input
              type="range"
              min="1"
              max="10"
              value={amnaCount}
              onChange={(e) => setAmnaCount(Number(e.target.value))}
              className="w-32"
            />
            <span className="text-sm w-8">{amnaCount}</span>
          </label>
        </div>

        <div className="flex flex-wrap justify-center gap-4">
          {Array.from({ length: amnaCount }, (_, i) => (
            <AMNA
              key={i}
              state={['dormant', 'processing', 'communicating'][i % 3] as AMNAState}
              size="md"
              intensity="md"
            />
          ))}
        </div>

        <p className="text-xs text-gray-500 text-center max-w-md mx-auto">
          This demo shows multiple AMNA instances to test performance. AMNA is optimized to maintain 60fps even with multiple instances running simultaneously.
        </p>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Performance testing with multiple AMNA instances and real-time FPS monitoring to demonstrate optimization.',
      },
    },
  },
};

// AMNA Personality Showcase
export const PersonalityShowcase: Story = {
  render: () => {
    const [currentPersonality, setCurrentPersonality] = useState<'friendly' | 'professional' | 'creative'>('friendly');
    const [amnaState, setAmnaState] = useState<AMNAState>('dormant');

    const personalities = {
      friendly: {
        intensity: 'md' as const,
        description: 'Warm and approachable AMNA',
        greeting: 'Hi there! I\'m AMNA, ready to help! 😊'
      },
      professional: {
        intensity: 'subtle' as const,
        description: 'Professional and focused AMNA',
        greeting: 'Good day. I am AMNA, your neural assistant.'
      },
      creative: {
        intensity: 'strong' as const,
        description: 'Energetic and creative AMNA',
        greeting: 'Hey! I\'m AMNA - let\'s create something amazing! ✨'
      }
    };

    return (
      <div className="flex flex-col items-center gap-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">AMNA Personality Modes</h3>
          <p className="text-sm text-gray-600 mb-4">
            {personalities[currentPersonality].description}
          </p>
        </div>

        <AMNA
          state={amnaState}
          size="lg"
          intensity={personalities[currentPersonality].intensity}
        />

        <div className="flex gap-2">
          {Object.entries(personalities).map(([key, personality]) => (
            <button
              key={key}
              onClick={() => setCurrentPersonality(key as any)}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                currentPersonality === key
                  ? 'bg-purple-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {key.charAt(0).toUpperCase() + key.slice(1)}
            </button>
          ))}
        </div>

        <div className="flex gap-2">
          <button
            onClick={() => setAmnaState('processing')}
            className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
          >
            Think
          </button>
          <button
            onClick={() => setAmnaState('communicating')}
            className="px-3 py-1 bg-green-500 text-white rounded text-sm"
          >
            Speak
          </button>
          <button
            onClick={() => setAmnaState('voice')}
            className="px-3 py-1 bg-purple-500 text-white rounded text-sm"
          >
            Listen
          </button>
          <button
            onClick={() => setAmnaState('dormant')}
            className="px-3 py-1 bg-gray-500 text-white rounded text-sm"
          >
            Rest
          </button>
        </div>

        <div className="bg-gray-100 p-3 rounded-lg text-sm text-center max-w-md">
          "{personalities[currentPersonality].greeting}"
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Showcase of AMNA different personality modes with varying intensities and interaction styles.',
      },
    },
  },
};