import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/display/card";
import { <PERSON><PERSON> } from "@/components/ui/actions/button";
import { Badge } from "@/components/ui/display/badge";
import { Switch } from "@/components/ui/forms/switch";
import { Slider } from "@/components/ui/forms/slider";
import { Input } from "@/components/ui/forms/input";
import { Label } from "@/components/ui/forms/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/feedback/alert";
import { Progress } from "@/components/ui/display/progress-bar";
import { Avatar } from "@/components/ui/display/avatar";
import { Separator } from "@/components/ui/layout/divider";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/navigation/tabs";
import { ScrollArea } from "@/components/ui/layout/scroll-area";
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Settings,
  Bell,
  User,
  Shield,
  Palette,
  Mail,
  Calendar,
  Clock,
  Star,
  Heart,
  Bookmark,
  Download,
  Share,
  Play,
  Pause,
  SkipForward,
  Volume2,
  Info,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Maximize2,
  Monitor,
  Moon,
  Sun

interface ThemePreviewProps {
  className?: string;
  variant?: "compact" | "full" | "showcase";
  enableInteractions?: boolean;
}

export const ThemePreview: React.FC<ThemePreviewProps> = ({
  className,
  variant = "full",
  enableInteractions = true,
}) => {
  const [activeTab, setActiveTab] = useState("dashboard");
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [volume, setVolume] = useState([50]);
  const [progress, setProgress] = useState(65);

  if (variant === "compact") {
    return <CompactPreview className={className} />;
  }

  if (variant === "showcase") {
    return <ShowcasePreview className={className} />;
  }

  return (
    <div className={cn("w-full space-y-6", className)}>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="forms">Forms</TabsTrigger>
          <TabsTrigger value="data">Data</TabsTrigger>
          <TabsTrigger value="media">Media</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-4">
          <DashboardPreview 
            enableInteractions={enableInteractions}
            notificationsEnabled={notificationsEnabled}
            setNotificationsEnabled={setNotificationsEnabled}
            darkMode={darkMode}
            setDarkMode={setDarkMode}
            progress={progress}
          />
        </TabsContent>

        <TabsContent value="components" className="space-y-4">
          <ComponentsPreview enableInteractions={enableInteractions} />
        </TabsContent>

        <TabsContent value="forms" className="space-y-4">
          <FormsPreview enableInteractions={enableInteractions} />
        </TabsContent>

        <TabsContent value="data" className="space-y-4">
          <DataPreview />
        </TabsContent>

        <TabsContent value="media" className="space-y-4">
          <MediaPreview 
            enableInteractions={enableInteractions}
            volume={volume}
            setVolume={setVolume}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

const DashboardPreview: React.FC<{
  enableInteractions: boolean;
  notificationsEnabled: boolean;
  setNotificationsEnabled: (enabled: boolean) => void;
  darkMode: boolean;
  setDarkMode: (dark: boolean) => void;
  progress: number;
}> = ({ enableInteractions, notificationsEnabled, setNotificationsEnabled, darkMode, setDarkMode, progress }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {/* Stats Cards */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
          <Badge variant="secondary">+12%</Badge>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">$45,231.89</div>
          <p className="text-xs text-muted-foreground">+20.1% from last month</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Users</CardTitle>
          <User className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">2,350</div>
          <p className="text-xs text-muted-foreground">+180.1% from last month</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Progress</CardTitle>
          <CheckCircle className="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{progress}%</div>
          <Progress value={progress} className="mt-2" />
        </CardContent>
      </Card>

      {/* Settings Card */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Quick Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              <Label>Notifications</Label>
            </div>
            <Switch
              checked={notificationsEnabled}
              onCheckedChange={enableInteractions ? setNotificationsEnabled : undefined}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {darkMode ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
              <Label>Dark Mode</Label>
            </div>
            <Switch
              checked={darkMode}
              onCheckedChange={enableInteractions ? setDarkMode : undefined}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <Label>Two-Factor Auth</Label>
            </div>
            <Badge variant="outline">Enabled</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Activity Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Recent Activity</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center gap-3">
            <Avatar className="h-6 w-6">
              <User className="h-3 w-3" />
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">Sarah updated project</p>
              <p className="text-xs text-muted-foreground">2 minutes ago</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Avatar className="h-6 w-6">
              <User className="h-3 w-3" />
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">New user registered</p>
              <p className="text-xs text-muted-foreground">5 minutes ago</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Avatar className="h-6 w-6">
              <User className="h-3 w-3" />
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">Payment received</p>
              <p className="text-xs text-muted-foreground">1 hour ago</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const ComponentsPreview: React.FC<{ enableInteractions: boolean }> = ({ enableInteractions }) => {
  return (
    <div className="space-y-6">
      {/* Buttons Section */}
      <Card>
        <CardHeader>
          <CardTitle>Buttons & Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button variant="default">Primary</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="link">Link</Button>
            <Button variant="destructive">Delete</Button>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button size="sm">Small</Button>
            <Button size="default">Default</Button>
            <Button size="lg">Large</Button>
            <Button size="icon">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Alerts Section */}
      <Card>
        <CardHeader>
          <CardTitle>Alerts & Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Information</AlertTitle>
            <AlertDescription>
              This is an informational alert with default styling.
            </AlertDescription>
          </Alert>

          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              This is an error alert showing destructive colors.
            </AlertDescription>
          </Alert>

          <div className="flex flex-wrap gap-2">
            <Badge>Default</Badge>
            <Badge variant="secondary">Secondary</Badge>
            <Badge variant="outline">Outline</Badge>
            <Badge variant="destructive">Error</Badge>
            <Badge className="bg-green-500 hover:bg-green-600">Success</Badge>
            <Badge className="bg-yellow-500 hover:bg-yellow-600">Warning</Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const FormsPreview: React.FC<{ enableInteractions: boolean }> = ({ enableInteractions }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Form Controls</CardTitle>
        <CardDescription>
          Interactive form elements showcase
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input id="email" type="email" placeholder="Enter your email" />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input id="name" placeholder="Enter your name" />
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch id="marketing" />
            <Label htmlFor="marketing">Marketing emails</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch id="security" defaultChecked />
            <Label htmlFor="security">Security updates</Label>
          </div>
        </div>

        <div className="space-y-2">
          <Label>Volume</Label>
          <Slider defaultValue={[50]} max={100} step={1} />
        </div>

        <CardFooter className="px-0 pb-0">
          <Button className="w-full">Submit Form</Button>
        </CardFooter>
      </CardContent>
    </Card>
  );
};

const DataPreview: React.FC = () => {
  const users = [
    { id: 1, name: "John Doe", email: "<EMAIL>", role: "Admin", status: "Active" },
    { id: 2, name: "Jane Smith", email: "<EMAIL>", role: "User", status: "Inactive" },
    { id: 3, name: "Bob Johnson", email: "<EMAIL>", role: "User", status: "Active" },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Data Tables</CardTitle>
        <CardDescription>
          Data display and organization
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">{user.name}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{user.role}</TableCell>
                <TableCell>
                  <Badge variant={user.status === "Active" ? "default" : "secondary"}>
                    {user.status}
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

const MediaPreview: React.FC<{
  enableInteractions: boolean;
  volume: number[];
  setVolume: (volume: number[]) => void;
}> = ({ enableInteractions, volume, setVolume }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Play className="h-5 w-5" />
          Media Player
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-4">
          <Button size="icon" variant="outline">
            <Play className="h-4 w-4" />
          </Button>
          <Button size="icon" variant="outline">
            <Pause className="h-4 w-4" />
          </Button>
          <Button size="icon" variant="outline">
            <SkipForward className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Volume2 className="h-4 w-4" />
          <Slider
            value={volume}
            onValueChange={enableInteractions ? setVolume : undefined}
            max={100}
            step={1}
            className="flex-1"
          />
          <span className="text-sm text-muted-foreground w-8">{volume[0]}</span>
        </div>

        <div className="flex gap-2">
          <Button size="sm" variant="outline">
            <Heart className="h-4 w-4 mr-1" />
            Like
          </Button>
          <Button size="sm" variant="outline">
            <Bookmark className="h-4 w-4 mr-1" />
            Save
          </Button>
          <Button size="sm" variant="outline">
            <Share className="h-4 w-4 mr-1" />
            Share
          </Button>
          <Button size="sm" variant="outline">
            <Download className="h-4 w-4 mr-1" />
            Download
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

const CompactPreview: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <Card className={cn("w-full max-w-sm", className)}>
      <CardHeader>
        <CardTitle className="text-sm">Theme Preview</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex gap-2">
          <Button size="sm">Primary</Button>
          <Button size="sm" variant="secondary">Secondary</Button>
        </div>
        
        <Badge>Sample Badge</Badge>
        
        <div className="text-sm">
          <p className="font-medium">Sample text on background</p>
          <p className="text-muted-foreground">Muted text color</p>
        </div>
        
        <Progress value={75} />
      </CardContent>
    </Card>
  );
};

const ShowcasePreview: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <div className={cn("grid gap-4", className)}>
      {/* Color Swatches */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Color Palette</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-8 gap-2">
            {[
              "background", "foreground", "primary", "secondary", 
              "accent", "muted", "destructive", "border"
            ].map((color) => (
              <div
                key={color}
                className={`aspect-square rounded border bg-${color}`}
                title={color}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Components */}
      <div className="grid grid-cols-2 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <Button className="w-full">Primary Action</Button>
              <Button variant="outline" className="w-full">Secondary</Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <Badge>Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="outline">Outline</Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ThemePreview;