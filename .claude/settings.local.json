{"permissions": {"allow": ["Bash(git reset:*)", "Bash(npm run typecheck:*)", "Bash(npx tsc:*)", "Bash(find:*)", "Bash(npx prisma generate:*)", "Bash(npx prisma migrate dev:*)", "Bash(npx prisma db push:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(grep:*)", "Bash(grep -n \"export.*DataRequestStatus\" /Users/<USER>/Luminar/apps/command-center/src/modules/gdpr/services/data-portability.service.ts)", "Bash(grep -n -A 5 \"enum DataCategory\\|enum RetentionAction\" /Users/<USER>/Luminar/apps/command-center/prisma/schema.prisma)", "Bash(grep -n \"model User\" /Users/<USER>/Luminar/apps/command-center/prisma/schema.prisma)", "Bash(grep -r \"DataCategory\\|RetentionAction\" /Users/<USER>/Luminar/apps/command-center/prisma/schema.prisma)", "Bash(grep -n -A 15 \"model DataRetentionPolicy\" /Users/<USER>/Luminar/apps/command-center/prisma/schema.prisma)", "Bash(npm run type-check:backend:*)", "Bash(npm run type-check:*)", "Bash(rg:*)", "Bash(# Get all remaining files that need fixing\nfind . -name \"\"*.tsx\"\" -exec awk ''/import type.*{/{flag=1} flag && /defaultComponentProps/{print FILENAME; flag=0; nextfile} /}.*from.*component-props/{flag=0}'' {} \\;)", "Bash(# Count how many files are left\nfind . -name \"\"*.tsx\"\" -exec awk ''/import type.*{/{flag=1} flag && /defaultComponentProps/{print FILENAME; flag=0; nextfile} /}.*from.*component-props/{flag=0}'' {} \\; | wc -l)", "Bash(#!/bin/bash\n# Create a script to fix all remaining files\nfiles_to_fix=(\n  \"\"./components/layouts/application/ecommerce-layout.tsx\"\"\n  \"\"./components/layouts/specialized/landing-page.tsx\"\"\n  \"\"./lib/component-factory.ts\"\"\n)\n\nfor file in \"\"${files_to_fix[@]}\"\"; do\n  if [[ -f \"\"$file\"\" ]]; then\n    echo \"\"Processing: $file\"\"\n    # Create a temporary file with the fix\n    awk ''\n      /import type.*\\{/ { \n        in_import=1; \n        import_block=\"\"\"\"\n      }\n      in_import {\n        import_block = import_block $0 \"\"\\n\"\"\n        if (/\\}.*from.*component-props/) {\n          # Check if defaultComponentProps is in the import block\n          if (import_block ~ /defaultComponentProps/) {\n            # Split the import\n            gsub(/,\\s*defaultComponentProps/, \"\"\"\", import_block)\n            gsub(/defaultComponentProps,\\s*/, \"\"\"\", import_block)\n            gsub(/defaultComponentProps/, \"\"\"\", import_block)\n            printf \"\"%s\"\", import_block\n            print \"\"import { defaultComponentProps } from \"\" substr($0, index($0, \"\"from\"\"))\n          } else {\n            printf \"\"%s\"\", import_block\n          }\n          in_import=0\n          import_block=\"\"\"\"\n        }\n        next\n      }\n      !in_import { print }\n    '' \"\"$file\"\" > \"\"${file}.tmp\"\" && mv \"\"${file}.tmp\"\" \"\"$file\"\"\n  fi\ndone)", "<PERSON><PERSON>(python3:*)", "Bash(# Final verification\nfind . -name \"\"*.ts\"\" -o -name \"\"*.tsx\"\" | xargs grep -l \"\"defaultComponentProps\"\" | xargs grep -l \"\"import type.*{.*defaultComponentProps\"\" | wc -l)", "Bash(rm:*)"], "deny": []}}