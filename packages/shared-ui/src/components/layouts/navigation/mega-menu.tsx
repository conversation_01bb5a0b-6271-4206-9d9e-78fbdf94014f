import { ReactNode, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  ChevronRight,
  Search,
  Tag,
  Star,
  TrendingUp,
  Zap,
  Shield,
  Users,
  Settings,
  ArrowRight,
  ExternalLink,
  ChevronDown
} from "lucide-react";

import { LuminarCard } from '../../ui/display';
import { Button } from '../../ui/actions';
import { LuminarInput } from '../../ui/forms';
import { LuminarBadge } from '../../ui/display';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface MegaMenuProps {
  className?: string;
  brand?: {
    name: string;
    logo?: string;
    href?: string;
  };
  menuItems?: MegaMenuItem[];
  quickActions?: QuickAction[];
  searchPlaceholder?: string;
  showSearch?: boolean;
  onSearch?: (query: string) => void;
  onItemClick?: (itemId: string, href?: string) => void;
  theme?: 'light' | 'dark' | 'glass';
}

export interface MegaMenuItem {
  id: string;
  label: string;
  href?: string;
  icon?: React.ComponentType<{ className?: string }>;
  megaContent?: MegaMenuSection[];
  badge?: string;
  isNew?: boolean;
  isPopular?: boolean;
  description?: string;
}

export interface MegaMenuSection {
  id: string;
  title: string;
  description?: string;
  items: MegaMenuLink[];
  featured?: MegaMenuFeatured;
  showAll?: {
    label: string;
    href: string;
  };
}

export interface MegaMenuLink {
  id: string;
  label: string;
  description?: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  badge?: string;
  isNew?: boolean;
  isPopular?: boolean;
}

export interface MegaMenuFeatured {
  title: string;
  description: string;
  image?: string;
  href: string;
  cta: string;
}

export interface QuickAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  onClick?: () => void;
}

const defaultMenuItems: MegaMenuItem[] = [
  {
    id: 'products',
    label: 'Products',
    megaContent: [
      {
        id: 'featured',
        title: 'Featured Products',
        items: [
          {
            id: 'analytics',
            label: 'Analytics Dashboard',
            description: 'Advanced data visualization and insights',
            href: '/products/analytics',
            icon: TrendingUp,
            isNew: true
          },
          {
            id: 'security',
            label: 'Security Suite',
            description: 'Enterprise-grade security solutions',
            href: '/products/security',
            icon: Shield,
            isPopular: true
          },
          {
            id: 'collaboration',
            label: 'Team Collaboration',
            description: 'Real-time teamwork and communication',
            href: '/products/collaboration',
            icon: Users
          }
        ],
        featured: {
          title: 'New: AI-Powered Insights',
          description: 'Transform your data into actionable insights with our latest AI features.',
          href: '/products/ai-insights',
          cta: 'Learn More'
        }
      },
      {
        id: 'integrations',
        title: 'Integrations',
        items: [
          {
            id: 'api',
            label: 'REST API',
            description: 'Powerful API for custom integrations',
            href: '/integrations/api',
            icon: Zap
          },
          {
            id: 'webhooks',
            label: 'Webhooks',
            description: 'Real-time event notifications',
            href: '/integrations/webhooks',
            icon: Settings
          }
        ],
        showAll: {
          label: 'View All Integrations',
          href: '/integrations'
        }
      }
    ]
  },
  {
    id: 'solutions',
    label: 'Solutions',
    megaContent: [
      {
        id: 'by-industry',
        title: 'By Industry',
        items: [
          {
            id: 'fintech',
            label: 'Financial Services',
            description: 'Secure solutions for financial institutions',
            href: '/solutions/fintech'
          },
          {
            id: 'healthcare',
            label: 'Healthcare',
            description: 'HIPAA-compliant healthcare solutions',
            href: '/solutions/healthcare'
          },
          {
            id: 'ecommerce',
            label: 'E-commerce',
            description: 'Scale your online business',
            href: '/solutions/ecommerce'
          }
        ]
      },
      {
        id: 'by-role',
        title: 'By Role',
        items: [
          {
            id: 'developers',
            label: 'For Developers',
            description: 'Tools and APIs for developers',
            href: '/solutions/developers'
          },
          {
            id: 'executives',
            label: 'For Executives',
            description: 'Strategic insights and reporting',
            href: '/solutions/executives'
          }
        ]
      }
    ]
  },
  {
    id: 'resources',
    label: 'Resources',
    megaContent: [
      {
        id: 'learn',
        title: 'Learn',
        items: [
          {
            id: 'documentation',
            label: 'Documentation',
            description: 'Comprehensive guides and references',
            href: '/docs'
          },
          {
            id: 'tutorials',
            label: 'Tutorials',
            description: 'Step-by-step learning resources',
            href: '/tutorials'
          },
          {
            id: 'blog',
            label: 'Blog',
            description: 'Latest insights and best practices',
            href: '/blog'
          }
        ]
      },
      {
        id: 'support',
        title: 'Support',
        items: [
          {
            id: 'help-center',
            label: 'Help Center',
            description: 'Find answers to common questions',
            href: '/help'
          },
          {
            id: 'community',
            label: 'Community',
            description: 'Connect with other users',
            href: '/community'
          }
        ]
      }
    ]
  }
];

const defaultQuickActions: QuickAction[] = [
  {
    id: 'search',
    label: 'Search',
    icon: Search
  },
  {
    id: 'favorites',
    label: 'Favorites',
    icon: Star
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings
  }
];

export function MegaMenu({
  className,
  brand = { name: 'Brand' },
  menuItems = defaultMenuItems,
  quickActions = defaultQuickActions,
  searchPlaceholder = 'Search...',
  showSearch = true,
  onSearch,
  onItemClick,
  theme = 'glass',
  ...props
}: MegaMenuProps) {
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    onSearch?.(query);
  };

  const handleItemClick = (itemId: string, href?: string) => {
    onItemClick?.(itemId, href);
    setActiveMenu(null);
  };

  const themeClasses = {
    light: 'bg-white border-gray-200',
    dark: 'bg-gray-900 border-gray-700',
    glass: 'bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg border-gray-200/20 dark:border-gray-700/20'
  };

  return (
    <nav className={cn(
      "sticky top-0 z-50 border-b",
      themeClasses[theme],
      className
    )} {...props}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Brand */}
          <div className="flex items-center gap-8">
            <a
              href={brand.href || '/'}
              className="flex items-center gap-3"
              onClick={() => handleItemClick('brand', brand.href)}
            >
              {brand.logo ? (
                <img src={brand.logo} alt={brand.name} className="h-8 w-auto" />
              ) : (
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center font-bold text-white">
                  {brand.name.charAt(0)}
                </div>
              )}
              <span className="text-xl font-bold text-gray-900 dark:text-white">
                {brand.name}
              </span>
            </a>

            {/* Main Menu */}
            <div className="hidden lg:flex items-center space-x-1">
              {menuItems.map((item) => (
                <div
                  key={item.id}
                  className="relative"
                  onMouseEnter={() => item.megaContent && setActiveMenu(item.id)}
                  onMouseLeave={() => setActiveMenu(null)}
                >
                  <button
                    onClick={() => handleItemClick(item.id, item.href)}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors",
                      activeMenu === item.id
                        ? "bg-blue-50 dark:bg-blue-950 text-blue-600 dark:text-blue-400"
                        : "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
                    )}
                  >
                    {item.icon && <item.icon className="w-4 h-4" />}
                    {item.label}
                    {item.megaContent && <ChevronDown className="w-4 h-4" />}
                    {item.badge && (
                      <LuminarBadge size="sm" variant="success">
                        {item.badge}
                      </LuminarBadge>
                    )}
                    {item.isNew && (
                      <LuminarBadge size="sm" variant="success">New</LuminarBadge>
                    )}
                    {item.isPopular && (
                      <LuminarBadge size="sm" variant="warning">Popular</LuminarBadge>
                    )}
                  </button>

                  {/* Mega Menu Dropdown */}
                  <AnimatePresence>
                    {activeMenu === item.id && item.megaContent && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        transition={{ duration: 0.2 }}
                        className="absolute top-full left-0 mt-2 w-screen max-w-6xl"
                        style={{ transform: 'translateX(-50%)', left: '50%' }}
                      >
                        <LuminarCard className="p-8 shadow-xl">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            {item.megaContent.map((section) => (
                              <div key={section.id}>
                                <h3 className="font-semibold text-gray-900 dark:text-white mb-4">
                                  {section.title}
                                </h3>
                                {section.description && (
                                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    {section.description}
                                  </p>
                                )}
                                
                                <div className="space-y-3">
                                  {section.items.map((link) => (
                                    <a
                                      key={link.id}
                                      href={link.href}
                                      onClick={() => handleItemClick(link.id, link.href)}
                                      className="group flex items-start gap-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                                    >
                                      {link.icon && (
                                        <link.icon className="w-5 h-5 text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 mt-0.5 flex-shrink-0" />
                                      )}
                                      <div className="flex-1 min-w-0">
                                        <div className="flex items-center gap-2">
                                          <span className="font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                                            {link.label}
                                          </span>
                                          {link.isNew && (
                                            <LuminarBadge size="sm" variant="success">New</LuminarBadge>
                                          )}
                                          {link.isPopular && (
                                            <LuminarBadge size="sm" variant="warning">Popular</LuminarBadge>
                                          )}
                                          {link.badge && (
                                            <LuminarBadge size="sm">{link.badge}</LuminarBadge>
                                          )}
                                        </div>
                                        {link.description && (
                                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                            {link.description}
                                          </p>
                                        )}
                                      </div>
                                      <ChevronRight className="w-4 h-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                                    </a>
                                  ))}
                                </div>

                                {section.showAll && (
                                  <a
                                    href={section.showAll.href}
                                    onClick={() => handleItemClick('show-all', section.showAll?.href)}
                                    className="inline-flex items-center gap-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 mt-4"
                                  >
                                    {section.showAll.label}
                                    <ArrowRight className="w-4 h-4" />
                                  </a>
                                )}

                                {section.featured && (
                                  <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950 rounded-lg">
                                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                                      {section.featured.title}
                                    </h4>
                                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                      {section.featured.description}
                                    </p>
                                    <a
                                      href={section.featured.href}
                                      onClick={() => handleItemClick('featured', section.featured?.href)}
                                      className="inline-flex items-center gap-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
                                    >
                                      {section.featured.cta}
                                      <ExternalLink className="w-4 h-4" />
                                    </a>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </LuminarCard>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ))}
            </div>
          </div>

          {/* Right Side */}
          <div className="flex items-center gap-4">
            {/* Search */}
            {showSearch && (
              <div className="hidden md:block relative">
                <LuminarInput
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  placeholder={searchPlaceholder}
                  className="w-64"
                  icon={Search}
                />
              </div>
            )}

            {/* Quick Actions */}
            <div className="hidden lg:flex items-center gap-2">
              {quickActions.map((action) => (
                <Button
                  key={action.id}
                  variant="ghost"
                  size="sm"
                  onClick={action.onClick || (() => handleItemClick(action.id, action.href))}
                  className="p-2"
                >
                  <action.icon className="w-5 h-5" />
                  <span className="sr-only">{action.label}</span>
                </Button>
              ))}
            </div>

            {/* Mobile Menu Button */}
            <Button variant="ghost" size="sm" className="lg:hidden">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
}