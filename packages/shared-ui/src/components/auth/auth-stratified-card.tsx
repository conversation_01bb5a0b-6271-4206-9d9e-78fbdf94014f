"use client";

import React from "react";
import { StratifiedGlassDepth, glassPresets } from "../glass/StratifiedGlassDepth";
import { cn } from '../../lib/utils';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../types/component-props';
import { defaultComponentProps } from '../../types/component-props';

interface AuthStratifiedCardProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  description?: string;
  enableParallax?: boolean;
  enableInteraction?: boolean;
}

export const AuthStratifiedCard: React.FC<AuthStratifiedCardProps> = ({
  children,
  className,
  title,
  description,
  enableParallax = true,
  enableInteraction = true,
}) => {
  return (
    <div className={cn("relative w-full max-w-md mx-auto h-auto min-h-[400px]", className)}>
      <StratifiedGlassDepth
        layers={glassPresets.auth}
        enableParallax={enableParallax}
        enableInteraction={enableInteraction}
        className="rounded-xl overflow-hidden"
      >
        <div className="relative z-10 p-8 h-full flex flex-col justify-center">
          {title && (
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">{title}</h2>
              {description && (
                <p className="text-white/80 text-sm">{description}</p>
              )}
            </div>
          )}
          <div className="flex-1 flex flex-col justify-center">
            {children}
          </div>
        </div>
      </StratifiedGlassDepth>
    </div>
  );
};