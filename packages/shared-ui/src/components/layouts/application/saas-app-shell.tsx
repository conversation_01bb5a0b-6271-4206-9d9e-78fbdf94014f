import { ReactNode, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Menu,

  Search,
  Bell,
  Settings,
  HelpCircle,
  LogOut,
  ChevronDown,
  Home,
  BarChart3,
  Users,
  FileText,
  Database,
  Zap,
  CreditCard,
  Shield,
  Globe,
  Smartphone,
  Mail,
  Calendar,
  Star,
  Plus,
  Filter,
  Download,
  Share2,
  X
} from "lucide-react";

import { LuminarCard } from '../../ui/display';
import { Button } from '../../ui/actions';
import { LuminarInput } from '../../ui/forms';
import { LuminarBadge, LuminarAvatar } from '../../ui/display';
import { ThemeToggle } from '../../ui/utilities';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface SaaSAppShellProps {
  className?: string;
  children: ReactNode;
  user?: AppUser;
  organization?: Organization;
  navigation?: NavigationSection[];
  topActions?: TopAction[];
  notifications?: AppNotification[];
  showMobileMenu?: boolean;
  onMobileMenuToggle?: (open: boolean) => void;
  breadcrumbs?: Breadcrumb[];
  headerTitle?: string;
  headerSubtitle?: string;
  headerActions?: ReactNode;
  showSearch?: boolean;
  onSearch?: (query: string) => void;
  showNotifications?: boolean;
  showUserMenu?: boolean;
  footer?: ReactNode;
}

export interface AppUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  plan: 'free' | 'starter' | 'pro' | 'enterprise';
  permissions?: string[];
}

export interface Organization {
  id: string;
  name: string;
  avatar?: string;
  plan: 'free' | 'starter' | 'pro' | 'enterprise';
  memberCount?: number;
  billingStatus?: 'active' | 'trial' | 'expired';
}

export interface NavigationSection {
  id: string;
  title?: string;
  items: NavigationItem[];
  collapsible?: boolean;
  defaultOpen?: boolean;
}

export interface NavigationItem {
  id: string;
  label: string;
  href?: string;
  onClick?: () => void;
  icon?: React.ComponentType<{ className?: string }>;
  active?: boolean;
  badge?: string | number;
  disabled?: boolean;
  children?: NavigationItem[];
}

export interface TopAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  variant?: ComponentVariant | 'primary' | 'outline';
  badge?: string | number;
}

export interface AppNotification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: Date;
  read?: boolean;
  avatar?: string;
  actionUrl?: string;
}

export interface Breadcrumb {
  label: string;
  href?: string;
  onClick?: () => void;
}

const defaultNavigation: NavigationSection[] = [
  {
    id: 'main',
    items: [
      { id: 'dashboard', label: 'Dashboard', icon: Home, active: true },
      { id: 'analytics', label: 'Analytics', icon: BarChart3, badge: 'New' },
      { id: 'users', label: 'Users', icon: Users },
      { id: 'projects', label: 'Projects', icon: FileText },
    ]
  },
  {
    id: 'tools',
    title: 'Tools',
    items: [
      { id: 'database', label: 'Database', icon: Database },
      { id: 'api', label: 'API', icon: Zap },
      { id: 'integrations', label: 'Integrations', icon: Globe },
    ]
  },
  {
    id: 'account',
    title: 'Account',
    items: [
      { id: 'billing', label: 'Billing', icon: CreditCard },
      { id: 'security', label: 'Security', icon: Shield },
      { id: 'settings', label: 'Settings', icon: Settings },
    ]
  }
];

const defaultTopActions: TopAction[] = [
  { id: 'new-project', label: 'New Project', icon: Plus, onClick: () => {}, variant: 'primary' },
  { id: 'invite', label: 'Invite', icon: Mail, onClick: () => {} },
];

export function SaaSAppShell({
  className,
  children,
  user,
  organization,
  navigation = defaultNavigation,
  topActions = defaultTopActions,
  notifications = [],
  showMobileMenu: controlledMobileMenu,
  onMobileMenuToggle,
  breadcrumbs = [],
  headerTitle,
  headerSubtitle,
  headerActions,
  showSearch = true,
  onSearch,
  showNotifications = true,
  showUserMenu = true,
  footer,
  ...props
}: SaaSAppShellProps) {
  const [internalMobileMenu, setInternalMobileMenu] = useState(false);
  const [showNotificationPanel, setShowNotificationPanel] = useState(false);
  const [showUserMenuPanel, setShowUserMenuPanel] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedSections, setExpandedSections] = useState<string[]>(['main', 'tools', 'account']);

  const mobileMenuOpen = controlledMobileMenu ?? internalMobileMenu;
  const setMobileMenuOpen = onMobileMenuToggle ?? setInternalMobileMenu;

  const unreadNotifications = notifications.filter(n => !n.read).length;

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const getPlanBadgeColor = (plan: string) => {
    switch (plan) {
      case 'enterprise': return 'bg-purple-500';
      case 'pro': return 'bg-blue-500';
      case 'starter': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className={cn(
      "min-h-screen bg-gray-50 dark:bg-gray-900",
      className
    )} {...props}>
      
      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setMobileMenuOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <aside className={cn(
        "fixed inset-y-0 left-0 w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transform transition-transform duration-200 ease-in-out z-50",
        mobileMenuOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
      )}>
        {/* Sidebar Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {organization && (
                <>
                  <LuminarAvatar
                    src={organization.avatar}
                    alt={organization.name}
                    fallback={organization.name.charAt(0)}
                    size="sm"
                  />
                  <div>
                    <h2 className="font-semibold text-gray-900 dark:text-white">
                      {organization.name}
                    </h2>
                    <div className="flex items-center gap-2">
                      <LuminarBadge
                        variant="default"
                        size="sm"
                        className={cn("text-white border-white/20", getPlanBadgeColor(organization.plan))}
                      >
                        {organization.plan}
                      </LuminarBadge>
                    </div>
                  </div>
                </>
              )}
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setMobileMenuOpen(false)}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto p-4">
          <div className="space-y-6">
            {navigation.map((section) => (
              <div key={section.id}>
                {section.title && (
                  <button
                    onClick={() => section.collapsible && toggleSection(section.id)}
                    className="flex items-center justify-between w-full mb-3"
                  >
                    <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      {section.title}
                    </h3>
                    {section.collapsible && (
                      <ChevronDown className={cn(
                        "w-3 h-3 transition-transform",
                        expandedSections.includes(section.id) ? "rotate-0" : "-rotate-90"
                      )} />
                    )}
                  </button>
                )}
                
                <AnimatePresence>
                  {(!section.collapsible || expandedSections.includes(section.id)) && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="space-y-1"
                    >
                      {section.items.map((item) => (
                        <Button
                          key={item.id}
                          variant={item.active ? "default" : "ghost"}
                          className="w-full justify-start gap-3 h-10"
                          onClick={item.onClick}
                          disabled={item.disabled}
                        >
                          {item.icon && <item.icon className="w-4 h-4" />}
                          <span className="flex-1 text-left">{item.label}</span>
                          {item.badge && (
                            <LuminarBadge variant="info" size="sm">
                              {item.badge}
                            </LuminarBadge>
                          )}
                        </Button>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>
        </nav>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="space-y-2">
            <Button variant="ghost" className="w-full justify-start gap-3">
              <HelpCircle className="w-4 h-4" />
              Help & Support
            </Button>
            <Button variant="ghost" className="w-full justify-start gap-3">
              <LogOut className="w-4 h-4" />
              Sign Out
            </Button>
          </div>
        </div>
      </aside>

      {/* Main Content */}
      <div className="lg:ml-64">
        {/* Top Header */}
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 lg:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Mobile Menu Button */}
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden"
                onClick={() => setMobileMenuOpen(true)}
              >
                <Menu className="w-5 h-5" />
              </Button>

              {/* Breadcrumbs */}
              {breadcrumbs.length > 0 && (
                <nav className="flex items-center gap-2 text-sm">
                  {breadcrumbs.map((crumb, index) => (
                    <div key={index} className="flex items-center gap-2">
                      {index > 0 && <span className="text-gray-400">/</span>}
                      <button
                        onClick={crumb.onClick}
                        className={cn(
                          "hover:text-blue-600 dark:hover:text-blue-400",
                          index === breadcrumbs.length - 1
                            ? "text-gray-900 dark:text-white font-medium"
                            : "text-gray-600 dark:text-gray-400"
                        )}
                      >
                        {crumb.label}
                      </button>
                    </div>
                  ))}
                </nav>
              )}

              {/* Page Title */}
              {(headerTitle || headerSubtitle) && (
                <div>
                  {headerTitle && (
                    <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {headerTitle}
                    </h1>
                  )}
                  {headerSubtitle && (
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {headerSubtitle}
                    </p>
                  )}
                </div>
              )}
            </div>

            <div className="flex items-center gap-4">
              {/* Search */}
              {showSearch && (
                <div className="hidden md:block">
                  <LuminarInput
                    placeholder="Search..."
                    value={searchQuery}
                    onChange={(value) => setSearchQuery(value)}
                    onKeyPress={(e) => e.key === 'Enter' && onSearch?.(searchQuery)}
                    icon={Search}
                    className="w-64"
                    size="sm"
                  />
                </div>
              )}

              {/* Top Actions */}
              {topActions.length > 0 && (
                <div className="flex items-center gap-2">
                  {topActions.map((action) => (
                    <Button
                      key={action.id}
                      variant={action.variant === "primary" ? "default" : action.variant || "outline"}
                      size="sm"
                      onClick={action.onClick}
                      className="relative"
                    >
                      <action.icon className="w-4 h-4 mr-2" />
                      {action.label}
                      {action.badge && (
                        <LuminarBadge
                          variant="error"
                          size="sm"
                          className="absolute -top-1 -right-1 min-w-[1.25rem] h-5"
                        >
                          {action.badge}
                        </LuminarBadge>
                      )}
                    </Button>
                  ))}
                </div>
              )}

              {/* Notifications */}
              {showNotifications && (
                <div className="relative">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowNotificationPanel(!showNotificationPanel)}
                    className="relative"
                  >
                    <Bell className="w-5 h-5" />
                    {unreadNotifications > 0 && (
                      <LuminarBadge
                        variant="error"
                        size="sm"
                        className="absolute -top-1 -right-1 min-w-[1.25rem] h-5"
                      >
                        {unreadNotifications}
                      </LuminarBadge>
                    )}
                  </Button>

                  <AnimatePresence>
                    {showNotificationPanel && (
                      <motion.div
                        initial={{ opacity: 0, y: -10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -10, scale: 0.95 }}
                        className="absolute right-0 top-full mt-2 w-80 z-50"
                      >
                        <LuminarCard className="p-4 max-h-96 overflow-y-auto">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="font-semibold">Notifications</h3>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setShowNotificationPanel(false)}
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                          
                          {notifications.length === 0 ? (
                            <p className="text-gray-500 text-center py-4">
                              No notifications
                            </p>
                          ) : (
                            <div className="space-y-3">
                              {notifications.slice(0, 5).map((notification) => (
                                <div
                                  key={notification.id}
                                  className={cn(
                                    "p-3 rounded-lg border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",
                                    notification.read
                                      ? "bg-gray-50 dark:bg-gray-800"
                                      : "bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800"
                                  )}
                                >
                                  <div className="flex items-start gap-3">
                                    {notification.avatar && (
                                      <LuminarAvatar
                                        src={notification.avatar}
                                        alt=""
                                        size="sm"
                                      />
                                    )}
                                    <div className="flex-1">
                                      <h4 className="font-medium text-sm">
                                        {notification.title}
                                      </h4>
                                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                        {notification.message}
                                      </p>
                                      <p className="text-xs text-gray-400 mt-1">
                                        {notification.timestamp.toLocaleTimeString()}
                                      </p>
                                    </div>
                                    <LuminarBadge
                                      variant={notification.type}
                                      size="sm"
                                    >
                                      {notification.type}
                                    </LuminarBadge>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </LuminarCard>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              )}

              {/* User Menu */}
              {showUserMenu && user && (
                <div className="relative">
                  <Button
                    variant="ghost"
                    onClick={() => setShowUserMenuPanel(!showUserMenuPanel)}
                    className="flex items-center gap-2"
                  >
                    <LuminarAvatar
                      src={user.avatar}
                      alt={user.name}
                      size="sm"
                      fallback={user.name.slice(0, 2).toUpperCase()}
                    />
                    <div className="text-left hidden md:block">
                      <p className="text-sm font-medium">{user.name}</p>
                      <p className="text-xs text-gray-500">{user.role}</p>
                    </div>
                    <ChevronDown className="w-4 h-4" />
                  </Button>

                  <AnimatePresence>
                    {showUserMenuPanel && (
                      <motion.div
                        initial={{ opacity: 0, y: -10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -10, scale: 0.95 }}
                        className="absolute right-0 top-full mt-2 w-64 z-50"
                      >
                        <LuminarCard className="p-4">
                          <div className="space-y-3">
                            <div className="pb-3 border-b border-gray-200 dark:border-gray-700">
                              <p className="font-medium">{user.name}</p>
                              <p className="text-sm text-gray-500">{user.email}</p>
                              <div className="flex items-center gap-2 mt-2">
                                <LuminarBadge variant="default" size="sm">
                                  {user.role}
                                </LuminarBadge>
                                <LuminarBadge
                                  variant="default"
                                  size="sm"
                                  className={cn("text-white border-white/20", getPlanBadgeColor(user.plan))}
                                >
                                  {user.plan}
                                </LuminarBadge>
                              </div>
                            </div>
                            
                            <Button variant="ghost" className="w-full justify-start gap-3">
                              <Settings className="w-4 h-4" />
                              Account Settings
                            </Button>
                            
                            <Button variant="ghost" className="w-full justify-start gap-3">
                              <CreditCard className="w-4 h-4" />
                            </Button>
                            
                            <div className="flex items-center justify-between">
                              <span className="text-sm">Dark Mode</span>
                              <ThemeToggle />
                            </div>
                            
                            <hr className="border-gray-200 dark:border-gray-700" />
                            
                            <Button variant="ghost" className="w-full justify-start gap-3">
                              <LogOut className="w-4 h-4" />
                              Sign Out
                            </Button>
                          </div>
                        </LuminarCard>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              )}

              {headerActions}
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-6">
          {children}
        </main>

        {/* Footer */}
        {footer && (
          <footer className="border-t border-gray-200 dark:border-gray-700 p-4 lg:p-6">
            {footer}
          </footer>
        )}
      </div>
    </div>
  );
}