import { motion, useAnimation } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes, useEffect } from "react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface ProgressBarProps extends HTMLAttributes<HTMLDivElement> {
  value: number
  max?: number
  variant?: ComponentVariant | "gradient" | "striped" | "glow"
  size?: ComponentSize | "md" | "lg"
  showLabel?: boolean
  animated?: boolean
  glass?: boolean
}

const ProgressBar = forwardRef<HTMLDivElement, ProgressBarProps>(
  ({ 
    value, 
    max = 100, 
    variant = defaultComponentProps.variant, 
    size = defaultComponentProps.size,
    showLabel = false,
    animated = true,
    glass = false,
    className,
    ...props 
  }, ref) => {
    const controls = useAnimation()
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100)

    useEffect(() => {
      if (animated) {
        controls.start({ width: `${percentage}%` })
      }
    }, [percentage, animated, controls])

    const sizeClasses = {
      xs: "h-1",
      sm: "h-2",
      md: "h-4",
      lg: "h-6"
    }

    const variantClasses = {
      default: "bg-primary",
      gradient: "bg-gradient-to-r from-purple-500 via-pink-500 to-red-500",
      striped: "bg-primary bg-stripes",
      glow: "bg-primary shadow-[0_0_15px_rgba(var(--primary),0.5)]"
    }

    const containerClasses = glass
      ? "backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30"
      : "bg-secondary"

    return (
      <div className={cn("relative", className)} {...props}>
        <div
          ref={ref}
          className={cn(
            "w-full rounded-full overflow-hidden",
            sizeClasses[size],
            containerClasses
          )}
        >
          <motion.div
            className={cn(
              "h-full rounded-full relative overflow-hidden",
              variantClasses[variant]
            )}
            initial={{ width: 0 }}
            animate={animated ? controls : { width: `${percentage}%` }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {variant === "striped" && (
              <motion.div
                className="absolute inset-0 opacity-20"
                animate={{ x: ["0%", "100%"] }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                style={{
                  backgroundImage: "linear-gradient(45deg, transparent 25%, rgba(255,255,255,0.3) 25%, rgba(255,255,255,0.3) 50%, transparent 50%, transparent 75%, rgba(255,255,255,0.3) 75%)",
                  backgroundSize: "20px 20px"
                }}
              />
            )}
          </motion.div>
        </div>
        
        {showLabel && (
          <motion.span
            className={cn(
              "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",
              "text-xs font-medium",
              percentage > 50 ? "text-white" : "text-foreground"
            )}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            {Math.round(percentage)}%
          </motion.span>
        )}
      </div>
    )
  }
)
ProgressBar.displayName = "ProgressBar"

export { ProgressBar }
export { ProgressBar as LuminarProgressBar }