import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes } from "react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface SkeletonProps extends HTMLAttributes<HTMLDivElement> {
  variant?: ComponentVariant | "text" | "circular" | "rectangular" | "glass"
  animation?: "pulse" | "wave" | "none"
  width?: string | number
  height?: string | number
  count?: number
}

const Skeleton = forwardRef<HTMLDivElement, SkeletonProps>(
  ({ 
    variant = defaultComponentProps.variant, 
    animation = "pulse",
    width,
    height,
    count = 1,
    className,
    ...props 
  }, ref) => {
    const baseClasses = cn(
      "relative overflow-hidden",
      variant === "glass" 
        ? "backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30"
        : "bg-muted"
    )

    const variantClasses = {
      default: "rounded-md",
      text: "rounded-md h-4 w-full",
      circular: "rounded-full aspect-square",
      rectangular: "rounded-lg",
      glass: "rounded-lg"
    }

    const animationElement = animation === "wave" && (
      <motion.div
        className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent"
        animate={{ x: ["0%", "200%"] }}
        transition={{ duration: 1.5, repeat: Infinity, ease: "linear" as const }}
      />
    )

    const pulseAnimation = animation === "pulse" ? {
      animate: { opacity: [0.5, 1, 0.5] },
      transition: { duration: 1.5, repeat: Infinity, ease: "easeInOut" as const }
    } : {}

    const renderSkeleton = () => {
      return (
        <motion.div
          className={cn(
            baseClasses,
            variantClasses[variant],
            className
          )}
          style={{ width, height }}
          {...pulseAnimation}
        >
          {animationElement}
        </motion.div>
      )
    }

    if (count > 1) {
      return (
        <div ref={ref} className="space-y-2">
          {Array.from({ length: count }).map((_, index) => (
            <div key={index}>{renderSkeleton()}</div>
          ))}
        </div>
      )
    }

    return renderSkeleton()
  }
)
Skeleton.displayName = "Skeleton"

const SkeletonCard = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "rounded-lg p-4 space-y-3",
          "backdrop-blur-md bg-white/10 dark:bg-gray-900/10",
          "border border-white/20 dark:border-gray-700/30",
          className
        )}
        {...props}
      >
        <Skeleton variant="circular" width={40} height={40} />
        <div className="space-y-2">
          <Skeleton variant="text" className="w-3/4" />
          <Skeleton variant="text" />
          <Skeleton variant="text" className="w-1/2" />
        </div>
      </div>
    )
  }
)
SkeletonCard.displayName = "SkeletonCard"

export { Skeleton, SkeletonCard }
export { Skeleton as LuminarSkeleton }