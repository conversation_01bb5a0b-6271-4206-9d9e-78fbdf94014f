import React, { createContext, useContext, useEffect, useState } from 'react';
import { IntegrationClient } from '../../../services/integration/integrationClient';
import { WebSocketClient } from '../../../services/integration/websocketClient';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface IntegrationContextValue {
  connected: boolean;
  loading: boolean;
  error: Error | null;
  integrations: Record<string, any>;
  syncIntegration: (name: string) => Promise<void>;
  configureIntegration: (name: string, config: any) => Promise<void>;
  sendMessage: (type: string, data: any) => Promise<void>;
}

const IntegrationContext = createContext<IntegrationContextValue | undefined>(undefined);

export interface IntegrationProviderProps {
  children: React.ReactNode;
  config?: {
    apiUrl?: string;
    wsUrl?: string;
    autoConnect?: boolean;
  };
}

export const IntegrationProvider: React.FC<IntegrationProviderProps> = ({
  children,
  config = {}
}) => {
  const [connected, setConnected] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [integrations, setIntegrations] = useState<Record<string, any>>({});

  const integrationClient = IntegrationClient.getInstance(config.apiUrl);
  const wsClient = WebSocketClient.getInstance(config.wsUrl);

  useEffect(() => {
    const initialize = async () => {
      try {
        setLoading(true);

        if (config.autoConnect !== false) {
          await wsClient.connect();
        }

        // Load initial integration statuses
        const statuses = await integrationClient.getAllStatuses();
        setIntegrations(statuses);
        setConnected(wsClient.isConnected());
        setError(null);
      } catch (err) {
        setError(err as Error);
        setConnected(false);
      } finally {
        setLoading(false);
      }
    };

    initialize();

    // Subscribe to WebSocket events
    const handleConnected = () => setConnected(true);
    const handleDisconnected = () => setConnected(false);
    const handleError = (err: Error) => setError(err);
    const handleIntegrationUpdate = (data: any) => {
      setIntegrations(prev => ({
        ...prev,
        [data.integration]: data.status
      }));
    };

    wsClient.on('connected', handleConnected);
    wsClient.on('disconnected', handleDisconnected);
    wsClient.on('error', handleError);
    wsClient.on('integration.update', handleIntegrationUpdate);

    return () => {
      wsClient.off('connected', handleConnected);
      wsClient.off('disconnected', handleDisconnected);
      wsClient.off('error', handleError);
      wsClient.off('integration.update', handleIntegrationUpdate);
    };
  }, [config.apiUrl, config.wsUrl, config.autoConnect]);

  const syncIntegration = async (name: string) => {
    try {
      setLoading(true);
      await integrationClient.syncIntegration(name);
      
      // Update local state
      const status = await integrationClient.getIntegrationStatus(name);
      setIntegrations(prev => ({
        ...prev,
        [name]: status
      }));
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const configureIntegration = async (name: string, config: any) => {
    try {
      await integrationClient.configureIntegration(name, config);
      
      // Update local state
      const status = await integrationClient.getIntegrationStatus(name);
      setIntegrations(prev => ({
        ...prev,
        [name]: status
      }));
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  };

  const sendMessage = async (type: string, data: any) => {
    if (!connected) {
      throw new Error('Not connected to integration service');
    }
    
    await wsClient.send(type, data);
  };

  const value: IntegrationContextValue = {
    connected,
    loading,
    error,
    integrations,
    syncIntegration,
    configureIntegration,
    sendMessage
  };

  return (
    <IntegrationContext.Provider value={value}>
      {children}
    </IntegrationContext.Provider>
  );
};

export const useIntegrationContext = () => {
  const context = useContext(IntegrationContext);
  if (!context) {
    throw new Error('useIntegrationContext must be used within IntegrationProvider');
  }
  return context;
};