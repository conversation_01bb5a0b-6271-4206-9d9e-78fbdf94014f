/**
 * Example Component: StandardizedPropsExample
 * 
 * This example demonstrates how to create a new component using the
 * standardized props interface system.
 */

import * as React from 'react';
import type { HTMLAttributes } from 'react';
import { motion } from 'framer-motion';
import { Calendar, Clock, User } from 'lucide-react';

import { cn } from '../../lib/utils';
import { createGlassStyles } from '../../lib/glass-utils';
import type {
  StandardComponentProps,
  ExtendedHTMLProps,
  ComponentSize,
  ComponentVariant
} from '../../types/component-props';
import { defaultComponentProps } from '../../types/component-props';

/**
 * Props for the EventCard component
 */
export interface EventCardProps extends
  StandardComponentProps,
  ExtendedHTMLProps<HTMLAttributes<HTMLDivElement>> {
  /**
   * Event title
   */
  title: string;
  
  /**
   * Event description
   */
  description?: string;
  
  /**
   * Event date
   */
  date: Date | string;
  
  /**
   * Event time
   */
  time?: string;
  
  /**
   * Event attendees
   */
  attendees?: number;
  
  /**
   * Featured event
   */
  featured?: boolean;
  
  /**
   * Click handler
   */
  onClick?: () => void;
}

/**
 * Get card classes based on props
 */
const getCardClasses = (
  variant: ComponentVariant = 'default',
  size: ComponentSize = 'md',
  featured: boolean = false,
  hoverable: boolean = true
) => {
  const baseClasses = [
    'relative overflow-hidden',
    'rounded-xl transition-all duration-300',
    'cursor-pointer select-none'
  ];

  // Size-based padding
  const sizeClasses: Record<ComponentSize, string> = {
    xs: 'p-3',
    sm: 'p-4',
    md: 'p-5',
    lg: 'p-6',
    xl: 'p-8'
  };

  // Variant styles
  const variantClasses: Record<ComponentVariant, string> = {
    default: 'bg-card border border-border',
    primary: 'bg-primary text-primary-foreground',
    secondary: 'bg-secondary text-secondary-foreground',
    outline: 'border-2 border-primary bg-transparent',
    ghost: 'bg-transparent hover:bg-accent',
    glass: 'backdrop-blur-md bg-white/10 border border-white/20',
    gradient: 'bg-gradient-to-br from-purple-600 to-pink-600 text-white',
    destructive: 'bg-destructive text-destructive-foreground',
    success: 'bg-green-100 dark:bg-green-900/20 text-green-900 dark:text-green-100',
    warning: 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-900 dark:text-yellow-100',
    info: 'bg-blue-100 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100'
  };

  // Hover effects
  const hoverClasses = hoverable ? [
    'hover:shadow-lg',
    'hover:scale-[1.02]',
    'active:scale-[0.98]'
  ] : [];

  // Featured styles
  const featuredClasses = featured ? [
    'ring-2 ring-primary ring-offset-2',
    'shadow-xl'
  ] : [];

  return cn(
    ...baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    ...hoverClasses,
    ...featuredClasses
  );
};

/**
 * EventCard Component
 * 
 * A card component for displaying events that fully implements
 * the standardized props interface.
 */
export const EventCard = React.forwardRef<HTMLDivElement, EventCardProps>(
  ({
    // Standard props
    className,
    id,
    style,
    'data-testid': dataTestId,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy,
    
    // Variant props
    variant = defaultComponentProps.variant,
    size = defaultComponentProps.size,
    
    // Glass props
    glass = defaultComponentProps.glass,
    glassIntensity = defaultComponentProps.glassIntensity,
    glassDepth,
    glassConfig,
    
    // Animation props
    animation = defaultComponentProps.animation,
    disableAnimation = defaultComponentProps.disableAnimation,
    animationDuration = 0.3,
    animationDelay = 0,
    motionProps,
    
    // Interactive props
    disabled = false,
    loading = false,
    interactive = defaultComponentProps.interactive,
    hoverable = defaultComponentProps.hoverable,
    
    // Component specific props
    title,
    description,
    date,
    time,
    attendees,
    featured = false,
    onClick,
    
    // Other props
    role,
    ...props
  }, ref) => {
    // Format date
    const formattedDate = date instanceof Date 
      ? date.toLocaleDateString()
      : date;

    // Get classes
    const cardClasses = getCardClasses(variant, size, featured, hoverable && !disabled);
    
    // Apply glass styles if enabled
    const glassStyles = glass ? createGlassStyles({
      element: 'card',
      profile: glassIntensity === 'subtle' ? 'soft' : 
               glassIntensity === 'intense' ? 'hard' : 'standard',
      interactive: interactive && !disabled,
      ...glassConfig
    }) : '';

    // Animation configuration
    const animationConfig = !disableAnimation && animation !== 'none' ? {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: -20 },
      transition: {
        duration: animationDuration,
        delay: animationDelay,
        ease: 'easeOut'
      },
      whileHover: hoverable && !disabled ? { y: -4 } : undefined,
      whileTap: interactive && !disabled ? { scale: 0.98 } : undefined,
      ...motionProps
    } : {};

    // Click handler
    const handleClick = () => {
      if (!disabled && !loading && onClick) {
        onClick();
      }
    };

    return (
      <motion.div
        ref={ref}
        id={id}
        className={cn(
          cardClasses,
          glass && glassStyles,
          disabled && 'opacity-50 cursor-not-allowed',
          loading && 'animate-pulse',
          className
        )}
        style={style}
        onClick={handleClick}
        data-testid={dataTestId}
        aria-label={ariaLabel || `Event: ${title}`}
        aria-describedby={ariaDescribedBy}
        aria-busy={loading}
        role={role || "article"}
        tabIndex={interactive && !disabled ? 0 : -1}
        {...animationConfig}
        {...props}
      >
        {/* Featured badge */}
        {featured && (
          <div className="absolute -top-1 -right-1">
            <div className="bg-primary text-primary-foreground text-xs font-bold px-3 py-1 rounded-bl-lg rounded-tr-lg">
              Featured
            </div>
          </div>
        )}

        {/* Event content */}
        <div className="space-y-3">
          {/* Title */}
          <h3 className={cn(
            "font-semibold",
            size === 'xs' && 'text-sm',
            size === 'sm' && 'text-base',
            size === 'md' && 'text-lg',
            size === 'lg' && 'text-xl',
            size === 'xl' && 'text-2xl'
          )}>
            {title}
          </h3>

          {/* Description */}
          {description && (
            <p className={cn(
              "text-muted-foreground",
              size === 'xs' && 'text-xs',
              size === 'sm' && 'text-sm',
              size === 'md' && 'text-base',
              size === 'lg' && 'text-lg',
              size === 'xl' && 'text-xl'
            )}>
              {description}
            </p>
          )}

          {/* Event details */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
            {/* Date */}
            <div className="flex items-center gap-1">
              <Calendar size={16} />
              <span>{formattedDate}</span>
            </div>

            {/* Time */}
            {time && (
              <div className="flex items-center gap-1">
                <Clock size={16} />
                <span>{time}</span>
              </div>
            )}

            {/* Attendees */}
            {attendees !== undefined && (
              <div className="flex items-center gap-1">
                <User size={16} />
                <span>{attendees} attendees</span>
              </div>
            )}
          </div>
        </div>

        {/* Loading overlay */}
        {loading && (
          <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
          </div>
        )}
      </motion.div>
    );
  }
);

EventCard.displayName = 'EventCard';

/**
 * Usage Examples:
 * 
 * Basic usage:
 * <EventCard 
 *   title="Team Meeting"
 *   date={new Date()}
 *   time="2:00 PM"
 * />
 * 
 * With all features:
 * <EventCard
 *   variant="gradient"
 *   size="lg"
 *   title="Annual Conference"
 *   description="Join us for our biggest event of the year"
 *   date="2024-06-15"
 *   time="9:00 AM - 5:00 PM"
 *   attendees={500}
 *   featured
 *   glass
 *   animation="slideUp"
 *   onClick={() => console.log('Event clicked')}
 * />
 * 
 * Different variants:
 * <EventCard variant="success" title="Workshop Complete" date={new Date()} />
 * <EventCard variant="warning" title="Deadline Approaching" date={new Date()} />
 * <EventCard variant="glass" glassIntensity="strong" title="Premium Event" date={new Date()} />
 */