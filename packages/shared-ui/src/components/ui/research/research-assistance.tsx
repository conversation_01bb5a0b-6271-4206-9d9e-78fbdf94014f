import { motion, AnimatePresence } from "framer-motion";
import { forwardRef, createContext, useContext, useEffect, useState } from "react";
import {
  MessageSquare, 
  Search, 
  FileText, 
  Quote, 
  Settings, 
  Maximize2, 
  Minimize2, 
  X,
  Menu,
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  Download,
  Share2,
  HelpCircle,
  Zap
} from "lucide-react";
import { cn } from '../../../lib/utils';
import { LuminarButton } from "../actions/button-advanced";
import { LuminarCard } from "../display/card";
import { LuminarText } from "../display/text";
import { LuminarTabs } from "../navigation/tabs";
import { LuminarTooltip } from "../feedback/tooltip";
import { LuminarDropdown } from "../actions/dropdown";
import { LuminarSwitch } from "../forms/switch";
import { LuminarSelect } from "../forms/select";
import { ResearchChat } from "./research-chat";
import { ResearchSearch } from "./research-search";
import { ResearchSources } from "./research-sources";
import { ResearchCitations } from "./research-citations";
import {
  getGlassClasses, 
  animationPresets, 
  transitions,
  type ComponentSize,
  type GlassIntensity,
  type ColorTheme
} from "../../../design-system";
import { useResearchState } from "../../../hooks/use-research-state";
import { ResearchContextType, ResearchExportData } from '../../../types/research';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

// Create context for research state
const ResearchContext = createContext<ResearchContextType | null>(null);

export interface LuminarResearchAssistanceProps {
  className?: string;
  size?: ComponentSize;
  defaultMode?: 'chat' | 'search' | 'split';
  glassIntensity?: GlassIntensity;
  colorTheme?: ColorTheme;
  showToolbar?: boolean;
  showSidebar?: boolean;
  collapsible?: boolean;
  resizable?: boolean;
  maxWidth?: string;
  maxHeight?: string;
  placeholder?: string;
  onModeChange?: (mode: 'chat' | 'search' | 'split') => void;
  onExport?: (data: ResearchExportData) => void;
  disabled?: boolean;
}

const LuminarResearchAssistance = forwardRef<HTMLDivElement, LuminarResearchAssistanceProps>(
  ({
    className,
    size = "md",
    defaultMode = "chat",
    glassIntensity = "medium",
    colorTheme = "neutral",
    showToolbar = true,
    showSidebar = true,
    collapsible = true,
    resizable = false,
    maxWidth = "1200px",
    maxHeight = "800px",
    placeholder = "Ask me anything about your research...",
    onModeChange,
    onExport,
    disabled = false,
    ...props
  }, ref) => {
    // Extract non-DOM props that might be passed from Storybook
    const {
      glassVariant,
      glassIntensity: storyGlassIntensity,
      ...domProps
    } = props as any;
    const research = useResearchState();
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [sidebarWidth, setSidebarWidth] = useState(300);
    const [isResizing, setIsResizing] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [showMobileMenu, setShowMobileMenu] = useState(false);

    // Handle responsive design
    useEffect(() => {
      const handleResize = () => {
        const mobile = window.innerWidth < 768;
        setIsMobile(mobile);
        if (mobile && research.state.sidebarOpen) {
          research.actions.toggleSidebar();
        }
      };

      handleResize();
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, [research.state.sidebarOpen, research.actions]);

    // Initialize mode
    useEffect(() => {
      if (defaultMode !== research.state.mode) {
        research.actions.setMode(defaultMode);
      }
    }, [defaultMode, research.state.mode, research.actions]);

    // Handle mode change
    useEffect(() => {
      onModeChange?.(research.state.mode);
    }, [research.state.mode, onModeChange]);

    // Handle export
    const handleExport = (format: 'json' | 'markdown' | 'pdf') => {
      const exportData: ResearchExportData = {
        title: `Research Session - ${new Date().toLocaleDateString()}`,
        createdAt: new Date(),
        messages: research.state.messages,
        sources: research.state.sources,
        citations: research.state.citations,
        metadata: {
          totalMessages: research.state.messages.length,
          totalSources: research.state.sources.length,
          totalCitations: research.state.citations.length,
          citationStyle: research.state.citationStyle,
          exportFormat: format
        }
      };

      onExport?.(exportData);

      // Default export behavior
      if (!onExport) {
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `research-session-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
      }
    };

    // Handle resize
    const handleMouseDown = (e: React.MouseEvent) => {
      if (!resizable) return;
      setIsResizing(true);
      e.preventDefault();
    };

    useEffect(() => {
      if (!isResizing) return;

      const handleMouseMove = (e: MouseEvent) => {
        const newWidth = Math.max(200, Math.min(500, e.clientX - 20));
        setSidebarWidth(newWidth);
      };

      const handleMouseUp = () => {
        setIsResizing(false);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }, [isResizing]);

    // Glass classes for container
    const containerGlassClasses = getGlassClasses('modal', {
      intensity: glassIntensity,
      depth: 'floating',
      animated: true,
      interactive: false
    });

    // Sidebar glass classes
    const sidebarGlassClasses = getGlassClasses('card', {
      intensity: 'light',
      depth: 'surface',
      animated: true,
      interactive: false
    });

    // Main content area
    const MainContent = () => {
      if (research.state.mode === 'split') {
        return (
          <div className="flex h-full">
            {/* Chat panel */}
            <div className="flex-1 border-r border-white/10">
              <ResearchChat
                messages={research.state.messages}
                currentMessage={research.state.currentMessage}
                isTyping={research.state.isTyping}
                onMessageChange={research.actions.setCurrentMessage}
                onSendMessage={research.actions.sendMessage}
                onClearChat={research.actions.clearChat}
                size={size}
                disabled={disabled}
                placeholder={placeholder}
              />
            </div>
            
            {/* Search panel */}
            <div className="flex-1">
              <ResearchSearch
                query={research.state.searchQuery}
                results={research.state.searchResults}
                searchHistory={research.state.searchHistory}
                isSearching={research.state.isSearching}
                onQueryChange={research.actions.setSearchQuery}
                onSearch={research.actions.search}
                onAddSource={research.actions.addSource}
                onSelectSource={research.actions.selectSource}
                onClearSearch={research.actions.clearSearch}
                size={size}
                disabled={disabled}
              />
            </div>
          </div>
        );
      }

      if (research.state.mode === 'search') {
        return (
          <ResearchSearch
            query={research.state.searchQuery}
            results={research.state.searchResults}
            searchHistory={research.state.searchHistory}
            isSearching={research.state.isSearching}
            onQueryChange={research.actions.setSearchQuery}
            onSearch={research.actions.search}
            onAddSource={research.actions.addSource}
            onSelectSource={research.actions.selectSource}
            onClearSearch={research.actions.clearSearch}
            size={size}
            disabled={disabled}
          />
        );
      }

      return (
        <ResearchChat
          messages={research.state.messages}
          currentMessage={research.state.currentMessage}
          isTyping={research.state.isTyping}
          onMessageChange={research.actions.setCurrentMessage}
          onSendMessage={research.actions.sendMessage}
          onClearChat={research.actions.clearChat}
          size={size}
          disabled={disabled}
          placeholder={placeholder}
        />
      );
    };

    // Sidebar content
    const SidebarContent = () => (
      <div className="flex flex-col h-full">
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between p-3 border-b border-white/10">
            {collapsible && (
              <LuminarButton
                size="xs"
                variant="ghost"
                onClick={research.actions.toggleSidebar}
              >
                <ChevronRight className="w-3 h-3" />
              </LuminarButton>
            )}
          </div>
          <LuminarTabs 
            tabs={[
              { 
                id: "sources", 
                label: "Sources", 
                content: (
                  <div className="flex-1 overflow-hidden">
                    <ResearchSources
                      sources={research.state.sources}
                      citations={research.state.citations}
                      selectedSource={research.state.selectedSource}
                      onSourceSelect={research.actions.selectSource}
                      onSourceRemove={research.actions.removeSource}
                      onCitationAdd={research.actions.addCitation}
                      onCitationRemove={research.actions.removeCitation}
                      citationStyle={research.state.citationStyle}
                      size={size}
                      showSearch={true}
                      showFilters={true}
                      collapsible={false}
                    />
                  </div>
                )
              },
              { 
                id: "citations", 
                label: "Citations", 
                content: (
                  <div className="flex-1 overflow-hidden">
                    <ResearchCitations
                      citations={research.state.citations}
                      sources={research.state.sources}
                      onCitationRemove={research.actions.removeCitation}
                      onCitationEdit={(id, updates) => {
                        // Handle citation edit
                        console.log('Edit citation:', id, updates);
                      }}
                      onCitationStyleChange={research.actions.updateCitationStyle}
                      onExportCitations={(format) => {
                        // Handle citation export
                        console.log('Export citations:', format);
                      }}
                      citationStyle={research.state.citationStyle}
                      size={size}
                      showSearch={true}
                      showGrouping={true}
                      collapsible={false}
                    />
                  </div>
                )
              }
            ]}
            defaultActiveTab="sources"
            className="flex-1"
          />
        </div>
      </div>
    );

    return (
      <ResearchContext.Provider value={research}>
        <motion.div
          ref={ref}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={transitions.spring}
          className={cn(
            "relative flex flex-col",
            containerGlassClasses,
            isFullscreen ? "fixed inset-0 z-50" : "rounded-lg",
            "w-full",
            className
          )}
          role="application"
          aria-label="Research Assistant"
          aria-describedby="research-assistant-description"
          style={{
            maxWidth: isFullscreen ? 'none' : maxWidth,
            maxHeight: isFullscreen ? 'none' : maxHeight,
            height: isFullscreen ? '100vh' : '100%',
            width: isFullscreen ? '100vw' : '100%'
          }}
          {...domProps}
        >
          {/* Hidden description for screen readers */}
          <div id="research-assistant-description" className="sr-only">
            Research assistant interface with chat, search, and citation management capabilities.
            Use Tab to navigate between elements. Press Enter to activate buttons.
          </div>

          {/* Toolbar */}
          {showToolbar && (
            <div className="flex items-center justify-between p-2 md:p-4 border-b border-white/10 flex-wrap gap-2">
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-2">
                  <Zap className="w-5 h-5 text-blue-400" />
                  <LuminarText text="Research Assistant" className="font-semibold text-sm md:text-base" />
                </div>
                
                {/* Mode selector */}
                <div className="flex items-center gap-1 ml-4">
                  <LuminarTooltip content="Chat mode">
                    <LuminarButton
                      size="sm"
                      variant={research.state.mode === 'chat' ? 'default' : 'ghost'}
                      onClick={() => research.actions.setMode('chat')}
                    >
                      <MessageSquare className="w-4 h-4" />
                    </LuminarButton>
                  </LuminarTooltip>
                  
                  <LuminarTooltip content="Search mode">
                    <LuminarButton
                      size="sm"
                      variant={research.state.mode === 'search' ? 'default' : 'ghost'}
                      onClick={() => research.actions.setMode('search')}
                    >
                      <Search className="w-4 h-4" />
                    </LuminarButton>
                  </LuminarTooltip>
                  
                  <LuminarTooltip content="Split mode">
                    <LuminarButton
                      size="sm"
                      variant={research.state.mode === 'split' ? 'default' : 'ghost'}
                      onClick={() => research.actions.setMode('split')}
                    >
                      <Menu className="w-4 h-4" />
                    </LuminarButton>
                  </LuminarTooltip>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* Export dropdown */}
                <LuminarDropdown
                  trigger={
                    <LuminarButton size="sm" variant="ghost">
                      <Download className="w-4 h-4" />
                    </LuminarButton>
                  }
                  items={[
                    { id: 'json', label: 'Export JSON', icon: FileText, onClick: () => handleExport('json') },
                    { id: 'markdown', label: 'Export Markdown', icon: FileText, onClick: () => handleExport('markdown') },
                    { id: 'pdf', label: 'Export PDF', icon: FileText, onClick: () => handleExport('pdf') }
                  ]}
                />

                {/* Settings dropdown */}
                <LuminarDropdown
                  trigger={
                    <LuminarButton size="sm" variant="ghost">
                      <Settings className="w-4 h-4" />
                    </LuminarButton>
                  }
                  items={[
                    { id: 'citation-style', label: 'Citation Style Settings', icon: Settings, onClick: () => {} },
                    { id: 'auto-save', label: 'Auto-save Settings', icon: Settings, onClick: () => {} },
                    { id: 'max-results', label: 'Max Results Settings', icon: Settings, onClick: () => {} }
                  ]}
                />

                {/* Sidebar toggle */}
                {showSidebar && collapsible && (
                  <LuminarButton
                    size="sm"
                    variant="ghost"
                    onClick={research.actions.toggleSidebar}
                  >
                    {research.state.sidebarOpen ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
                  </LuminarButton>
                )}

                {/* Fullscreen toggle */}
                <LuminarButton
                  size="sm"
                  variant="ghost"
                  onClick={() => setIsFullscreen(!isFullscreen)}
                >
                  {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
                </LuminarButton>
              </div>
            </div>
          )}

          {/* Main content area */}
          <div className="flex flex-1 overflow-hidden">
            {/* Main panel */}
            <div className="flex-1 overflow-hidden">
              <MainContent />
            </div>

            {/* Sidebar */}
            <AnimatePresence>
              {showSidebar && research.state.sidebarOpen && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{
                    duration: 0.3,
                    ease: "easeInOut" as const
                  }}
                  className={cn(
                    "border-l border-white/10 overflow-hidden",
                    sidebarGlassClasses
                  )}
                  style={{ width: sidebarWidth }}
                >
                  <SidebarContent />
                  
                  {/* Resize handle */}
                  {resizable && (
                    <div
                      className="absolute left-0 top-0 w-1 h-full cursor-col-resize hover:bg-blue-500/50 transition-colors"
                      onMouseDown={handleMouseDown}
                    />
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      </ResearchContext.Provider>
    );
  }
);
LuminarResearchAssistance.displayName = "LuminarResearchAssistance";

// Hook to use research context
export const useResearchContext = () => {
  const context = useContext(ResearchContext);
  if (!context) {
    throw new Error('useResearchContext must be used within a LuminarResearchAssistance');
  }
  return context;
};

export { LuminarResearchAssistance };