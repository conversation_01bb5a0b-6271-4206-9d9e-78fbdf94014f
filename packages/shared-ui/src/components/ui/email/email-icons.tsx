import * as React from "react"
import { motion } from "framer-motion"
import { cva, type VariantProps } from "class-variance-authority"
import { Mail, Send, Inbox, Star, Archive, Trash2, Reply, Forward, Paperclip, Shield, Flag, Eye, EyeOff, MoreHorizontal, CheckCircle, AlertCircle } from "lucide-react"

import { cn } from '../../../lib/utils'
import { getAdaptiveGlassClasses } from '../../../design-system'
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

const emailIconVariants = cva(
  "inline-flex items-center justify-center rounded-md transition-all duration-300 relative overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground hover:bg-accent hover:text-accent-foreground",
        glass: "backdrop-blur-md bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 text-gray-900 dark:text-white hover:bg-white/20 dark:hover:bg-white/10",
        primary: "bg-primary text-primary-foreground hover:bg-primary/90",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        success: "bg-green-500/20 text-green-700 dark:text-green-400 hover:bg-green-500/30",
        warning: "bg-yellow-500/20 text-yellow-700 dark:text-yellow-400 hover:bg-yellow-500/30",
        error: "bg-red-500/20 text-red-700 dark:text-red-400 hover:bg-red-500/30",
        info: "bg-blue-500/20 text-blue-700 dark:text-blue-400 hover:bg-blue-500/30"
      },
      size: {
        sm: "h-6 w-6 text-xs",
        md: "h-8 w-8 text-sm",
        lg: "h-10 w-10 text-base",
        xl: "h-12 w-12 text-lg"
      },
      state: {
        idle: "",
        active: "scale-110 shadow-lg",
        disabled: "opacity-50 cursor-not-allowed"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      state: "idle"
    }
  }
)

export interface EmailIconProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof emailIconVariants> {
  icon: 'mail' | 'send' | 'inbox' | 'star' | 'archive' | 'trash' | 'reply' | 'forward' | 'attachment' | 'secure' | 'flag' | 'read' | 'unread' | 'more' | 'sent' | 'draft' | 'spam'
  animated?: boolean
  pulse?: boolean
  glow?: boolean
  bounce?: boolean
  loading?: boolean
  count?: number
  showTooltip?: boolean
  tooltipText?: string
}

const iconMap = {
  mail: Mail,
  send: Send,
  inbox: Inbox,
  star: Star,
  archive: Archive,
  trash: Trash2,
  reply: Reply,
  forward: Forward,
  attachment: Paperclip,
  secure: Shield,
  flag: Flag,
  read: Eye,
  unread: EyeOff,
  more: MoreHorizontal,
  sent: CheckCircle,
  draft: AlertCircle,
  spam: Flag
}

const EmailIcon = React.forwardRef<HTMLButtonElement, EmailIconProps>(
  ({
    className,
    variant,
    size,
    state,
    icon,
    animated = true,
    pulse = false,
    glow = false,
    bounce = false,
    loading = false,
    count,
    showTooltip = false,
    tooltipText,
    children,
    ...props
  }, ref) => {
    const IconComponent = iconMap[icon] || Mail
    const [hovered, setIsHovered] = React.useState(false)
    const [isPressed, setIsPressed] = React.useState(false)

    const getAnimationVariants = () => {
      if (!animated) return {}
      
      return {
        initial: { scale: 1, rotate: 0 },
        hover: { 
          scale: 1.1, 
          rotate: icon === 'star' ? 15 : 0,
          transition: { type: "spring", stiffness: 400, damping: 17 }
        },
        tap: { 
          scale: 0.95,
          transition: { type: "spring", stiffness: 400, damping: 17 }
        }
      }
    }

    const getPulseAnimation = () => {
      if (!pulse) return {}
      return {
        scale: [1, 1.2, 1],
        opacity: [0.7, 1, 0.7],
        transition: { duration: 2, repeat: Infinity, ease: "easeInOut" }
      }
    }

    const getBounceAnimation = () => {
      if (!bounce) return {}
      return {
        y: [0, -10, 0],
        transition: { duration: 0.6, repeat: Infinity, ease: "easeInOut" }
      }
    }

    const getGlowEffect = () => {
      if (!glow) return null
      return (
        <motion.div
          className="absolute inset-0 rounded-md bg-gradient-to-r from-blue-400/0 via-blue-400/40 to-blue-400/0"
          animate={{
            opacity: [0, 0.8, 0],
            scale: [0.8, 1.1, 0.8],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      )
    }

    const getSpecialAnimations = () => {
      switch (icon) {
        case 'send':
          return hovered ? {
            x: [0, 3, 0],
            transition: { duration: 0.3, ease: "easeInOut" }
          } : {}
        case 'trash':
          return hovered ? {
            rotate: [-2, 2, -2, 2, 0],
            transition: { duration: 0.4, ease: "easeInOut" }
          } : {}
        case 'star':
          return hovered ? {
            rotate: [0, 360],
            transition: { duration: 0.5, ease: "easeInOut" }
          } : {}
        case 'mail':
          return hovered ? {
            y: [0, -2, 0],
            transition: { duration: 0.3, ease: "easeInOut" }
          } : {}
        default:
          return {}
      }
    }

    return (
      <div className="relative inline-block">
        <motion.button
          ref={ref}
          className={cn(
            emailIconVariants({ variant, size, state }),
            loading && "cursor-not-allowed",
            className
          )}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onMouseDown={() => setIsPressed(true)}
          onMouseUp={() => setIsPressed(false)}
          disabled={loading || state === 'disabled'}
          variants={getAnimationVariants() as any}
          initial="initial"
          whileHover="hover"
          whileTap="tap"
          animate={{
            ...getPulseAnimation(),
            ...getBounceAnimation(),
            ...getSpecialAnimations()
          } as any}
        >
          {getGlowEffect()}
          
          {/* Loading Spinner */}
          {loading && (
            <motion.div
              className="absolute inset-0 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                className="h-3 w-3 rounded-full border-2 border-current border-t-transparent"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
            </motion.div>
          )}

          {/* Icon */}
          <motion.div
            className="relative z-10"
            initial={{ opacity: 1 }}
            animate={{ opacity: loading ? 0 : 1 }}
            transition={{ duration: 0.2 }}
          >
            <IconComponent className="h-full w-full" />
          </motion.div>

          {/* Count Badge */}
          {count && count > 0 && (
            <motion.div
              className="absolute -top-1 -right-1 min-w-[1.25rem] h-5 px-1 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              {count > 99 ? '99+' : count}
            </motion.div>
          )}

          {/* Tooltip */}
          {showTooltip && tooltipText && hovered && (
            <motion.div
              className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap z-20"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.2 }}
            >
              {tooltipText}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900" />
            </motion.div>
          )}
        </motion.button>
      </div>
    )
  }
)
EmailIcon.displayName = "EmailIcon"

// Specialized Email Action Components
export const EmailActions = {
  Send: React.forwardRef<HTMLButtonElement, Omit<EmailIconProps, 'icon'>>((props, ref) => (
    <EmailIcon ref={ref} icon="send" {...props} />
  )),
  Reply: React.forwardRef<HTMLButtonElement, Omit<EmailIconProps, 'icon'>>((props, ref) => (
    <EmailIcon ref={ref} icon="reply" {...props} />
  )),
  Forward: React.forwardRef<HTMLButtonElement, Omit<EmailIconProps, 'icon'>>((props, ref) => (
    <EmailIcon ref={ref} icon="forward" {...props} />
  )),
  Archive: React.forwardRef<HTMLButtonElement, Omit<EmailIconProps, 'icon'>>((props, ref) => (
    <EmailIcon ref={ref} icon="archive" {...props} />
  )),
  Delete: React.forwardRef<HTMLButtonElement, Omit<EmailIconProps, 'icon'>>((props, ref) => (
    <EmailIcon ref={ref} icon="trash" variant="error" {...props} />
  )),
  Star: React.forwardRef<HTMLButtonElement, Omit<EmailIconProps, 'icon'>>((props, ref) => (
    <EmailIcon ref={ref} icon="star" variant="warning" {...props} />
  )),
  MarkRead: React.forwardRef<HTMLButtonElement, Omit<EmailIconProps, 'icon'>>((props, ref) => (
    <EmailIcon ref={ref} icon="read" variant="success" {...props} />
  )),
  MarkUnread: React.forwardRef<HTMLButtonElement, Omit<EmailIconProps, 'icon'>>((props, ref) => (
    <EmailIcon ref={ref} icon="unread" variant="info" {...props} />
  )),
  Flag: React.forwardRef<HTMLButtonElement, Omit<EmailIconProps, 'icon'>>((props, ref) => (
    <EmailIcon ref={ref} icon="flag" variant="error" {...props} />
  )),
  Secure: React.forwardRef<HTMLButtonElement, Omit<EmailIconProps, 'icon'>>((props, ref) => (
    <EmailIcon ref={ref} icon="secure" variant="success" {...props} />
  )),
  Attachment: React.forwardRef<HTMLButtonElement, Omit<EmailIconProps, 'icon'>>((props, ref) => (
    <EmailIcon ref={ref} icon="attachment" variant="info" {...props} />
  ))
}

// Email Status Indicators
export const EmailStatusIndicator = React.forwardRef<HTMLDivElement, {
  status: 'sent' | 'draft' | 'failed' | 'sending' | 'delivered' | 'read'
  size?: ComponentSize | 'md' | 'lg'
  animated?: boolean
  className?: string
}>(({ status, size = defaultComponentProps.size, animated = true, className }, ref) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'sent':
        return { icon: CheckCircle, color: 'text-green-500', bg: 'bg-green-100 dark:bg-green-900/20' }
      case 'draft':
        return { icon: AlertCircle, color: 'text-yellow-500', bg: 'bg-yellow-100 dark:bg-yellow-900/20' }
      case 'failed':
        return { icon: AlertCircle, color: 'text-red-500', bg: 'bg-red-100 dark:bg-red-900/20' }
      case 'sending':
        return { icon: Send, color: 'text-blue-500', bg: 'bg-blue-100 dark:bg-blue-900/20' }
      case 'delivered':
        return { icon: CheckCircle, color: 'text-blue-500', bg: 'bg-blue-100 dark:bg-blue-900/20' }
      case 'read':
        return { icon: Eye, color: 'text-purple-500', bg: 'bg-purple-100 dark:bg-purple-900/20' }
      default:
        return { icon: Mail, color: 'text-gray-500', bg: 'bg-gray-100 dark:bg-gray-900/20' }
    }
  }

  const config = getStatusConfig()
  const Icon = config.icon
  const sizeClass = size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-6 w-6' : 'h-5 w-5'

  return (
    <motion.div
      ref={ref}
      className={cn(
        "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
        config.bg,
        className
      )}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.2 }}
    >
      <motion.div
        animate={animated && status === 'sending' ? { rotate: 360 } : {}}
        transition={animated && status === 'sending' ? { duration: 1, repeat: Infinity, ease: "linear" } : {}}
      >
        <Icon className={cn(sizeClass, config.color)} />
      </motion.div>
      <span className={config.color}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    </motion.div>
  )
})
EmailStatusIndicator.displayName = "EmailStatusIndicator"

export { EmailIcon, emailIconVariants }