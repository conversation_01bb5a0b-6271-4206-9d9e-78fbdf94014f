import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence, useMotionValue, useSpring, type Variants } from 'framer-motion';
import { cn } from '../../../lib/utils';
import { createGlassStyles } from '../../../lib/glass-utils';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

// Types and interfaces
export type AMNAState = 'dormant' | 'processing' | 'communicating' | 'voice';

export interface CommunicationSync {
  isGenerating: boolean;
  currentText: string;
  emotionalTone?: 'neutral' | 'positive' | 'negative';
  wordsPerSecond?: number;
}

export interface VoiceVisualization {
  active: boolean;
  amplitude?: number[];
  frequency?: number[];
  duration?: number;
}

export interface AMNAProps {
  state?: AMNAState;
  size?: ComponentSize | 'md' | 'lg' | number;
  intensity?: 'subtle' | 'md' | 'strong';
  onStateChange?: (state: AMNAState) => void;
  communicationSync?: CommunicationSync;
  voiceVisualization?: VoiceVisualization;
  className?: string;
  style?: React.CSSProperties;
  autoTransition?: boolean;
  reducedMotion?: boolean;
}

// Size configurations
const sizeConfig = {
  sm: { diameter: 48, waveformHeight: 16 },
  md: { diameter: 64, waveformHeight: 20 },
  lg: { diameter: 80, waveformHeight: 24 },
};

// Animation variants for different states
const createAMNAVariants = (size: number, intensity: string): Variants => {
  const intensityMultiplier = {
    subtle: 0.5,
    medium: 1,
    strong: 1.5,
  }[intensity] || 1;

  return {
    dormant: {
      scale: [1, 1.02, 1],
      opacity: [0.8, 1, 0.8],
      filter: [
        'blur(0px) saturate(100%) hue-rotate(0deg)',
        'blur(0.5px) saturate(110%) hue-rotate(5deg)',
        'blur(0px) saturate(100%) hue-rotate(0deg)',
      ],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
    processing: {
      scale: [1, 1.05, 0.98, 1.03, 1],
      opacity: [0.9, 1, 0.85, 1, 0.9],
      filter: [
        'blur(0px) saturate(120%) hue-rotate(0deg)',
        'blur(1px) saturate(150%) hue-rotate(15deg)',
        'blur(0.5px) saturate(140%) hue-rotate(-10deg)',
        'blur(1px) saturate(160%) hue-rotate(20deg)',
        'blur(0px) saturate(120%) hue-rotate(0deg)',
      ],
      transition: {
        duration: 2 * intensityMultiplier,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
    communicating: {
      scale: 1,
      opacity: 1,
      filter: 'blur(0px) saturate(130%) hue-rotate(0deg)',
      transition: {
        duration: 0.3,
        ease: "easeOut",
      },
    },
    voice: {
      scale: 1,
      opacity: 1,
      filter: 'blur(0px) saturate(120%) hue-rotate(0deg)',
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  };
};

// Inner glow variants
const innerGlowVariants: Variants = {
  dormant: {
    opacity: [0.3, 0.6, 0.3],
    scale: [0.8, 1.1, 0.8],
    transition: {
      duration: 4,
      repeat: Infinity,
      ease: "easeInOut",
    },
  },
  processing: {
    opacity: [0.5, 0.9, 0.4, 0.8, 0.5],
    scale: [0.9, 1.3, 0.7, 1.2, 0.9],
    rotate: [0, 180, 360],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut",
    },
  },
  communicating: {
    opacity: 0.7,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
  voice: {
    opacity: 0.4,
    scale: 0.8,
    transition: {
      duration: 0.8,
      ease: "easeOut",
    },
  },
};

// Surface effects variants
const surfaceEffectsVariants: Variants = {
  dormant: {
    background: [
      'radial-gradient(circle at 30% 30%, rgba(255,255,255,0.2) 0%, transparent 50%)',
      'radial-gradient(circle at 70% 70%, rgba(255,255,255,0.2) 0%, transparent 50%)',
      'radial-gradient(circle at 30% 30%, rgba(255,255,255,0.2) 0%, transparent 50%)',
    ],
    transition: {
      duration: 6,
      repeat: Infinity,
      ease: "linear",
    },
  },
  processing: {
    background: [
      'radial-gradient(circle at 20% 50%, rgba(59,130,246,0.3) 0%, transparent 40%)',
      'radial-gradient(circle at 80% 50%, rgba(147,51,234,0.3) 0%, transparent 40%)',
      'radial-gradient(circle at 50% 20%, rgba(236,72,153,0.3) 0%, transparent 40%)',
      'radial-gradient(circle at 50% 80%, rgba(59,130,246,0.3) 0%, transparent 40%)',
      'radial-gradient(circle at 20% 50%, rgba(59,130,246,0.3) 0%, transparent 40%)',
    ],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: "linear",
    },
  },
  communicating: {
    background: 'radial-gradient(circle at 50% 50%, rgba(34,197,94,0.2) 0%, transparent 60%)',
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
  voice: {
    background: 'linear-gradient(90deg, rgba(59,130,246,0.2) 0%, rgba(147,51,234,0.2) 100%)',
    transition: {
      duration: 0.8,
      ease: "easeOut",
    },
  },
};

export const AMNA: React.FC<AMNAProps> = ({
  state = 'dormant',
  size = 'md',
  intensity = 'md',
  onStateChange,
  communicationSync,
  voiceVisualization,
  className,
  style,
  autoTransition = false,
  reducedMotion = false,
}) => {
  const [currentState, setCurrentState] = useState<AMNAState>(state);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const amnaRef = useRef<HTMLDivElement>(null);
  
  // Get size configuration
  const sizeValue = typeof size === 'number' ? size : sizeConfig[size].diameter;
  const waveformHeight = typeof size === 'number' ? size * 0.3 : sizeConfig[size].waveformHeight;

  // Neural color intelligence for adaptive colors
  // Basic color palette
  const currentPalette = {
    primary: '#3b82f6',
    secondary: '#8b5cf6', 
    accent: '#06b6d4'
  };

  // Communication pulse effect
  const communicationPulse = useMotionValue(1);
  const communicationSpring = useSpring(communicationPulse, {
    stiffness: 300,
    damping: 30,
  });

  // Handle state changes
  useEffect(() => {
    if (state !== currentState) {
      setIsTransitioning(true);
      setCurrentState(state);
      onStateChange?.(state);
    }
  }, [state, currentState, onStateChange]);

  // Handle communication sync
  useEffect(() => {
    if (currentState === 'communicating' && communicationSync?.isGenerating) {
      const interval = setInterval(() => {
        communicationPulse.set(communicationPulse.get() === 1 ? 1.1 : 1);
      }, 100);
      return () => clearInterval(interval);
    }
  }, [currentState, communicationSync?.isGenerating, communicationPulse]);

  // Auto transition logic
  useEffect(() => {
    if (!autoTransition) return;

    const transitionSequence = async () => {
      await new Promise(resolve => setTimeout(resolve, 3000));
      setCurrentState('processing');
      await new Promise(resolve => setTimeout(resolve, 2000));
      setCurrentState('communicating');
      await new Promise(resolve => setTimeout(resolve, 1500));
      setCurrentState('voice');
      await new Promise(resolve => setTimeout(resolve, 2000));
      setCurrentState('dormant');
    };

    if (currentState === 'dormant') {
      const timeout = setTimeout(transitionSequence, 2000);
      return () => clearTimeout(timeout);
    }
  }, [autoTransition, currentState]);

  // Animation variants
  const amnaVariants = createAMNAVariants(sizeValue, intensity);

  // Glass morphism configuration
  const glassConfig = {
    profile: 'standard' as const,
    element: 'control' as const,
    interactive: false,
    glow: true,
    frost: true,
    theme: 'neutral' as const,
    textSafe: true,
  };

  const glassClasses = createGlassStyles({
    element: 'div',
    profile: intensity === 'strong' ? 'hard' : 'soft',
    interactive: false
  });

  // Handle animation complete
  const handleAnimationComplete = useCallback(() => {
    setIsTransitioning(false);
  }, []);

  // Waveform visualization component
  const WaveformVisualization = () => {
    if (currentState !== 'voice') return null;

    const bars = Array.from({ length: 12 }, (_, i) => {
      const amplitude = voiceVisualization?.amplitude?.[i] || Math.random();
      const height = Math.max(0.2, amplitude) * waveformHeight;
      
      return (
        <motion.div
          key={i}
          className="bg-gradient-to-t from-blue-400 to-purple-400 rounded-full"
          style={{
            width: 3,
            minHeight: 4,
          }}
          animate={{
            height: [height * 0.5, height, height * 0.3, height * 0.8, height * 0.5],
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            delay: i * 0.1,
            ease: 'easeInOut',
          }}
        />
      );
    });

    return (
      <div className="flex items-center justify-center gap-1">
        {bars}
      </div>
    );
  };

  return (
    <div
      className={cn('relative flex items-center justify-center', className)}
      style={style}
    >
      {currentState === 'voice' ? (
        // Voice state: Just the waveform bars without container
        <motion.div
          className="flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.8 }}
          layout
          layoutId="amna-container"
        >
          <WaveformVisualization />
        </motion.div>
      ) : (
        // Other states: Normal orb with glassmorphism container
        <motion.div
          ref={amnaRef}
          className={cn(
            'relative overflow-hidden border border-white/10 rounded-full',
            glassClasses
          )}
          style={{
            width: sizeValue,
            height: sizeValue,
            willChange: 'transform, filter',
            backfaceVisibility: 'hidden',
            background: 'rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(10px)',
          }}
          variants={amnaVariants}
          animate={reducedMotion ? 'communicating' : currentState}
          onAnimationComplete={handleAnimationComplete}
          layout
          layoutId="amna-container"
          transition={{
            layout: {
              duration: 0.8,
              ease: [0.25, 0.46, 0.45, 0.94],
            },
          }}
        >
          {/* Inner glow layer */}
          <motion.div
            className="absolute inset-2 rounded-full"
            style={{
              background: currentPalette
                ? `radial-gradient(circle, ${currentPalette.primary}40 0%, ${currentPalette.accent}20 50%, transparent 100%)`
                : currentState === 'dormant'
                  ? 'radial-gradient(circle, rgba(99,102,241,0.4) 0%, rgba(168,85,247,0.2) 50%, transparent 100%)'
                  : currentState === 'processing'
                  ? 'radial-gradient(circle, rgba(59,130,246,0.5) 0%, rgba(147,51,234,0.3) 50%, transparent 100%)'
                  : currentState === 'communicating'
                  ? 'radial-gradient(circle, rgba(34,197,94,0.4) 0%, rgba(16,185,129,0.2) 50%, transparent 100%)'
                  : 'radial-gradient(circle, rgba(59,130,246,0.4) 0%, rgba(147,51,234,0.2) 50%, transparent 100%)',
            }}
            variants={innerGlowVariants}
            animate={reducedMotion ? 'communicating' : currentState}
          />

          {/* Surface effects layer */}
          <motion.div
            className="absolute inset-0 mix-blend-overlay rounded-full"
            variants={surfaceEffectsVariants}
            animate={reducedMotion ? 'communicating' : currentState}
          />

          {/* Communication pulse overlay */}
          {currentState === 'communicating' && communicationSync?.isGenerating && (
            <motion.div
              className="absolute inset-0 rounded-full border-2 border-green-400/30"
              style={{
                scale: communicationSpring,
              }}
              animate={{
                opacity: [0.3, 0.7, 0.3],
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
            />
          )}

          {/* State indicator for development */}
          {import.meta.env.DEV && (
            <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs opacity-50 pointer-events-none">
              {currentState}
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

// Display name for debugging
AMNA.displayName = 'AMNA';

export default AMNA;