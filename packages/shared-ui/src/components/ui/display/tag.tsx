import React from 'react';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface TagProps {
  children: React.ReactNode;
  variant?: ComponentVariant | 'secondary' | 'success' | 'warning' | 'error' | 'neutral';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  removable?: boolean;
  onRemove?: () => void;
  className?: string;
}

export function LuminarTag({ 
  children, 
  variant = 'neutral', 
  size = 'sm', 
  removable = false, 
  onRemove,
  className = '' 
}: TagProps) {
  const sizeClasses = {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-2.5 py-1.5 text-sm',
    md: 'px-3 py-2 text-base',
    lg: 'px-4 py-2.5 text-lg'
  };

  const variantClasses = {
    primary: 'bg-primary/20 text-primary border-primary/30',
    secondary: 'bg-secondary/80 text-secondary-foreground border-secondary',
    success: 'bg-green-50/80 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200/60 dark:border-green-700/60',
    warning: 'bg-yellow-50/80 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 border-yellow-200/60 dark:border-yellow-700/60',
    error: 'bg-red-50/80 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-red-200/60 dark:border-red-700/60',
    neutral: 'bg-muted text-muted-foreground border-border'
  };

  const glassClasses = getAdaptiveGlassClasses('button', 'light', variant);

  return (
    <span className={`${glassClasses} ${sizeClasses[size]} ${variantClasses[variant]} rounded-full border inline-flex items-center gap-1 font-medium ${className}`}>
      {children}
      {removable && (
        <button
          onClick={onRemove}
          className="ml-1 p-0.5 hover:bg-black/10 dark:hover:bg-white/10 rounded-full transition-colors"
        >
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </span>
  );
}