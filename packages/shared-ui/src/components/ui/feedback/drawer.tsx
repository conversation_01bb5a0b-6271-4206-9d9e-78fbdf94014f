import { motion, AnimatePresence } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes, useEffect, useRef } from "react"
import { X } from "lucide-react"
import { Button } from "../actions/button"
import { createPortal } from "react-dom"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface LuminarDrawerProps {
  open: boolean
  onClose: () => void
  position?: 'left' | 'right' | 'top' | 'bottom'
  size?: ComponentSize | 'md' | 'lg' | 'xl' | 'full'
  overlay?: boolean
  overlayClosable?: boolean
  closeButton?: boolean
  title?: React.ReactNode
  footer?: React.ReactNode
  glass?: boolean
  animated?: boolean
  persistent?: boolean
  className?: string
  children?: React.ReactNode
}

const sizeMap = {
  sm: {
    left: 'w-64',
    right: 'w-64',
    top: 'h-64',
    bottom: 'h-64'
  },
  md: {
    left: 'w-80',
    right: 'w-80',
    top: 'h-80',
    bottom: 'h-80'
  },
  lg: {
    left: 'w-96',
    right: 'w-96',
    top: 'h-96',
    bottom: 'h-96'
  },
  xl: {
    left: 'w-[448px]',
    right: 'w-[448px]',
    top: 'h-[448px]',
    bottom: 'h-[448px]'
  },
  full: {
    left: 'w-full',
    right: 'w-full',
    top: 'h-full',
    bottom: 'h-full'
  }
}

const positionClasses = {
  left: 'inset-y-0 left-0',
  right: 'inset-y-0 right-0',
  top: 'inset-x-0 top-0',
  bottom: 'inset-x-0 bottom-0'
}

const slideVariants = {
  left: {
    initial: { x: '-100%' },
    animate: { x: 0 },
    exit: { x: '-100%' }
  },
  right: {
    initial: { x: '100%' },
    animate: { x: 0 },
    exit: { x: '100%' }
  },
  top: {
    initial: { y: '-100%' },
    animate: { y: 0 },
    exit: { y: '-100%' }
  },
  bottom: {
    initial: { y: '100%' },
    animate: { y: 0 },
    exit: { y: '100%' }
  }
}

const LuminarDrawer = forwardRef<HTMLDivElement, LuminarDrawerProps>(
  ({ 
    open,
    onClose,
    position = 'right',
    size = defaultComponentProps.size,
    overlay = true,
    overlayClosable = true,
    closeButton = true,
    title,
    footer,
    glass = true,
    animated = true,
    persistent = false,
    className,
    children 
  }, ref) => {
    const drawerRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
      const handleEscape = (event: KeyboardEvent) => {
        if (event.key === 'Escape' && open && !persistent) {
          onClose()
        }
      }

      if (open) {
        document.addEventListener('keydown', handleEscape)
        document.body.style.overflow = 'hidden'
      }

      return () => {
        document.removeEventListener('keydown', handleEscape)
        document.body.style.overflow = ''
      }
    }, [open, onClose, persistent])

    useEffect(() => {
      if (open && drawerRef.current) {
        drawerRef.current.focus()
      }
    }, [open])

    const content = (
      <AnimatePresence>
        {open && (
          <>
            {/* Overlay */}
            {overlay && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className={cn(
                  "fixed inset-0 z-40",
                  glass && "backdrop-blur-sm bg-black/50",
                  !glass && "bg-black/50"
                )}
                onClick={overlayClosable && !persistent ? onClose : undefined}
                aria-hidden="true"
              />
            )}

            {/* Drawer */}
            <motion.div
              ref={drawerRef}
              variants={animated ? slideVariants[position] : {}}
              initial={animated ? "initial" : undefined}
              animate={animated ? "animate" : undefined}
              exit={animated ? "exit" : undefined}
              transition={{ 
                type: "spring", 
                stiffness: 300, 
                damping: 30,
                mass: 0.8
              }}
              className={cn(
                "fixed z-50 flex flex-col",
                positionClasses[position],
                sizeMap[size][position],
                glass && "backdrop-blur-md bg-white/10 border",
                !glass && "bg-gray-900 border",
                position === 'left' && "border-r border-white/20",
                position === 'right' && "border-l border-white/20",
                position === 'top' && "border-b border-white/20",
                position === 'bottom' && "border-t border-white/20",
                "shadow-2xl",
                className
              )}
              tabIndex={-1}
              role="dialog"
              aria-modal="true"
              aria-labelledby={title ? "drawer-title" : undefined}
            >
              {/* Header */}
              {(title || closeButton) && (
                <div className="flex items-center justify-between p-4 border-b border-white/10">
                  {title && (
                    <h2 id="drawer-title" className="text-lg font-semibold">
                      {title}
                    </h2>
                  )}
                  {closeButton && !persistent && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onClose}
                      className="ml-auto"
                      aria-label="Close drawer"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              )}

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-4">
                {children}
              </div>

              {/* Footer */}
              {footer && (
                <div className="p-4 border-t border-white/10">
                  {footer}
                </div>
              )}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    )

    // Use portal to render outside of parent DOM hierarchy
    if (typeof document !== 'undefined') {
      return createPortal(content, document.body)
    }

    return content
  }
)
LuminarDrawer.displayName = "LuminarDrawer"

export { LuminarDrawer }