import { motion, AnimatePresence } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes, useState } from "react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface Tab {
  id: string
  label: string | React.ReactNode
  content: React.ReactNode
  disabled?: boolean
}

export interface LuminarTabsProps {
  tabs: Tab[]
  defaultActiveTab?: string
  activeTab?: string
  variant?: ComponentVariant | "pills" | "underline"
  glass?: boolean
  onTabChange?: (tabId: string) => void
  onChange?: (tabId: string) => void
  className?: string
}

const LuminarTabs = forwardRef<HTMLDivElement, LuminarTabsProps>(
  ({ 
    tabs, 
    defaultActiveTab,
    activeTab: controlledActiveTab,
    variant = defaultComponentProps.variant,
    glass = true,
    onTabChange,
    onChange,
    className,
    ...props 
  }, ref) => {
    const [internalActiveTab, setInternalActiveTab] = useState(defaultActiveTab || tabs[0]?.id)
    const activeTab = controlledActiveTab ?? internalActiveTab
    
    const handleTabClick = (tabId: string, disabled?: boolean) => {
      if (disabled) return
      if (!controlledActiveTab) {
        setInternalActiveTab(tabId)
      }
      onTabChange?.(tabId)
      onChange?.(tabId)
    }

    const activeTabData = tabs.find(tab => tab.id === activeTab)

    const tabListClasses = {
      default: "border-b border-white/20 dark:border-gray-700/30",
      pills: "bg-white/5 dark:bg-gray-900/5 p-1 rounded-lg",
      underline: ""
    }

    const tabClasses = {
      default: "pb-3",
      pills: "px-4 py-2 rounded-md",
      underline: "pb-3"
    }

    return (
      <div ref={ref} className={cn("w-full", className)} {...props}>
        <div
          className={cn(
            "relative",
            glass && variant === "pills" && "backdrop-blur-md",
            tabListClasses[variant]
          )}
        >
          <div className="flex gap-2">
            {tabs.map((tab) => {
              const active = activeTab === tab.id
              
              return (
                <motion.button
                  key={tab.id}
                  className={cn(
                    "relative px-4 transition-colors",
                    tabClasses[variant],
                    tab.disabled 
                      ? "opacity-50 cursor-not-allowed" 
                      : "hover:text-primary cursor-pointer",
                    active && "text-primary",
                    variant === "pills" && active && glass && "bg-white/10 dark:bg-gray-700/20"
                  )}
                  onClick={() => handleTabClick(tab.id, tab.disabled)}
                  whileHover={!tab.disabled ? { scale: 1.05 } : {}}
                  whileTap={!tab.disabled ? { scale: 0.95 } : {}}
                >
                  <span className="relative z-10 font-medium">{tab.label}</span>
                  
                  {active && variant === "default" && (
                    <motion.div
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"
                      layoutId="activeTab"
                      transition={{ type: "spring", stiffness: 400, damping: 30 }}
                    />
                  )}
                  
                  {active && variant === "underline" && (
                    <motion.div
                      className="absolute bottom-0 left-1/2 -translate-x-1/2 w-8 h-1 bg-primary rounded-full"
                      layoutId="activeTab"
                      transition={{ type: "spring", stiffness: 400, damping: 30 }}
                    />
                  )}
                </motion.button>
              )
            })}
          </div>
        </div>
        
        <div className="mt-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
            >
              {activeTabData?.content}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    )
  }
)
LuminarTabs.displayName = "LuminarTabs"

export { LuminarTabs }