import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes } from "react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface LuminarAvatarProps extends HTMLAttributes<HTMLDivElement> {
  src?: string
  alt?: string
  fallback?: string
  size?: ComponentSize | "md" | "lg" | "xl"
  status?: "online" | "offline" | "away" | "busy"
  glass?: boolean
  animated?: boolean
}

const LuminarAvatar = forwardRef<HTMLDivElement, LuminarAvatarProps>(
  ({ 
    src,
    alt = "Avatar",
    fallback,
    size = "md",
    status,
    glass = true,
    animated = true,
    className,
    ...props 
  }, ref) => {
    const sizeClasses = {
      xs: "w-6 h-6 text-xs",
      sm: "w-8 h-8 text-xs",
      md: "w-10 h-10 text-sm",
      lg: "w-12 h-12 text-base",
      xl: "w-16 h-16 text-lg"
    }

    const statusSizeClasses = {
      xs: "w-1.5 h-1.5",
      sm: "w-2 h-2",
      md: "w-2.5 h-2.5",
      lg: "w-3 h-3",
      xl: "w-4 h-4"
    }

    const statusColors = {
      online: "bg-green-500",
      offline: "bg-gray-400",
      away: "bg-yellow-500",
      busy: "bg-red-500"
    }

    const getInitials = (name: string) => {
      return name
        .split(' ')
        .map(word => word[0])
        .join('')
        .toUpperCase()
        .slice(0, 2)
    }

    return (
      <motion.div
        ref={ref}
        className={cn(
          "relative inline-flex items-center justify-center rounded-full overflow-hidden",
          glass && "backdrop-blur-md bg-white/20 dark:bg-gray-900/20 border border-white/30 dark:border-gray-700/40",
          !glass && "bg-secondary",
          sizeClasses[size],
          className
        )}
        initial={animated ? { scale: 0, rotate: -180 } : {}}
        animate={animated ? { scale: 1, rotate: 0 } : {}}
        whileHover={animated ? { scale: 1.05 } : {}}
        transition={{ type: "spring", stiffness: 400, damping: 30 }}
      >
        {src ? (
          <img
            src={src}
            alt={alt}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Hide broken image and show fallback
              (e.target as HTMLImageElement).style.display = 'none'
            }}
          />
        ) : null}
        
        {(!src || fallback) && (
          <span className="font-medium">
            {fallback ? getInitials(fallback) : "?"}
          </span>
        )}
        
        {status && (
          <motion.span
            className={cn(
              "absolute bottom-0 right-0 rounded-full ring-2 ring-background",
              statusSizeClasses[size],
              statusColors[status]
            )}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            {status === "online" && (
              <motion.span
                className={cn(
                  "absolute inset-0 rounded-full",
                  statusColors[status]
                )}
                animate={{
                  scale: [1, 1.5, 1.5],
                  opacity: [1, 0, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity
                }}
              />
            )}
          </motion.span>
        )}
      </motion.div>
    )
  }
)
LuminarAvatar.displayName = "LuminarAvatar"

// Avatar Group Component
export interface LuminarAvatarGroupProps extends HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  max?: number
  size?: ComponentSize | "md" | "lg" | "xl"
  glass?: boolean
}

const LuminarAvatarGroup = forwardRef<HTMLDivElement, LuminarAvatarGroupProps>(
  ({ 
    children,
    max = 3,
    size = "md",
    glass = true,
    className,
    ...props 
  }, ref) => {
    const childrenArray = Array.isArray(children) ? children : [children]
    const visibleChildren = childrenArray.slice(0, max)
    const remainingCount = childrenArray.length - max

    const overlapClasses = {
      xs: "-ml-1.5",
      sm: "-ml-2",
      md: "-ml-3",
      lg: "-ml-4",
      xl: "-ml-5"
    }

    return (
      <div
        ref={ref}
        className={cn("flex items-center", className)}
        {...props}
      >
        {visibleChildren.map((child, index) => (
          <motion.div
            key={index}
            className={cn(index > 0 && overlapClasses[size], "relative")}
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: index * 0.1 }}
            style={{ zIndex: visibleChildren.length - index }}
          >
            {child}
          </motion.div>
        ))}
        
        {remainingCount > 0 && (
          <motion.div
            className={cn(overlapClasses[size], "relative")}
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: visibleChildren.length * 0.1 }}
          >
            <LuminarAvatar
              size={size}
              glass={glass}
              fallback={`+${remainingCount}`}
              animated={false}
            />
          </motion.div>
        )}
      </div>
    )
  }
)
LuminarAvatarGroup.displayName = "LuminarAvatarGroup"

export { LuminarAvatar, LuminarAvatarGroup }