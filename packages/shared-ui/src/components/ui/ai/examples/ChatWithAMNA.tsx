import React, { useState } from 'react';
import { AMNA } from '../AMNA';
import { useChatAMNA } from './hooks/useAMNAState';
import { cn } from '../../../../lib/utils';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../../types/component-props';
import { defaultComponentProps } from '../../../../types/component-props';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface ChatWithAMNAProps {
  className?: string;
}

/**
 * Example integration of AMNA with a chat interface
 * Demonstrates real-world usage patterns and state management for AMNA
 */
export const ChatWithAMNA: React.FC<ChatWithAMNAProps> = ({ className }) => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'Hello! I\'m <PERSON><PERSON>, your Adaptive Mind Neural Assistant. How can I help you today?',
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setIsLoading] = useState(false);

  // Use the specialized chat AMNA hook
  const amna = useChatAMNA({
    onUserMessage: () => console.log('User sent a message to AMNA'),
    onAIResponse: () => console.log('AMNA is responding'),
    processingDuration: 1500,
    communicationDuration: 2500,
  });

  const handleSendMessage = async () => {
    if (!inputValue.trim() || loading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Trigger user message processing
    await amna.handleUserMessage();

    // Simulate AMNA processing delay
    setTimeout(async () => {
      const amnaMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: generateAMNAResponse(userMessage.content),
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, amnaMessage]);
      
      // Trigger AMNA response animation
      await amna.handleAIResponse();
      setIsLoading(false);
    }, 1000);
  };

  const generateAMNAResponse = (userInput: string): string => {
    const responses = [
      "As your Adaptive Mind Neural Assistant, I find that fascinating! Let me process this information...",
      "I understand your query. My neural networks are analyzing the optimal response for you...",
      "Excellent question! My adaptive algorithms suggest the following approach...",
      "That's a thoughtful inquiry. Let me engage my neural pathways to provide you with insights...",
      "I see what you're asking. My mind is adapting to provide you with the most relevant information...",
      "Interesting perspective! My neural intelligence is processing multiple response vectors...",
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getAMNAStatus = () => {
    if (loading) {
      return amna.currentState === 'processing' ? 'Neural Processing...' : 'Generating Response...';
    }
    return 'Adaptive & Ready';
  };

  return (
    <div className={cn('flex flex-col h-96 max-w-md mx-auto bg-white rounded-lg shadow-lg overflow-hidden', className)}>
      {/* Header with AMNA */}
      <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-purple-50 to-blue-50 border-b">
        <AMNA
          state={amna.currentState}
          size="sm"
          intensity="medium"
          communicationSync={{
            isGenerating: loading && amna.currentState === 'communicating',
            currentText: 'AMNA is generating response...',
            emotionalTone: 'positive',
          }}
          className="flex-shrink-0"
        />
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-gray-900">AMNA</h3>
          <p className="text-xs text-gray-500 truncate">{getAMNAStatus()}</p>
        </div>
        <div className="flex gap-1">
          <button
            onClick={() => amna.triggerVoice()}
            className="p-1.5 rounded-full hover:bg-gray-100 transition-colors"
            title="Voice input with AMNA"
          >
            <svg className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
            </svg>
          </button>
          <button
            onClick={() => amna.reset()}
            className="p-1.5 rounded-full hover:bg-gray-100 transition-colors"
            title="Reset AMNA"
          >
            <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              'flex',
              message.role === 'user' ? 'justify-end' : 'justify-start'
            )}
          >
            <div
              className={cn(
                'max-w-xs px-3 py-2 rounded-lg text-sm',
                message.role === 'user'
                  ? 'bg-purple-500 text-white rounded-br-sm'
                  : 'bg-gradient-to-r from-blue-50 to-purple-50 text-gray-800 rounded-bl-sm border border-purple-100'
              )}
            >
              {message.content}
            </div>
          </div>
        ))}
        
        {loading && (
          <div className="flex justify-start">
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-purple-100 px-3 py-2 rounded-lg rounded-bl-sm text-sm text-gray-500 flex items-center gap-2">
              <div className="flex gap-1">
                {[0, 1, 2].map((i) => (
                  <div
                    key={i}
                    className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-pulse"
                    style={{ animationDelay: `${i * 0.2}s` }}
                  />
                ))}
              </div>
              AMNA is thinking...
            </div>
          </div>
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t bg-gradient-to-r from-purple-50/30 to-blue-50/30">
        <div className="flex gap-2">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Message AMNA..."
            disabled={loading}
            className="flex-1 px-3 py-2 border border-purple-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || loading}
            className="px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg text-sm font-medium hover:from-purple-600 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            Send
          </button>
        </div>
        <p className="text-xs text-gray-500 mt-2 text-center">
          Powered by AMNA - Adaptive Mind Neural Assistant
        </p>
      </div>
    </div>
  );
};

export default ChatWithAMNA;