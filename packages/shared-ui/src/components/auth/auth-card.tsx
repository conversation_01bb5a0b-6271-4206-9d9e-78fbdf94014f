import React from 'react';
import { cn } from '../../lib/utils';
import { LuminarCard } from '../../components/ui/display/card';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../types/component-props';
import { defaultComponentProps } from '../../types/component-props';

interface AuthCardProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  footer?: React.ReactNode;
  className?: string;
  size?: ComponentSize;
}

export function AuthCard({
  children,
  title,
  description,
  footer,
  className,
  size = 'md'
}: AuthCardProps) {
  return (
    <LuminarCard className={cn(
      "w-full max-w-md mx-auto p-6",
      size === 'sm' && "max-w-sm p-4",
      size === 'lg' && "max-w-lg p-8",
      size === 'xl' && "max-w-xl p-10",
      className
    )}>
      {(title || description) && (
        <div className="space-y-1 mb-6">
          {title && <h2 className="text-2xl font-bold">{title}</h2>}
          {description && <p className="text-muted-foreground">{description}</p>}
        </div>
      )}
      <div className="space-y-4">{children}</div>
      {footer && <div className="mt-6">{footer}</div>}
    </LuminarCard>
  );
}