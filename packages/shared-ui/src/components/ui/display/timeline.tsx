import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes } from "react"
import { Check, Clock, AlertCircle, X, Activity } from "lucide-react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface TimelineItem {
  id: string
  title: string
  description?: string
  date?: string | Date
  status?: 'completed' | 'active' | 'pending' | 'error' | 'cancelled'
  icon?: React.ElementType
  content?: React.ReactNode
  highlight?: boolean
}

export interface LuminarTimelineProps extends HTMLAttributes<HTMLDivElement> {
  items: TimelineItem[]
  orientation?: 'vertical' | 'horizontal'
  variant?: ComponentVariant | 'alternating' | 'centered' | 'compact'
  glass?: boolean
  animated?: boolean
  showConnector?: boolean
  activeIndex?: number
}

const statusConfig = {
  completed: {
    icon: Check,
    color: 'text-green-400 bg-green-400/20 border-green-400/50',
    connectorColor: 'bg-green-400'
  },
  active: {
    icon: Activity,
    color: 'text-blue-400 bg-blue-400/20 border-blue-400/50',
    connectorColor: 'bg-blue-400'
  },
  pending: {
    icon: Clock,
    color: 'text-gray-400 bg-gray-400/20 border-gray-400/50',
    connectorColor: 'bg-gray-400'
  },
  error: {
    icon: AlertCircle,
    color: 'text-red-400 bg-red-400/20 border-red-400/50',
    connectorColor: 'bg-red-400'
  },
  cancelled: {
    icon: X,
    color: 'text-orange-400 bg-orange-400/20 border-orange-400/50',
    connectorColor: 'bg-orange-400'
  }
}

const LuminarTimeline = forwardRef<HTMLDivElement, LuminarTimelineProps>((props, ref) => {
  const { 
    items,
    orientation = 'vertical',
    variant = defaultComponentProps.variant,
    glass = true,
    animated = true,
    showConnector = true,
    activeIndex,
    className,
    ...rest
  } = props

  const isHorizontal = orientation === 'horizontal'
  const isAlternating = variant === 'alternating' && !isHorizontal
  const isCentered = variant === 'centered' && !isHorizontal
  const isCompact = variant === 'compact'

  const formatDate = (date: string | Date | undefined) => {
    if (!date) return null
    const d = typeof date === 'string' ? new Date(date) : date
    return d.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    })
  }

  const containerClasses = cn(
    "relative",
    isHorizontal ? "flex items-center" : "space-y-8",
    className
  )

  const renderTimelineItem = (item: TimelineItem, index: number) => {
    const status = item.status || 'pending'
    const StatusIcon = item.icon || statusConfig[status].icon
    const active = activeIndex !== undefined ? index === activeIndex : status === 'active'
    const isLast = index === items.length - 1
    const isEven = index % 2 === 0

    const itemAnimation = animated ? {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      transition: { delay: index * 0.1 }
    } : {}

    const iconAnimation = animated && active ? {
      animate: { scale: [1, 1.2, 1] },
      transition: { duration: 2, repeat: Infinity }
    } : {}

    return (
      <motion.div
        key={item.id}
        className={cn(
          "relative",
          isHorizontal && "flex flex-col items-center",
          isAlternating && "flex items-center",
          isCentered && "flex items-center justify-center"
        )}
        {...itemAnimation}
      >
        {/* Connector Line */}
        {showConnector && !isLast && (
          <div
            className={cn(
              "absolute",
              statusConfig[status].connectorColor,
              isHorizontal ? "h-0.5 top-10 left-1/2 right-[-100%] w-full" : 
              isAlternating ? "w-0.5 top-10 bottom-0 left-1/2 -translate-x-1/2" :
              isCentered ? "w-0.5 top-10 bottom-0 left-1/2 -translate-x-1/2" :
              "w-0.5 left-5 top-10 bottom-0"
            )}
          />
        )}

        {/* Timeline Content */}
        <div className={cn(
          "flex items-start gap-4",
          isAlternating && (isEven ? "flex-row" : "flex-row-reverse"),
          isCentered && "w-full"
        )}>
          {/* Left Content (for alternating/centered) */}
          {(isAlternating || isCentered) && (
            <div className={cn(
              "flex-1",
              isAlternating && (isEven ? "text-right pr-8" : "text-left pl-8"),
              isCentered && "text-right pr-8"
            )}>
              {((isAlternating && isEven) || isCentered) && (
                <div>
                  {!isCompact && item.date && (
                    <p className="text-sm text-gray-400 mb-1">{formatDate(item.date)}</p>
                  )}
                  <h3 className="font-semibold text-lg">{item.title}</h3>
                  {item.description && (
                    <p className="text-sm text-gray-400 mt-1">{item.description}</p>
                  )}
                  {item.content && (
                    <div className="mt-3">{item.content}</div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Icon */}
          <motion.div
            className={cn(
              "relative z-10 flex items-center justify-center",
              "w-10 h-10 rounded-full border-2",
              statusConfig[status].color,
              glass && "backdrop-blur-sm",
              item.highlight && "ring-4 ring-white/20"
            )}
            {...iconAnimation}
          >
            <StatusIcon className="w-5 h-5" />
          </motion.div>

          {/* Right Content (default, alternating odd, centered) */}
          <div className={cn(
            "flex-1",
            !isAlternating && !isCentered && "flex-1",
            isAlternating && (isEven ? "text-left pl-8" : "text-right pr-8"),
            isCentered && "text-left pl-8"
          )}>
            {(variant === 'default' || (isAlternating && !isEven) || isCentered) && (
              <div className={cn(
                glass && !isCompact && "backdrop-blur-sm bg-white/5 border border-white/10 rounded-lg p-4"
              )}>
                {!isCompact && item.date && (
                  <p className="text-sm text-gray-400 mb-1">{formatDate(item.date)}</p>
                )}
                <h3 className={cn(
                  "font-semibold",
                  isCompact ? "text-base" : "text-lg"
                )}>{item.title}</h3>
                {item.description && (
                  <p className={cn(
                    "text-gray-400 mt-1",
                    isCompact ? "text-xs" : "text-sm"
                  )}>{item.description}</p>
                )}
                {item.content && !isCompact && (
                  <div className="mt-3">{item.content}</div>
                )}
              </div>
            )}
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <div ref={ref} className={containerClasses} {...rest}>
      {items.map((item, index) => renderTimelineItem(item, index))}
    </div>
  )
})
LuminarTimeline.displayName = "LuminarTimeline"

export { LuminarTimeline }