/**
 * Standardized Component Props Interface System
 * 
 * This file contains the base interfaces and types that all Luminar UI components should implement.
 * It provides a consistent API surface across all components.
 */

import type { ReactNode, HTMLAttributes, RefObject, CSSProperties, ChangeEventHandler, FocusEventHandler } from 'react';
import type { MotionProps } from 'framer-motion';
import type { LucideIcon } from 'lucide-react';
import type { ColorTheme } from '../design-system';

// =============================================================================
// Core Types
// =============================================================================

/**
 * Standard size scale used across all components
 * 
 * @example
 * ```tsx
 * <Button size="sm">Small Button</Button>
 * <Button size="lg">Large Button</Button>
 * ```
 */
export type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

/**
 * Standard variant types for components
 * 
 * @example
 * ```tsx
 * <Button variant="primary">Primary Button</Button>
 * <Button variant="outline">Outline Button</Button>
 * <Button variant="glass">Glass Button</Button>
 * ```
 */
export type ComponentVariant = 
  | 'default' 
  | 'primary' 
  | 'secondary' 
  | 'outline' 
  | 'ghost' 
  | 'glass' 
  | 'gradient'
  | 'destructive'
  | 'success'
  | 'warning'
  | 'info';

/**
 * Glass effect intensity levels
 */
export type GlassIntensity = 'subtle' | 'light' | 'medium' | 'strong' | 'intense';

/**
 * Glass depth levels for layering
 */
export type GlassDepth = 'surface' | 'elevated' | 'floating' | 'modal';

/**
 * Animation presets available in the design system
 */
export type AnimationPreset = 
  | 'fadeIn' 
  | 'slideUp' 
  | 'slideDown' 
  | 'slideLeft' 
  | 'slideRight'
  | 'scale' 
  | 'rotate' 
  | 'bounce' 
  | 'pulse'
  | 'wiggle'
  | 'none';

/**
 * Loading states for async components
 */
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

/**
 * Color themes available in the design system
 * @deprecated Use ColorTheme from design-system.ts instead
 */
export type ColorTheme = 'neutral' | 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'error' | 'info';

// =============================================================================
// Base Component Props
// =============================================================================

/**
 * Base props that every Luminar component should accept
 * 
 * @example
 * ```tsx
 * <Button 
 *   className="custom-button"
 *   id="submit-btn"
 *   data-testid="submit-button"
 *   aria-label="Submit form"
 * >
 *   Submit
 * </Button>
 * ```
 */
export interface BaseComponentProps {
  /**
   * Additional CSS classes to apply
   * @example "bg-blue-500 text-white"
   */
  className?: string;
  
  /**
   * Component ID for testing and accessibility
   * @example "submit-button"
   */
  id?: string;
  
  /**
   * Inline styles (use sparingly, prefer className)
   * @example { backgroundColor: 'blue', color: 'white' }
   */
  style?: CSSProperties;
  
  /**
   * Data attributes for testing
   * @example "submit-button"
   */
  'data-testid'?: string;
  
  /**
   * Accessibility label
   * @example "Submit form"
   */
  'aria-label'?: string;
  
  /**
   * Accessibility description
   * @example "button-description"
   */
  'aria-describedby'?: string;
}

/**
 * Props for components that support variants
 */
export interface VariantProps {
  /**
   * Visual variant of the component
   */
  variant?: ComponentVariant;
  
  /**
   * Size of the component
   */
  size?: ComponentSize;
}

/**
 * Props for components with glass morphism effects
 */
export interface GlassProps {
  /**
   * Enable glass morphism effect
   */
  glass?: boolean;
  
  /**
   * Intensity of the glass effect
   */
  glassIntensity?: GlassIntensity;
  
  /**
   * Depth level for layering
   */
  glassDepth?: GlassDepth;
  
  /**
   * Custom glass configuration
   */
  glassConfig?: {
    blur?: number;
    opacity?: number;
    saturation?: number;
    brightness?: number;
  };
}

/**
 * Props for animated components
 */
export interface AnimationProps {
  /**
   * Animation preset to use
   */
  animation?: AnimationPreset;
  
  /**
   * Disable all animations
   */
  disableAnimation?: boolean;
  
  /**
   * Animation duration in seconds
   */
  animationDuration?: number;
  
  /**
   * Animation delay in seconds
   */
  animationDelay?: number;
  
  /**
   * Custom Framer Motion props
   */
  motionProps?: MotionProps;
}

/**
 * Props for interactive components
 */
export interface InteractiveProps {
  /**
   * Whether the component is disabled
   */
  disabled?: boolean;
  
  /**
   * Whether the component is in a loading state
   */
  loading?: boolean;
  
  /**
   * Loading text to display
   */
  loadingText?: string;
  
  /**
   * Whether the component is interactive
   */
  interactive?: boolean;
  
  /**
   * Whether the component shows hover effects
   */
  hoverable?: boolean;
}

/**
 * Props for components with state
 */
export interface StateProps {
  /**
   * Current loading state
   */
  loadingState?: LoadingState;
  
  /**
   * Error message to display
   */
  error?: string | null;
  
  /**
   * Success message to display
   */
  success?: string | null;
  
  /**
   * Warning message to display
   */
  warning?: string | null;
  
  /**
   * Info message to display
   */
  info?: string | null;
}

/**
 * Props for components with icons
 */
export interface IconProps {
  /**
   * Icon component to render
   */
  icon?: LucideIcon;
  
  /**
   * Position of the icon
   */
  iconPosition?: 'left' | 'right' | 'top' | 'bottom';
  
  /**
   * Size of the icon (defaults to component size)
   */
  iconSize?: number;
  
  /**
   * Icon animation
   */
  iconAnimation?: AnimationPreset;
}

/**
 * Props for components with themes
 */
export interface ThemeProps {
  /**
   * Color theme to apply
   */
  colorTheme?: ColorTheme;
  
  /**
   * Force a specific color mode
   */
  colorMode?: 'light' | 'dark' | 'auto';
}

/**
 * Props for components that can have children
 */
export interface ChildrenProps {
  /**
   * Child elements to render
   */
  children?: ReactNode;
}

/**
 * Props for form components
 */
export interface FormComponentProps<T = string> {
  /**
   * Current value
   */
  value?: T;
  
  /**
   * Default value
   */
  defaultValue?: T;
  
  /**
   * Change handler - accepts either value or event
   */
  onChange?: (value: T) => void;
  
  /**
   * Blur handler
   */
  onBlur?: () => void;
  
  /**
   * Focus handler
   */
  onFocus?: () => void;
  
  /**
   * Field name for forms
   */
  name?: string;
  
  /**
   * Whether the field is required
   */
  required?: boolean;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
  
  /**
   * Field label
   */
  label?: string;
  
  /**
   * Help text
   */
  helperText?: string;
  
  /**
   * Error message
   */
  error?: string;
  
  /**
   * Whether to show the error
   */
  showError?: boolean;
}

/**
 * Props for layout components
 */
export interface LayoutProps {
  /**
   * Spacing around the component
   */
  spacing?: ComponentSize | number;
  
  /**
   * Padding inside the component
   */
  padding?: ComponentSize | number;
  
  /**
   * Margin around the component
   */
  margin?: ComponentSize | number;
  
  /**
   * Gap between child elements
   */
  gap?: ComponentSize | number;
  
  /**
   * Maximum width constraint
   */
  maxWidth?: string | number;
  
  /**
   * Maximum height constraint
   */
  maxHeight?: string | number;
  
  /**
   * Whether to center the content
   */
  centered?: boolean;
}

/**
 * Props for performance optimization
 */
export interface PerformanceProps {
  /**
   * Enable performance monitoring
   */
  enablePerformanceMonitoring?: boolean;
  
  /**
   * Lazy load the component
   */
  lazyLoad?: boolean;
  
  /**
   * Virtualize long lists
   */
  virtualize?: boolean;
  
  /**
   * Debounce delay for inputs
   */
  debounceDelay?: number;
}

// =============================================================================
// Composite Props Types
// =============================================================================

/**
 * Standard props for UI components (buttons, cards, etc.)
 */
export interface StandardComponentProps extends 
  BaseComponentProps,
  VariantProps,
  AnimationProps,
  InteractiveProps,
  GlassProps,
  ThemeProps {}

/**
 * Standard props for form components (inputs, selects, etc.)
 */
export interface StandardFormComponentProps<T = string> extends
  BaseComponentProps,
  VariantProps,
  AnimationProps,
  InteractiveProps,
  GlassProps,
  FormComponentProps<T> {}

/**
 * Standard props for layout components
 */
export interface StandardLayoutProps extends
  BaseComponentProps,
  AnimationProps,
  LayoutProps,
  ChildrenProps {}

/**
 * Standard props for feedback components (alerts, toasts, etc.)
 */
export interface StandardFeedbackProps extends
  BaseComponentProps,
  VariantProps,
  AnimationProps,
  StateProps,
  IconProps {}

// =============================================================================
// Utility Types
// =============================================================================

/**
 * Make certain props required
 */
export type RequiredProps<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * Omit HTML attributes that conflict with our props
 */
export type SafeHTMLProps<T> = Omit<T, 
  | 'className' 
  | 'style' 
  | 'children' 
  | 'onChange' 
  | 'onBlur' 
  | 'onFocus'
  | 'disabled'
  | 'loading'
  | 'size'
  | 'id'
>;

/**
 * Props for components that extend HTML elements
 */
export type ExtendedHTMLProps<T extends HTMLAttributes<any>, P = {}> = 
  SafeHTMLProps<T> & P;

/**
 * Props with ref support
 */
export interface RefProps<T> {
  /**
   * Ref to the underlying element
   */
  ref?: RefObject<T>;
}

// =============================================================================
// Component-Specific Base Props
// =============================================================================

/**
 * Base props for button components
 */
export interface ButtonBaseProps extends StandardComponentProps, IconProps {
  /**
   * Button type
   */
  type?: 'button' | 'submit' | 'reset';
  
  /**
   * Click handler
   */
  onClick?: () => void;
  
  /**
   * Loading state with spinner
   */
  loading?: boolean;
  
  /**
   * Full width button
   */
  fullWidth?: boolean;
}

/**
 * Base props for input components - supports HTML input events
 */
export interface InputBaseProps extends 
  Omit<StandardFormComponentProps<string>, 'onChange' | 'onBlur' | 'onFocus'> {
  /**
   * Input type
   */
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  
  /**
   * Auto-complete behavior
   */
  autoComplete?: string;
  
  /**
   * Pattern for validation
   */
  pattern?: string;
  
  /**
   * Minimum length
   */
  minLength?: number;
  
  /**
   * Maximum length
   */
  maxLength?: number;
  
  /**
   * Change handler - supports both value and HTML event patterns
   */
  onChange?: ((value: string) => void) | ChangeEventHandler<HTMLInputElement>;
  
  /**
   * Blur handler - supports both simple and HTML event patterns  
   */
  onBlur?: (() => void) | FocusEventHandler<HTMLInputElement>;
  
  /**
   * Focus handler - supports both simple and HTML event patterns
   */
  onFocus?: (() => void) | FocusEventHandler<HTMLInputElement>;
}

/**
 * Base props for card components
 */
export interface CardBaseProps extends StandardComponentProps, LayoutProps {
  /**
   * Card elevation/shadow
   */
  elevation?: 0 | 1 | 2 | 3 | 4 | 5;
  
  /**
   * Whether the card is clickable
   */
  clickable?: boolean;
  
  /**
   * Click handler for clickable cards
   */
  onClick?: () => void;
  
  /**
   * Whether to show a border
   */
  bordered?: boolean;
}

/**
 * Base props for modal/dialog components
 */
export interface ModalBaseProps extends StandardComponentProps {
  /**
   * Whether the modal is open
   */
  open?: boolean;
  
  /**
   * Handler for closing the modal
   */
  onClose?: () => void;
  
  /**
   * Modal title
   */
  title?: ReactNode;
  
  /**
   * Modal description
   */
  description?: ReactNode;
  
  /**
   * Whether to show close button
   */
  showCloseButton?: boolean;
  
  /**
   * Whether to close on overlay click
   */
  closeOnOverlayClick?: boolean;
  
  /**
   * Whether to close on Escape key
   */
  closeOnEscape?: boolean;
}

// =============================================================================
// Export type guards and utilities
// =============================================================================

/**
 * Check if a component has variant props
 */
export const hasVariantProps = (props: any): props is VariantProps => {
  return 'variant' in props || 'size' in props;
};

/**
 * Check if a component has glass props
 */
export const hasGlassProps = (props: any): props is GlassProps => {
  return 'glass' in props || 'glassIntensity' in props;
};

/**
 * Check if a component has animation props
 */
export const hasAnimationProps = (props: any): props is AnimationProps => {
  return 'animation' in props || 'disableAnimation' in props;
};

/**
 * Default props for components
 */
export const defaultComponentProps: Partial<StandardComponentProps> = {
  variant: 'default',
  size: 'md',
  animation: 'fadeIn',
  disableAnimation: false,
  glass: false,
  glassIntensity: 'medium',
  interactive: true,
  hoverable: true,
};

/**
 * Default props for form components
 */
export const defaultFormProps: Partial<StandardFormComponentProps> = {
  ...defaultComponentProps,
  showError: true,
  required: false,
};

/**
 * Size to pixel mapping
 */
export const sizeToPixels: Record<ComponentSize, number> = {
  xs: 24,
  sm: 32,
  md: 40,
  lg: 48,
  xl: 56,
};

/**
 * Size to spacing mapping
 */
export const sizeToSpacing: Record<ComponentSize, string> = {
  xs: '0.25rem',
  sm: '0.5rem',
  md: '0.75rem',
  lg: '1rem',
  xl: '1.5rem',
};