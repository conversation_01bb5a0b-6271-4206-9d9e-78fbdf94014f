import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface BarChartData {
  label: string;
  value: number;
  color?: string;
}

export interface AnimatedBarChartProps {
  data: BarChartData[];
  width?: number;
  height?: number;
  orientation?: 'vertical' | 'horizontal';
  showGrid?: boolean;
  showLabels?: boolean;
  showValues?: boolean;
  animate?: boolean;
  animationDuration?: number;
  staggerDelay?: number;
  barStyle?: 'solid' | 'gradient' | 'glass';
  className?: string;
}

export function LuminarAnimatedBarChart({
  data,
  width = 600,
  height = 300,
  orientation = 'vertical',
  showGrid = true,
  showLabels = true,
  showValues = true,
  animate = true,
  animationDuration = 0.5,
  staggerDelay = 0.1,
  barStyle = 'gradient',
  className = ''
}: AnimatedBarChartProps) {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const glassClasses = getAdaptiveGlassClasses('card', 'md', 'neutral');

  // Chart dimensions
  const padding = { top: 20, right: 20, bottom: 40, left: 50 };
  const chartWidth = width - padding.left - padding.right;
  const chartHeight = height - padding.top - padding.bottom;

  // Calculate scales
  const maxValue = Math.max(...data.map(d => d.value));
  const barWidth = chartWidth / data.length * 0.7;
  const barSpacing = chartWidth / data.length * 0.3;

  // Default colors
  const defaultColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

  const getBarColor = (item: BarChartData, index: number) => {
    return item.color || defaultColors[index % defaultColors.length];
  };

  return (
    <div className={`${glassClasses} p-4 rounded-lg border ${className}`}>
      <svg width={width} height={height}>
        <defs>
          {data.map((item, index) => {
            const color = getBarColor(item, index);
            return (
              <linearGradient key={`gradient-${index}`} id={`barGradient-${index}`} x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={color} stopOpacity="0.9" />
                <stop offset="100%" stopColor={color} stopOpacity="0.6" />
              </linearGradient>
            );
          })}
        </defs>

        <g transform={`translate(${padding.left},${padding.top})`}>
          {/* Grid lines */}
          {showGrid && (
            <g className="grid">
              {[0, 0.25, 0.5, 0.75, 1].map((tick, i) => {
                const y = chartHeight - (tick * chartHeight);
                const value = tick * maxValue;
                return (
                  <g key={i}>
                    <motion.line
                      x1={0}
                      y1={y}
                      x2={chartWidth}
                      y2={y}
                      stroke="var(--border)"
                      strokeWidth="1"
                      opacity="0.3"
                      initial={{ opacity: 0, scaleX: 0 }}
                      animate={{ opacity: 0.3, scaleX: 1 }}
                      transition={{ delay: i * 0.1 }}
                    />
                    <motion.text
                      x={-10}
                      y={y + 4}
                      textAnchor="end"
                      fontSize="10"
                      fill="currentColor"
                      className="text-muted-foreground"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: i * 0.1 }}
                    >
                      {value.toFixed(0)}
                    </motion.text>
                  </g>
                );
              })}
            </g>
          )}

          {/* Bars */}
          {data.map((item, index) => {
            const barHeight = (item.value / maxValue) * chartHeight;
            const x = index * (barWidth + barSpacing) + barSpacing / 2;
            const y = chartHeight - barHeight;
            const hovered = hoveredIndex === index;
            const color = getBarColor(item, index);

            return (
              <g key={index}>
                {/* Bar */}
                <motion.rect
                  x={x}
                  y={y}
                  width={barWidth}
                  height={barHeight}
                  fill={barStyle === 'gradient' ? `url(#barGradient-${index})` : color}
                  fillOpacity={barStyle === 'glass' ? 0.3 : 1}
                  stroke={barStyle === 'glass' ? color : 'none'}
                  strokeWidth={barStyle === 'glass' ? 2 : 0}
                  rx="4"
                  initial={{ scaleY: 0 }}
                  animate={{ scaleY: 1 }}
                  transition={{
                    duration: animationDuration,
                    delay: index * staggerDelay,
                    ease: [0.16, 1, 0.3, 1]
                  }}
                  style={{ transformOrigin: 'bottom' }}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                  className={`cursor-pointer ${barStyle === 'glass' ? 'backdrop-blur-sm' : ''}`}
                />

                {/* Hover effect */}
                <AnimatePresence>
                  {hovered && (
                    <motion.rect
                      x={x - 2}
                      y={y - 2}
                      width={barWidth + 4}
                      height={barHeight + 4}
                      fill="none"
                      stroke={color}
                      strokeWidth="2"
                      rx="6"
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    />
                  )}
                </AnimatePresence>

                {/* Value label */}
                {(showValues || hovered) && (
                  <motion.text
                    x={x + barWidth / 2}
                    y={y - 5}
                    textAnchor="middle"
                    fontSize="12"
                    fill="currentColor"
                    className="text-foreground font-medium"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.3,
                      delay: index * staggerDelay + animationDuration
                    }}
                  >
                    {item.value}
                  </motion.text>
                )}

                {/* X-axis label */}
                {showLabels && (
                  <motion.text
                    x={x + barWidth / 2}
                    y={chartHeight + 20}
                    textAnchor="middle"
                    fontSize="11"
                    fill="currentColor"
                    className="text-muted-foreground"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{
                      duration: 0.3,
                      delay: index * staggerDelay + animationDuration * 0.5
                    }}
                  >
                    {item.label}
                  </motion.text>
                )}

                {/* Interactive animation on hover */}
                <AnimatePresence>
                  {hovered && (
                    <motion.g
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ type: "spring", stiffness: 300, damping: 20 }}
                    >
                      <rect
                        x={x + barWidth / 2 - 40}
                        y={y - 40}
                        width="80"
                        height="30"
                        rx="4"
                        fill="var(--popover)"
                        stroke="var(--border)"
                        strokeWidth="1"
                      />
                      <text
                        x={x + barWidth / 2}
                        y={y - 20}
                        textAnchor="middle"
                        fontSize="12"
                        fill="currentColor"
                        className="text-popover-foreground"
                      >
                        {item.label}: {item.value}
                      </text>
                    </motion.g>
                  )}
                </AnimatePresence>
              </g>
            );
          })}
        </g>
      </svg>
    </div>
  );
}