import React from 'react';
import { cn } from '../../lib/utils';
import { motion } from 'framer-motion';
import { animationPresets } from '../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../types/component-props';
import { defaultComponentProps } from '../../types/component-props';

interface AuthLayoutProps {
  children: React.ReactNode;
  className?: string;
  showBackground?: boolean;
  backgroundImage?: string;
}

export function AuthLayout({
  children,
  className,
  showBackground = true,
  backgroundImage
}: AuthLayoutProps) {
  return (
    <div className={cn(
      "min-h-screen flex flex-col items-center justify-center relative overflow-hidden",
      className
    )}>
      {showBackground && (
        <div className="absolute inset-0 -z-10">
          {backgroundImage ? (
            <img 
              src={backgroundImage} 
              alt="" 
              className="w-full h-full object-cover opacity-20"
            />
          ) : (
            <>
              {/* Default animated background */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-secondary/10" />
              <div className="absolute inset-0">
                <div className="absolute -top-1/2 -left-1/2 w-full h-full bg-primary/5 rounded-full blur-3xl animate-pulse" />
                <div className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-secondary/5 rounded-full blur-3xl animate-pulse delay-1000" />
              </div>
            </>
          )}
        </div>
      )}
      <motion.div
        initial={animationPresets.fadeIn.initial}
        animate={animationPresets.fadeIn.animate}
        className="w-full px-4 sm:px-6 lg:px-8"
      >
        {children}
      </motion.div>
    </div>
  );
}