import React from 'react';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface GanttTask {
  id: string;
  name: string;
  start: Date;
  end: Date;
  progress: number;
  color?: string;
  dependencies?: string[];
}

export interface GanttChartProps {
  tasks: GanttTask[];
  width?: number;
  height?: number;
  timeScale?: 'day' | 'week' | 'month';
  showProgress?: boolean;
  showDependencies?: boolean;
  onTaskClick?: (task: GanttTask) => void;
  className?: string;
}

export function LuminarGanttChart({
  tasks,
  width = 800,
  height = 400,
  timeScale = 'week',
  showProgress = true,
  showDependencies = false,
  onTaskClick,
  className = ''
}: GanttChartProps) {
  const glassClasses = getAdaptiveGlassClasses('card', 'md', 'neutral');

  // Calculate time range
  const allDates = tasks.flatMap(task => [task.start, task.end]);
  const minDate = new Date(Math.min(...allDates.map(d => d.getTime())));
  const maxDate = new Date(Math.max(...allDates.map(d => d.getTime())));

  // Chart dimensions
  const chartWidth = width - 200; // Reserve space for task names
  const chartHeight = height - 60; // Reserve space for header
  const taskHeight = 30;
  const taskSpacing = 10;

  // Time scale calculations
  const timeRange = maxDate.getTime() - minDate.getTime();
  const timeUnit = timeScale === 'day' ? 24 * 60 * 60 * 1000 : 
                   timeScale === 'week' ? 7 * 24 * 60 * 60 * 1000 : 
                   30 * 24 * 60 * 60 * 1000;

  // Convert date to x position
  const dateToX = (date: Date) => {
    return ((date.getTime() - minDate.getTime()) / timeRange) * chartWidth;
  };

  // Generate time grid
  const generateTimeGrid = () => {
    const grid = [];
    const current = new Date(minDate);
    
    while (current <= maxDate) {
      const x = dateToX(current);
      grid.push({
        x,
        date: new Date(current),
        label: current.toLocaleDateString()
      });
      
      current.setTime(current.getTime() + timeUnit);
    }
    
    return grid;
  };

  const timeGrid = generateTimeGrid();

  // Default colors
  const defaultColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

  return (
    <div className={`${glassClasses} p-4 rounded-lg border ${className}`}>
      <div className="overflow-auto">
        <svg width={width} height={height}>
          {/* Time grid */}
          <g className="time-grid">
            {timeGrid.map((gridLine, index) => (
              <g key={index}>
                <line
                  x1={200 + gridLine.x}
                  y1={40}
                  x2={200 + gridLine.x}
                  y2={height - 20}
                  stroke="var(--border)"
                  strokeWidth="1"
                  opacity="0.3"
                />
                <text
                  x={200 + gridLine.x}
                  y={35}
                  textAnchor="middle"
                  fontSize="10"
                  fill="currentColor"
                  className="text-muted-foreground"
                >
                  {gridLine.label}
                </text>
              </g>
            ))}
          </g>

          {/* Task bars */}
          {tasks.map((task, index) => {
            const y = 50 + index * (taskHeight + taskSpacing);
            const taskStartX = 200 + dateToX(task.start);
            const taskEndX = 200 + dateToX(task.end);
            const taskWidth = taskEndX - taskStartX;
            const progressWidth = taskWidth * (task.progress / 100);
            
            const taskColor = task.color || defaultColors[index % defaultColors.length];

            return (
              <g key={task.id}>
                {/* Task name */}
                <text
                  x={10}
                  y={y + taskHeight / 2}
                  dominantBaseline="middle"
                  fontSize="12"
                  fill="currentColor"
                  className="text-foreground font-medium"
                >
                  {task.name}
                </text>

                {/* Task bar background */}
                <rect
                  x={taskStartX}
                  y={y}
                  width={taskWidth}
                  height={taskHeight}
                  fill={taskColor}
                  opacity="0.3"
                  rx="4"
                  className="cursor-pointer hover:opacity-50 transition-opacity"
                  onClick={() => onTaskClick?.(task)}
                />

                {/* Progress bar */}
                {showProgress && (
                  <rect
                    x={taskStartX}
                    y={y}
                    width={progressWidth}
                    height={taskHeight}
                    fill={taskColor}
                    opacity="0.8"
                    rx="4"
                    className="cursor-pointer"
                    onClick={() => onTaskClick?.(task)}
                  />
                )}

                {/* Task progress text */}
                {showProgress && taskWidth > 50 && (
                  <text
                    x={taskStartX + taskWidth / 2}
                    y={y + taskHeight / 2}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    fontSize="10"
                    fill="white"
                    className="font-medium pointer-events-none"
                  >
                    {task.progress}%
                  </text>
                )}
              </g>
            );
          })}

          {/* Dependencies (if enabled) */}
          {showDependencies && tasks.map((task, index) => {
            if (!task.dependencies) return null;
            
            return task.dependencies.map((depId, depIndex) => {
              const depTask = tasks.find(t => t.id === depId);
              if (!depTask) return null;
              
              const depTaskIndex = tasks.findIndex(t => t.id === depId);
              const currentY = 50 + index * (taskHeight + taskSpacing) + taskHeight / 2;
              const depY = 50 + depTaskIndex * (taskHeight + taskSpacing) + taskHeight / 2;
              const depEndX = 200 + dateToX(depTask.end);
              const taskStartX = 200 + dateToX(task.start);
              
              return (
                <g key={`${task.id}-${depId}`}>
                  <path
                    d={`M ${depEndX} ${depY} L ${taskStartX - 20} ${depY} L ${taskStartX - 20} ${currentY} L ${taskStartX} ${currentY}`}
                    stroke="var(--muted-foreground)"
                    strokeWidth="1"
                    fill="none"
                    opacity="0.6"
                    markerEnd="url(#arrowhead)"
                  />
                </g>
              );
            });
          })}

          {/* Arrow marker for dependencies */}
          <defs>
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto"
            >
              <polygon
                points="0 0, 10 3.5, 0 7"
                fill="var(--muted-foreground)"
                opacity="0.6"
              />
            </marker>
          </defs>
        </svg>
      </div>
      
      <div className="mt-4 flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {tasks.length} tasks • {minDate.toLocaleDateString()} - {maxDate.toLocaleDateString()}
        </div>
        <div className="text-sm text-muted-foreground">
          Scale: {timeScale}
        </div>
      </div>
    </div>
  );
}

// Sample data generator
export function generateSampleGanttData(): GanttTask[] {
  const now = new Date();
  return [
    {
      id: '1',
      name: 'Project Planning',
      start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      end: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000),
      progress: 80,
      color: '#3b82f6'
    },
    {
      id: '2',
      name: 'Design Phase',
      start: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000),
      end: new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000),
      progress: 45,
      color: '#10b981',
      dependencies: ['1']
    },
    {
      id: '3',
      name: 'Development',
      start: new Date(now.getTime() + 10 * 24 * 60 * 60 * 1000),
      end: new Date(now.getTime() + 28 * 24 * 60 * 60 * 1000),
      progress: 20,
      color: '#f59e0b',
      dependencies: ['2']
    },
    {
      id: '4',
      name: 'Testing',
      start: new Date(now.getTime() + 25 * 24 * 60 * 60 * 1000),
      end: new Date(now.getTime() + 35 * 24 * 60 * 60 * 1000),
      progress: 0,
      color: '#ef4444',
      dependencies: ['3']
    },
    {
      id: '5',
      name: 'Deployment',
      start: new Date(now.getTime() + 32 * 24 * 60 * 60 * 1000),
      end: new Date(now.getTime() + 40 * 24 * 60 * 60 * 1000),
      progress: 0,
      color: '#8b5cf6',
      dependencies: ['4']
    }
  ];
}