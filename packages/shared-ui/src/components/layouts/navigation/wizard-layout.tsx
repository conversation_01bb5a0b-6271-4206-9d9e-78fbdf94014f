import { ReactNode, useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

import {
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  Clock,
  Save,
  SkipForward,
  RotateCcw,
  Check,
  X
} from "lucide-react";

import { LuminarCard } from '../../ui/display';
import { Button } from '../../ui/actions';
import { LuminarBadge } from '../../ui/display';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface WizardLayoutProps {
  className?: string;
  title?: string;
  subtitle?: string;
  steps: WizardStep[];
  currentStep?: number;
  allowSkipping?: boolean;
  showProgress?: boolean;
  showStepNumbers?: boolean;
  orientation?: 'horizontal' | 'vertical';
  size?: ComponentSize | 'md' | 'lg' | 'xl';
  onStepChange?: (stepIndex: number, direction: 'next' | 'previous' | 'jump') => void;
  onComplete?: (data: Record<string, any>) => void;
  onCancel?: () => void;
  onSave?: (data: Record<string, any>) => void;
  theme?: 'default' | 'minimal' | 'cards';
  autoSave?: boolean;
  persistProgress?: boolean;
}

export interface WizardStep {
  id: string;
  title: string;
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
  content: ReactNode;
  isCompleted?: boolean;
  isOptional?: boolean;
  disabled?: boolean;
  estimatedTime?: string;
  validation?: (data: any) => boolean | string;
  onEnter?: () => void;
  onExit?: () => void;
  customButtons?: WizardButton[];
}

export interface WizardButton {
  id: string;
  label: string;
  variant?: ComponentVariant | 'secondary' | 'outline' | 'ghost';
  icon?: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  disabled?: boolean;
}

const sizeClasses = {
  sm: 'max-w-2xl',
  md: 'max-w-4xl',
  lg: 'max-w-6xl',
  xl: 'max-w-7xl'
};

export function WizardLayout({
  className,
  title,
  subtitle,
  steps,
  currentStep: controlledStep,
  allowSkipping = false,
  showProgress = true,
  showStepNumbers = true,
  orientation = 'horizontal',
  size = 'md',
  onStepChange,
  onComplete,
  onCancel,
  onSave,
  theme = 'default',
  autoSave = false,
  persistProgress = false,
  ...props
}: WizardLayoutProps) {
  const [internalStep, setInternalStep] = useState(0);
  const [stepData, setStepData] = useState<Record<string, any>>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const currentStepIndex = controlledStep ?? internalStep;
  const currentStepData = steps[currentStepIndex];
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === steps.length - 1;
  const completedSteps = steps.filter(step => step.isCompleted).length;
  const progressPercentage = ((completedSteps + (currentStepIndex + 1)) / steps.length) * 100;

  useEffect(() => {
    currentStepData?.onEnter?.();
    return () => currentStepData?.onExit?.();
  }, [currentStepIndex]);

  useEffect(() => {
    if (autoSave && Object.keys(stepData).length > 0) {
      const saveTimer = setTimeout(() => {
        onSave?.(stepData);
      }, 2000);
      return () => clearTimeout(saveTimer);
    }
  }, [stepData, autoSave, onSave]);

  const validateStep = (stepIndex: number): boolean => {
    const step = steps[stepIndex];
    if (!step.validation) return true;

    const result = step.validation(stepData[step.id]);
    if (typeof result === 'string') {
      setValidationErrors(prev => ({ ...prev, [step.id]: result }));
      return false;
    }
    
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[step.id];
      return newErrors;
    });
    
    return result;
  };

  const goToStep = (stepIndex: number, direction: 'next' | 'previous' | 'jump' = 'jump') => {
    if (stepIndex < 0 || stepIndex >= steps.length) return;
    
    if (direction === 'next' && !validateStep(currentStepIndex)) {
      return;
    }

    const newStep = Math.max(0, Math.min(stepIndex, steps.length - 1));
    
    if (controlledStep === undefined) {
      setInternalStep(newStep);
    }
    
    onStepChange?.(newStep, direction);

    // Mark previous steps as completed
    if (direction === 'next') {
      steps[currentStepIndex].isCompleted = true;
    }
  };

  const nextStep = () => {
    if (isLastStep) {
      if (validateStep(currentStepIndex)) {
        onComplete?.(stepData);
      }
    } else {
      goToStep(currentStepIndex + 1, 'next');
    }
  };

  const previousStep = () => {
    goToStep(currentStepIndex - 1, 'previous');
  };

  const skipStep = () => {
    if (allowSkipping && currentStepData.isOptional) {
      goToStep(currentStepIndex + 1, 'next');
    }
  };

  const resetWizard = () => {
    setInternalStep(0);
    setStepData({});
    setValidationErrors({});
    steps.forEach(step => step.isCompleted = false);
    onStepChange?.(0, 'jump');
  };

  const renderStepIndicator = (step: WizardStep, index: number) => {
    const active = index === currentStepIndex;
    const isCompleted = step.isCompleted;
    const isPast = index < currentStepIndex;
    const canClick = !step.disabled && (isPast || active || allowSkipping);

    return (
      <div
        key={step.id}
        className={cn(
          "flex items-center",
          orientation === 'vertical' ? "w-full" : "flex-1"
        )}
      >
        <button
          onClick={() => canClick && goToStep(index, 'jump')}
          disabled={!canClick}
          className={cn(
            "relative flex items-center gap-3 p-3 rounded-lg transition-all",
            canClick ? "hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer" : "cursor-not-allowed",
            active && "bg-blue-50 dark:bg-blue-950",
            orientation === 'vertical' ? "w-full text-left" : "flex-col text-center"
          )}
        >
          {/* Step Circle */}
          <div className={cn(
            "relative flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all",
            isCompleted 
              ? "bg-green-500 border-green-500 text-white"
              : active
              ? "bg-blue-500 border-blue-500 text-white"
              : isPast
              ? "bg-gray-200 dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
          )}>
            {isCompleted ? (
              <Check className="w-5 h-5" />
            ) : showStepNumbers ? (
              <span className={cn(
                "text-sm font-medium",
                active ? "text-white" : "text-gray-600 dark:text-gray-400"
              )}>
                {index + 1}
              </span>
            ) : (
              step.icon && (
                <step.icon className={cn(
                  "w-5 h-5",
                  active ? "text-white" : "text-gray-600 dark:text-gray-400"
                )} />
              )
            )}
          </div>

          {/* Step Info */}
          <div className={cn(
            "flex-1 min-w-0",
            orientation === 'horizontal' && "mt-2"
          )}>
            <div className="flex items-center gap-2">
              <p className={cn(
                "font-medium text-sm",
                active ? "text-blue-600 dark:text-blue-400" : "text-gray-900 dark:text-white"
              )}>
                {step.title}
              </p>
              {step.isOptional && (
                <LuminarBadge size="sm" variant="default">Optional</LuminarBadge>
              )}
            </div>
            {step.description && (
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                {step.description}
              </p>
            )}
            {step.estimatedTime && (
              <div className="flex items-center gap-1 mt-1">
                <Clock className="w-3 h-3 text-gray-400" />
                <span className="text-xs text-gray-500">{step.estimatedTime}</span>
              </div>
            )}
          </div>

          {/* Validation Error Indicator */}
          {validationErrors[step.id] && (
            <AlertCircle className="w-4 h-4 text-red-500" />
          )}
        </button>

        {/* Connection Line */}
        {index < steps.length - 1 && (
          <div className={cn(
            "bg-gray-200 dark:bg-gray-700",
            orientation === 'horizontal' 
              ? "h-0.5 flex-1 mx-4" 
              : "w-0.5 h-8 ml-8"
          )} />
        )}
      </div>
    );
  };

  return (
    <div className={cn(
      "min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-950 dark:to-indigo-950",
      className
    )} {...props}>
      <div className={cn("mx-auto px-4 py-8", sizeClasses[size])}>
        {/* Header */}
        {(title || subtitle) && (
          <div className="text-center mb-8">
            {title && (
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {title}
              </h1>
            )}
            {subtitle && (
              <p className="text-gray-600 dark:text-gray-400">
                {subtitle}
              </p>
            )}
          </div>
        )}

        {/* Progress Bar */}
        {showProgress && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Step {currentStepIndex + 1} of {steps.length}
              </span>
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {Math.round(progressPercentage)}% Complete
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <motion.div
                className="bg-blue-500 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progressPercentage}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>
        )}

        <div className={cn(
          "grid gap-8",
          orientation === 'horizontal' ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-4"
        )}>
          {/* Step Navigation */}
          <div className={cn(
            orientation === 'horizontal' 
              ? "order-1" 
              : "order-2 lg:order-1 lg:col-span-1"
          )}>
            <LuminarCard className="p-6">
              <h2 className="font-semibold text-gray-900 dark:text-white mb-4">
              </h2>
              <div className={cn(
                "space-y-1",
                orientation === 'horizontal' && "flex space-y-0 space-x-4"
              )}>
                {steps.map((step, index) => renderStepIndicator(step, index))}
              </div>
            </LuminarCard>
          </div>

          {/* Step Content */}
          <div className={cn(
            orientation === 'horizontal' 
              ? "order-2" 
              : "order-1 lg:order-2 lg:col-span-3"
          )}>
            <LuminarCard className="p-8">
              {/* Step Header */}
              <div className="mb-6">
                <div className="flex items-center gap-3 mb-2">
                  {currentStepData.icon && (
                    <currentStepData.icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  )}
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {currentStepData.title}
                  </h2>
                  {currentStepData.isOptional && (
                    <LuminarBadge variant="default">Optional</LuminarBadge>
                  )}
                </div>
                {currentStepData.description && (
                  <p className="text-gray-600 dark:text-gray-400">
                    {currentStepData.description}
                  </p>
                )}
              </div>

              {/* Validation Error */}
              {validationErrors[currentStepData.id] && (
                <div className="mb-6 p-4 bg-red-50 dark:bg-red-950/50 border border-red-200 dark:border-red-800 rounded-lg">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="w-5 h-5 text-red-500" />
                    <p className="text-red-700 dark:text-red-300 font-medium">
                      {validationErrors[currentStepData.id]}
                    </p>
                  </div>
                </div>
              )}

              {/* Step Content */}
              <div className="mb-8">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentStepIndex}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    {currentStepData.content}
                  </motion.div>
                </AnimatePresence>
              </div>

              {/* Navigation Buttons */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {!isFirstStep && (
                    <Button
                      variant="outline"
                      onClick={previousStep}
                      className="flex items-center gap-2"
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </Button>
                  )}
                  
                  {allowSkipping && currentStepData.isOptional && !isLastStep && (
                    <Button
                      variant="ghost"
                      onClick={skipStep}
                      className="flex items-center gap-2"
                    >
                      <SkipForward className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  {/* Custom Buttons */}
                  {currentStepData.customButtons?.map((button) => (
                    <Button
                      key={button.id}
                      variant={button.variant === 'primary' ? 'default' : button.variant}
                      onClick={button.onClick}
                      disabled={button.disabled}
                      className="flex items-center gap-2"
                    >
                      {button.icon && <button.icon className="w-4 h-4" />}
                      {button.label}
                    </Button>
                  ))}

                  {/* Save Button */}
                  {onSave && (
                    <Button
                      variant="outline"
                      onClick={() => onSave(stepData)}
                      className="flex items-center gap-2"
                    >
                      <Save className="w-4 h-4" />
                    </Button>
                  )}

                  {/* Cancel Button */}
                  {onCancel && (
                    <Button
                      variant="outline"
                      onClick={onCancel}
                      className="flex items-center gap-2"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}

                  {/* Reset Button */}
                  {currentStepIndex > 0 && (
                    <Button
                      variant="ghost"
                      onClick={resetWizard}
                      className="flex items-center gap-2"
                    >
                      <RotateCcw className="w-4 h-4" />
                    </Button>
                  )}

                  {/* Next/Complete Button */}
                  <Button
                    onClick={nextStep}
                    disabled={currentStepData.disabled}
                    className="flex items-center gap-2"
                  >
                    {isLastStep ? 'Complete' : 'Next'}
                    {!isLastStep && <ChevronRight className="w-4 h-4" />}
                  </Button>
                </div>
              </div>
            </LuminarCard>
          </div>
        </div>
      </div>
    </div>
  );
}