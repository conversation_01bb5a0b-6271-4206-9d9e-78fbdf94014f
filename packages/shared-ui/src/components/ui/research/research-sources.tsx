import { motion, AnimatePresence } from "framer-motion";
import { forwardRef, useState, useMemo } from "react";
import {
  FileText, 
  Globe, 
  BookOpen, 
  Database, 
  ExternalLink, 
  Trash2, 
  Star, 
  Quote,
  Eye,
  Download,
  Share2,
  Calendar,
  User,
  Hash,
  Search,
  Filter,
  X,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import { cn } from '../../../lib/utils';
import { LuminarButton } from "../actions/button-advanced";
import { LuminarCard } from "../display/card";
import { LuminarText } from "../display/text";
import { LuminarBadge } from "../display/badge";
import { LuminarTooltip } from "../feedback/tooltip";
import { LuminarInput } from "../forms/input";
import { LuminarSelect } from "../forms/select";
import { LuminarDropdown } from "../actions/dropdown";
import {
  getGlassClasses, 
  animationPresets, 
  type ComponentSize
} from "../../../design-system";
import { ResearchSource, Citation } from '../../../types/research';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface ResearchSourcesProps {
  sources: ResearchSource[];
  citations: Citation[];
  selectedSource: ResearchSource | null;
  onSourceSelect: (source: ResearchSource | null) => void;
  onSourceRemove: (sourceId: string) => void;
  onCitationAdd: (citation: Omit<Citation, 'id'>) => void;
  onCitationRemove: (citationId: string) => void;
  citationStyle: Citation['style'];
  className?: string;
  size?: ComponentSize;
  showSearch?: boolean;
  showFilters?: boolean;
  collapsible?: boolean;
}

const ResearchSources = forwardRef<HTMLDivElement, ResearchSourcesProps>(
  ({
    sources,
    citations,
    selectedSource,
    onSourceSelect,
    onSourceRemove,
    onCitationAdd,
    onCitationRemove,
    citationStyle,
    className,
    size = "md",
    showSearch = true,
    showFilters = true,
    collapsible = false,
    ...props
  }, ref) => {
    const [searchQuery, setSearchQuery] = useState("");
    const [filterType, setFilterType] = useState<ResearchSource['type'] | 'all'>('all');
    const [sortBy, setSortBy] = useState<'relevance' | 'date' | 'title'>('relevance');
    const [isCollapsed, setIsCollapsed] = useState(false);

    // Filter and sort sources
    const filteredSources = useMemo(() => {
      let filtered = sources;

      // Search filter
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        filtered = filtered.filter(source =>
          source.title.toLowerCase().includes(query) ||
          source.snippet.toLowerCase().includes(query) ||
          source.metadata?.author?.toLowerCase().includes(query)
        );
      }

      // Type filter
      if (filterType !== 'all') {
        filtered = filtered.filter(source => source.type === filterType);
      }

      // Sort
      switch (sortBy) {
        case 'relevance':
          filtered.sort((a, b) => b.relevanceScore - a.relevanceScore);
          break;
        case 'date':
          filtered.sort((a, b) => {
            const dateA = a.metadata?.publishDate || a.lastAccessed;
            const dateB = b.metadata?.publishDate || b.lastAccessed;
            return dateB.getTime() - dateA.getTime();
          });
          break;
        case 'title':
          filtered.sort((a, b) => a.title.localeCompare(b.title));
          break;
      }

      return filtered;
    }, [sources, searchQuery, filterType, sortBy]);

    // Get source type icon
    const getSourceTypeIcon = (type: ResearchSource['type']) => {
      switch (type) {
        case 'web':
          return Globe;
        case 'academic':
          return BookOpen;
        case 'database':
          return Database;
        case 'pdf':
        case 'doc':
          return FileText;
        default:
          return FileText;
      }
    };

    // Get source type color
    const getSourceTypeColor = (type: ResearchSource['type']) => {
      switch (type) {
        case 'web':
          return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
        case 'academic':
          return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
        case 'database':
          return 'bg-green-500/20 text-green-400 border-green-500/30';
        case 'pdf':
        case 'doc':
          return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
        default:
          return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      }
    };

    // Format citation
    const formatCitation = (source: ResearchSource, style: Citation['style']) => {
      const author = source.metadata?.author || 'Unknown Author';
      const title = source.title;
      const date = source.metadata?.publishDate || source.lastAccessed;
      const url = source.url;
      const domain = source.metadata?.domain || (url ? new URL(url).hostname : '');

      switch (style) {
        case 'APA':
          return `${author} (${date.getFullYear()}). ${title}. ${domain}. ${url}`;
        case 'MLA':
          return `${author}. "${title}." ${domain}, ${date.getFullYear()}, ${url}.`;
        case 'Chicago':
          return `${author}. "${title}." ${domain}. Accessed ${date.toLocaleDateString()}. ${url}.`;
        case 'Harvard':
          return `${author} (${date.getFullYear()}) '${title}', ${domain}. Available at: ${url}.`;
        case 'IEEE':
          return `${author}, "${title}," ${domain}, ${date.getFullYear()}. [Online]. Available: ${url}`;
        default:
          return `${author}. ${title}. ${domain}. ${url}`;
      }
    };

    // Source card component
    const SourceCard = ({ source, index }: { source: ResearchSource; index: number }) => {
      const isSelected = selectedSource?.id === source.id;
      const sourceCitations = citations.filter(c => c.sourceId === source.id);
      const TypeIcon = getSourceTypeIcon(source.type);
      const typeColor = getSourceTypeColor(source.type);

      const glassClasses = getGlassClasses('card', {
        intensity: isSelected ? 'md' : 'light',
        depth: isSelected ? 'elevated' : 'surface',
        animated: true,
        interactive: true
      });

      return (
        <motion.div
          key={source.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ 
            type: "spring" as const,
            stiffness: 300,
            damping: 30,
            delay: index * 0.05 
          }}
          className={cn(
            "group relative cursor-pointer",
            glassClasses,
            isSelected && "ring-2 ring-blue-500/50 bg-blue-500/5",
            "hover:scale-[1.02] transition-transform duration-200"
          )}
          onClick={() => onSourceSelect(isSelected ? null : source)}
        >
          <div className="p-4">
            {/* Header */}
            <div className="flex items-start justify-between gap-3 mb-3">
              <div className="flex items-center gap-3 flex-1 min-w-0">
                <div className={cn("p-2 rounded-lg border", typeColor)}>
                  <TypeIcon className="w-4 h-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <LuminarText 
                    text={source.title}
                    className="font-medium line-clamp-1 mb-1"
                  />
                  <div className="flex items-center gap-2 text-xs text-gray-400">
                    {source.metadata?.author && (
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        <span>{source.metadata.author}</span>
                      </div>
                    )}
                    {source.metadata?.publishDate && (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>{source.metadata.publishDate.toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Actions */}
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <LuminarTooltip content="Add citation">
                  <LuminarButton
                    size="xs"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      onCitationAdd({
                        sourceId: source.id,
                        text: source.snippet,
                        position: { start: 0, end: source.snippet.length },
                        style: citationStyle,
                        generatedText: formatCitation(source, citationStyle)
                      });
                    }}
                  >
                    <Quote className="w-3 h-3" />
                  </LuminarButton>
                </LuminarTooltip>

                <LuminarDropdown
                  trigger={
                    <LuminarButton
                      size="xs"
                      variant="ghost"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Share2 className="w-3 h-3" />
                    </LuminarButton>
                  }
                  items={[
                    {
                      id: 'view',
                      label: 'View full source',
                      icon: Eye,
                      onClick: () => {}
                    },
                    {
                      id: 'export',
                      label: 'Export citation',
                      icon: Download,
                      onClick: () => {}
                    },
                    {
                      id: 'favorite',
                      label: 'Add to favorites',
                      icon: Star,
                      onClick: () => {}
                    },
                    {
                      id: 'separator',
                      label: '',
                      separator: true
                    },
                    {
                      id: 'remove',
                      label: 'Remove source',
                      icon: Trash2,
                      onClick: () => onSourceRemove(source.id)
                    }
                  ]}
                />
              </div>
            </div>

            {/* Content */}
            <div className="mb-3">
              <LuminarText 
                text={source.snippet}
                className="text-sm text-gray-300 line-clamp-3"
              />
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between gap-2">
              <div className="flex items-center gap-2">
                <LuminarBadge 
                  variant="default" 
                  className={cn("capitalize text-xs", typeColor)}
                >
                  {source.type}
                </LuminarBadge>
                
                <div className="flex items-center gap-1 text-xs text-gray-400">
                  <Star className="w-3 h-3" />
                  <span>{(source.relevanceScore * 100).toFixed(0)}%</span>
                </div>

                {sourceCitations.length > 0 && (
                  <LuminarBadge variant="default" className="text-xs">
                    {sourceCitations.length} citation{sourceCitations.length !== 1 ? 's' : ''}
                  </LuminarBadge>
                )}
              </div>
              
              {source.url && (
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <ExternalLink className="w-3 h-3" />
                  <span className="truncate max-w-32">
                    {source.metadata?.domain || new URL(source.url).hostname}
                  </span>
                </div>
              )}
            </div>

            {/* Expanded content */}
            <AnimatePresence>
              {isSelected && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{
                    duration: 0.3,
                    ease: 'easeInOut'
                  }}
                  className="mt-4 pt-4 border-t border-white/10"
                >
                  {/* Citation preview */}
                  <div className="mb-4">
                    <LuminarText 
                      text={`${citationStyle} Citation:`}
                      className="text-xs text-gray-400 mb-2"
                    />
                    <div className="p-3 bg-gray-500/10 rounded-lg">
                      <LuminarText 
                        text={formatCitation(source, citationStyle)}
                        className="text-sm font-mono text-gray-300"
                      />
                    </div>
                  </div>

                  {/* Metadata */}
                  {source.metadata && (
                    <div className="grid grid-cols-2 gap-4 text-xs">
                      {source.metadata.wordCount && (
                        <div>
                          <span className="text-gray-400">Words:</span>
                          <span className="ml-2 text-gray-300">{source.metadata.wordCount.toLocaleString()}</span>
                        </div>
                      )}
                      {source.metadata.language && (
                        <div>
                          <span className="text-gray-400">Language:</span>
                          <span className="ml-2 text-gray-300">{source.metadata.language}</span>
                        </div>
                      )}
                      <div>
                        <span className="text-gray-400">Last accessed:</span>
                        <span className="ml-2 text-gray-300">{source.lastAccessed.toLocaleDateString()}</span>
                      </div>
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      );
    };

    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col h-full",
          className
        )}
        {...props}
      >
        {/* Header */}
        <div className="p-4 border-b border-white/10">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <LuminarText 
                text="Sources"
                className="text-lg font-semibold"
              />
              <LuminarBadge variant="default">
                {sources.length}
              </LuminarBadge>
            </div>
            
            {collapsible && (
              <LuminarButton
                size="sm"
                variant="ghost"
                onClick={() => setIsCollapsed(!isCollapsed)}
              >
                {isCollapsed ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
              </LuminarButton>
            )}
          </div>

          {/* Search and Filters */}
          {!isCollapsed && (
            <div className="space-y-3">
              {showSearch && (
                <div className="relative">
                  <LuminarInput
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search sources..."
                    size="sm"
                    className="w-full pl-8"
                  />
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  {searchQuery && (
                    <LuminarButton
                      size="xs"
                      variant="ghost"
                      onClick={() => setSearchQuery('')}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2"
                    >
                      <X className="w-3 h-3" />
                    </LuminarButton>
                  )}
                </div>
              )}

              {showFilters && (
                <div className="flex items-center gap-2">
                  <LuminarSelect
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value as ResearchSource['type'] | 'all')}
                    options={[
                      { value: 'all', label: 'All types' },
                      { value: 'web', label: 'Web' },
                      { value: 'academic', label: 'Academic' },
                      { value: 'database', label: 'Database' },
                      { value: 'pdf', label: 'PDF' },
                      { value: 'doc', label: 'Document' }
                    ]}
                  />

                  <LuminarSelect
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as 'relevance' | 'date' | 'title')}
                    options={[
                      { value: 'relevance', label: 'Relevance' },
                      { value: 'date', label: 'Date' },
                      { value: 'title', label: 'Title' }
                    ]}
                  />
                </div>
              )}
            </div>
          )}
        </div>

        {/* Sources List */}
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="flex-1 overflow-y-auto p-4"
            >
              <AnimatePresence mode="wait">
                {filteredSources.length > 0 ? (
                  <motion.div
                    key="sources"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="space-y-3"
                  >
                    {filteredSources.map((source, index) => (
                      <SourceCard key={source.id} source={source} index={index} />
                    ))}
                  </motion.div>
                ) : sources.length === 0 ? (
                  <motion.div
                    key="empty"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="flex flex-col items-center justify-center py-12 text-center"
                  >
                    <FileText className="w-12 h-12 text-gray-400 mb-4" />
                    <LuminarText 
                      text="No sources added yet"
                      className="text-lg font-medium text-gray-300 mb-2"
                    />
                    <LuminarText 
                      text="Sources you add from search results or conversations will appear here."
                      className="text-sm text-gray-500 max-w-md"
                    />
                  </motion.div>
                ) : (
                  <motion.div
                    key="no-match"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="flex flex-col items-center justify-center py-12 text-center"
                  >
                    <Search className="w-12 h-12 text-gray-400 mb-4" />
                    <LuminarText 
                      text="No sources match your filters"
                      className="text-lg font-medium text-gray-300 mb-2"
                    />
                    <LuminarText 
                      text="Try adjusting your search or filter settings."
                      className="text-sm text-gray-500 max-w-md"
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }
);
ResearchSources.displayName = "ResearchSources";

export { ResearchSources };