import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, InputHTMLAttributes } from "react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface LuminarSwitchProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type' | 'size'> {
  label?: string
  size?: ComponentSize | "md" | "lg"
  glass?: boolean
  onCheckedChange?: (checked: boolean) => void
}

const LuminarSwitch = forwardRef<HTMLInputElement, LuminarSwitchProps>(
  ({ 
    label,
    size = "md",
    glass = true,
    className,
    checked,
    onChange,
    onCheckedChange,
    disabled,
    ...props 
  }, ref) => {
    const sizeClasses = {
      xs: { track: "w-6 h-3", thumb: "w-2 h-2", translate: "translate-x-3" },
      sm: { track: "w-8 h-4", thumb: "w-3 h-3", translate: "translate-x-4" },
      md: { track: "w-11 h-6", thumb: "w-5 h-5", translate: "translate-x-5" },
      lg: { track: "w-14 h-8", thumb: "w-6 h-6", translate: "translate-x-6" }
    }

    const { track, thumb, translate } = sizeClasses[size]

    return (
      <label className={cn("inline-flex items-center gap-3 cursor-pointer", disabled && "opacity-50 cursor-not-allowed", className)}>
        <div className="relative">
          <input
            ref={ref}
            type="checkbox"
            className="sr-only"
            checked={checked}
            onChange={(e) => {
              onChange?.(e)
              onCheckedChange?.(e.target.checked)
            }}
            disabled={disabled}
            {...props}
          />
          
          <motion.div
            className={cn(
              "relative rounded-full transition-colors duration-300",
              track,
              glass && "backdrop-blur-md bg-white/20 dark:bg-gray-900/20 border border-white/30 dark:border-gray-700/40",
              !glass && "bg-gray-300 dark:bg-gray-700",
              checked && !glass && "bg-primary",
              checked && glass && "bg-primary/30"
            )}
            animate={{
              backgroundColor: checked 
                ? glass ? "rgba(var(--primary), 0.3)" : "rgb(var(--primary))"
                : glass ? "rgba(255, 255, 255, 0.2)" : undefined
            }}
          >
            <motion.div
              className={cn(
                "absolute top-0.5 left-0.5 rounded-full bg-white shadow-md",
                thumb
              )}
              animate={{
                x: checked ? translate.replace("translate-x-", "") : "0",
                scale: checked ? 1.1 : 1
              }}
              transition={{
                type: "spring",
                stiffness: 500,
                damping: 30
              }}
            />
            
            {/* Glow effect when checked */}
            {checked && (
              <motion.div
                className="absolute inset-0 rounded-full bg-primary/20"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1.2, opacity: 0 }}
                transition={{
                  duration: 0.6,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />
            )}
          </motion.div>
        </div>
        
        {label && (
          <span className={cn("select-none text-sm font-medium", size === "lg" && "text-base")}>
            {label}
          </span>
        )}
      </label>
    )
  }
)
LuminarSwitch.displayName = "LuminarSwitch"

export { LuminarSwitch }