import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes } from "react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface LuminarSkeletonProps extends HTMLAttributes<HTMLDivElement> {
  variant?: ComponentVariant | "text" | "circular" | "rectangular" | "glass" | "card" | "list" | "table" | "avatar" | "button" | "input" | "chart"
  animation?: "pulse" | "wave" | "shimmer" | "none"
  width?: string | number
  height?: string | number
  count?: number
  rounded?: boolean
  glass?: boolean
}

const LuminarSkeleton = forwardRef<HTMLDivElement, LuminarSkeletonProps>(
  ({ 
    variant = defaultComponentProps.variant, 
    animation = "pulse",
    width,
    height,
    count = 1,
    rounded = true,
    glass = true,
    className,
    ...props 
  }, ref) => {
    const baseClasses = cn(
      "relative overflow-hidden",
      glass 
        ? "backdrop-blur-sm bg-white/5 border border-white/10"
        : "bg-gray-800"
    )

    const variantStyles = {
      default: { borderRadius: rounded ? '0.5rem' : '0' },
      text: { height: height || '1rem', width: width || '100%', borderRadius: '0.25rem' },
      circular: { aspectRatio: '1', borderRadius: '50%', width: width || '2.5rem' },
      rectangular: { borderRadius: rounded ? '0.5rem' : '0' },
      glass: { borderRadius: rounded ? '0.5rem' : '0' },
      card: { height: height || '200px', width: width || '100%', borderRadius: '0.75rem' },
      list: { height: height || '60px', width: width || '100%', borderRadius: '0.5rem' },
      table: { height: height || '40px', width: width || '100%', borderRadius: '0.25rem' },
      avatar: { width: width || '3rem', height: height || '3rem', borderRadius: '50%' },
      button: { height: height || '2.5rem', width: width || '6rem', borderRadius: '0.5rem' },
      input: { height: height || '2.5rem', width: width || '100%', borderRadius: '0.5rem' },
      chart: { height: height || '300px', width: width || '100%', borderRadius: '0.75rem' }
    }

    const animationElement = () => {
      switch (animation) {
        case 'wave':
          return (
            <motion.div
              className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/10 to-transparent"
              animate={{ x: ["0%", "200%"] }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "linear" as const }}
            />
          )
        case 'shimmer':
          return (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12"
              animate={{ x: ["-200%", "200%"] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" as const }}
            />
          )
        default:
          return null
      }
    }

    const pulseAnimation = animation === 'pulse' ? {
      animate: { opacity: [0.5, 1, 0.5] },
      transition: { duration: 1.5, repeat: Infinity, ease: "easeInOut" as const }
    } : {}

    const renderSkeleton = () => {
      const variantStyle = variantStyles[variant]
      return (
        <motion.div
          className={cn(baseClasses, className)}
          style={{
            width: 'width' in variantStyle ? variantStyle.width : undefined,
            height: 'height' in variantStyle ? variantStyle.height : undefined,
            borderRadius: variantStyle.borderRadius,
            aspectRatio: 'aspectRatio' in variantStyle ? variantStyle.aspectRatio : undefined
          }}
          {...pulseAnimation}
        >
          {animationElement()}
        </motion.div>
      )
    }

    if (count > 1) {
      return (
        <div ref={ref} className="space-y-2">
          {Array.from({ length: count }).map((_, index) => (
            <div key={index}>{renderSkeleton()}</div>
          ))}
        </div>
      )
    }

    return renderSkeleton()
  }
)
LuminarSkeleton.displayName = "LuminarSkeleton"

// Preset skeleton components for common use cases
const LuminarSkeletonCard = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "rounded-xl p-6 space-y-4",
          "backdrop-blur-sm bg-white/5 border border-white/10",
          className
        )}
        {...props}
      >
        <div className="flex items-center space-x-4">
          <LuminarSkeleton variant="avatar" />
          <div className="flex-1 space-y-2">
            <LuminarSkeleton variant="text" width="60%" />
            <LuminarSkeleton variant="text" width="40%" />
          </div>
        </div>
        <LuminarSkeleton variant="rectangular" height={120} />
        <div className="space-y-2">
          <LuminarSkeleton variant="text" />
          <LuminarSkeleton variant="text" />
          <LuminarSkeleton variant="text" width="80%" />
        </div>
        <div className="flex justify-between">
          <LuminarSkeleton variant="button" />
          <LuminarSkeleton variant="button" width="4rem" />
        </div>
      </div>
    )
  }
)
LuminarSkeletonCard.displayName = "LuminarSkeletonCard"

const LuminarSkeletonList = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement> & { items?: number }>(
  ({ className, items = 5, ...props }, ref) => {
    return (
      <div ref={ref} className={cn("space-y-3", className)} {...props}>
        {Array.from({ length: items }).map((_, index) => (
          <div key={index} className="flex items-center space-x-4 p-4 rounded-lg backdrop-blur-sm bg-white/5 border border-white/10">
            <LuminarSkeleton variant="circular" width="2.5rem" />
            <div className="flex-1 space-y-2">
              <LuminarSkeleton variant="text" width="70%" />
              <LuminarSkeleton variant="text" width="40%" height="0.75rem" />
            </div>
            <LuminarSkeleton variant="button" width="5rem" height="2rem" />
          </div>
        ))}
      </div>
    )
  }
)
LuminarSkeletonList.displayName = "LuminarSkeletonList"

const LuminarSkeletonTable = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement> & { rows?: number; columns?: number }>(
  ({ className, rows = 5, columns = 4, ...props }, ref) => {
    return (
      <div ref={ref} className={cn("rounded-xl overflow-hidden backdrop-blur-sm bg-white/5 border border-white/10", className)} {...props}>
        {/* Header */}
        <div className="border-b border-white/10 p-4">
          <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }).map((_, index) => (
              <LuminarSkeleton key={index} variant="text" height="1.25rem" />
            ))}
          </div>
        </div>
        {/* Rows */}
        <div className="divide-y divide-white/5">
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <div key={rowIndex} className="p-4">
              <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
                {Array.from({ length: columns }).map((_, colIndex) => (
                  <LuminarSkeleton key={colIndex} variant="text" height="1rem" width={colIndex === 0 ? "80%" : "60%"} />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }
)
LuminarSkeletonTable.displayName = "LuminarSkeletonTable"

const LuminarSkeletonForm = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return (
      <div ref={ref} className={cn("space-y-6", className)} {...props}>
        <div className="space-y-2">
          <LuminarSkeleton variant="text" width="30%" height="0.875rem" />
          <LuminarSkeleton variant="input" />
        </div>
        <div className="space-y-2">
          <LuminarSkeleton variant="text" width="25%" height="0.875rem" />
          <LuminarSkeleton variant="input" />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <LuminarSkeleton variant="text" width="40%" height="0.875rem" />
            <LuminarSkeleton variant="input" />
          </div>
          <div className="space-y-2">
            <LuminarSkeleton variant="text" width="35%" height="0.875rem" />
            <LuminarSkeleton variant="input" />
          </div>
        </div>
        <div className="space-y-2">
          <LuminarSkeleton variant="text" width="20%" height="0.875rem" />
          <LuminarSkeleton variant="rectangular" height="100px" />
        </div>
        <div className="flex justify-end space-x-3 pt-4">
          <LuminarSkeleton variant="button" />
          <LuminarSkeleton variant="button" width="8rem" />
        </div>
      </div>
    )
  }
)
LuminarSkeletonForm.displayName = "LuminarSkeletonForm"

const LuminarSkeletonChart = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "rounded-xl p-6 backdrop-blur-sm bg-white/5 border border-white/10",
          className
        )}
        {...props}
      >
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <LuminarSkeleton variant="text" width="40%" height="1.5rem" />
            <LuminarSkeleton variant="button" width="7rem" height="2rem" />
          </div>
          <LuminarSkeleton variant="chart" animation="shimmer" />
          <div className="grid grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="space-y-1">
                <LuminarSkeleton variant="text" width="60%" height="0.75rem" />
                <LuminarSkeleton variant="text" width="80%" height="1.25rem" />
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }
)
LuminarSkeletonChart.displayName = "LuminarSkeletonChart"

export { 
  LuminarSkeleton, 
  LuminarSkeletonCard, 
  LuminarSkeletonList, 
  LuminarSkeletonTable,
  LuminarSkeletonForm,
  LuminarSkeletonChart
}
export { LuminarSkeleton as LuminarSkeletonAdvanced }