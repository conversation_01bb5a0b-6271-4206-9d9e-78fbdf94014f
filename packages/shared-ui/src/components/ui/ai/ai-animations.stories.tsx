import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { Bo<PERSON> } from 'lucide-react'
import { 
  TypingIndicator, 
  StreamingPulse,
  ConnectionStatus,
  ProcessingIndicator,
  TextReveal,
  GlowingBorder,
  MessageAppearance,
  FloatingParticles,
  BreathingAvatar,
  StaggeredContainer,
  itemVariants
} from './ai-animations'

const meta = {
  title: 'UI/AI/AIAnimations',
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'dark',
    },
  },
  tags: ['autodocs'],
} satisfies Meta

export default meta

export const TypingIndicatorDemo: StoryObj<typeof TypingIndicator> = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-sm font-medium mb-3">Normal Speed</h3>
        <TypingIndicator speed="normal" />
      </div>
      <div>
        <h3 className="text-sm font-medium mb-3">Fast Speed</h3>
        <TypingIndicator speed="fast" />
      </div>
      <div>
        <h3 className="text-sm font-medium mb-3">Slow Speed</h3>
        <TypingIndicator speed="slow" />
      </div>
    </div>
  ),
}

export const StreamingPulseDemo: StoryObj<typeof StreamingPulse> = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-sm font-medium mb-3">Default</h3>
        <StreamingPulse />
      </div>
      <div>
        <h3 className="text-sm font-medium mb-3">Large Size</h3>
        <StreamingPulse size="lg" />
      </div>
      <div>
        <h3 className="text-sm font-medium mb-3">With Custom Color</h3>
        <StreamingPulse className="text-purple-500" />
      </div>
    </div>
  ),
}

export const ConnectionStatusDemo: StoryObj<typeof ConnectionStatus> = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-sm font-medium mb-3">Connected</h3>
        <ConnectionStatus status="connected" />
      </div>
      <div>
        <h3 className="text-sm font-medium mb-3">Connecting</h3>
        <ConnectionStatus status="connecting" />
      </div>
      <div>
        <h3 className="text-sm font-medium mb-3">Disconnected</h3>
        <ConnectionStatus status="disconnected" />
      </div>
      <div>
        <h3 className="text-sm font-medium mb-3">Error</h3>
        <ConnectionStatus status="disconnected" />
      </div>
    </div>
  ),
}

export const ProcessingIndicatorDemo: StoryObj<typeof ProcessingIndicator> = {
  render: () => {
    const [progress, setProgress] = React.useState(0)
    
    React.useEffect(() => {
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) return 0
          return prev + 5
        })
      }, 200)
      return () => clearInterval(interval)
    }, [])
    
    return (
      <div className="space-y-8 w-full max-w-md">
        <ProcessingIndicator progress={progress} isProcessing={true} />
      </div>
    )
  },
}

export const TextRevealDemo: StoryObj<typeof TextReveal> = {
  render: () => {
    const text = "Hello! I'm an AI assistant ready to help you with any questions or tasks you might have."
    
    return (
      <div className="space-y-8 max-w-2xl">
        <div>
          <h3 className="text-sm font-medium mb-3">Default Speed</h3>
          <div className="bg-background/50 p-4 rounded-lg">
            <TextReveal text={text} />
          </div>
        </div>
        <div>
          <h3 className="text-sm font-medium mb-3">Fast Speed</h3>
          <div className="bg-background/50 p-4 rounded-lg">
            <TextReveal text={text} speed={10} />
          </div>
        </div>
        <div>
          <h3 className="text-sm font-medium mb-3">Slow Speed</h3>
          <div className="bg-background/50 p-4 rounded-lg">
            <TextReveal text={text} speed={50} />
          </div>
        </div>
      </div>
    )
  },
}

export const GlowingBorderDemo: StoryObj<typeof GlowingBorder> = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-sm font-medium mb-3">Button with Glow</h3>
        <GlowingBorder active={true}>
          <button className="px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium">
            AI-Powered Button
          </button>
        </GlowingBorder>
      </div>
      <div>
        <h3 className="text-sm font-medium mb-3">Card with Glow</h3>
        <GlowingBorder active={true}>
          <div className="p-6 bg-card rounded-lg border border-border">
            <h4 className="font-semibold mb-2">AI Analysis</h4>
            <p className="text-sm text-muted-foreground">
              This card has an AI glow effect applied to it.
            </p>
          </div>
        </GlowingBorder>
      </div>
    </div>
  ),
}

export const MessageAppearanceDemo: StoryObj<typeof MessageAppearance> = {
  render: () => (
    <div className="space-y-8 max-w-md">
      <div>
        <h3 className="text-sm font-medium mb-3">User Message</h3>
        <MessageAppearance direction="right">
          <div className="bg-primary/10 p-4 rounded-lg">
            <p>How do I use the AI animations?</p>
          </div>
        </MessageAppearance>
      </div>
      <div>
        <h3 className="text-sm font-medium mb-3">Assistant Message</h3>
        <MessageAppearance direction="left">
          <div className="bg-secondary/10 p-4 rounded-lg">
            <p>The AI animations provide smooth visual feedback for various AI interactions.</p>
          </div>
        </MessageAppearance>
      </div>
    </div>
  ),
}

export const FloatingParticlesDemo: StoryObj<typeof FloatingParticles> = {
  render: () => (
    <div className="relative h-64 bg-background/50 rounded-lg overflow-hidden">
      <h3 className="absolute top-4 left-4 text-sm font-medium z-10">Floating Particles Effect</h3>
      <FloatingParticles active={true} />
    </div>
  ),
}

export const BreathingAvatarDemo: StoryObj<typeof BreathingAvatar> = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="text-sm font-medium mb-3">Active Avatar</h3>
        <BreathingAvatar active={true}>
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
            <Bot className="w-6 h-6 text-white" />
          </div>
        </BreathingAvatar>
      </div>
      <div>
        <h3 className="text-sm font-medium mb-3">Inactive Avatar</h3>
        <BreathingAvatar active={false}>
          <div className="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-600 rounded-full flex items-center justify-center">
            <Bot className="w-6 h-6 text-white" />
          </div>
        </BreathingAvatar>
      </div>
    </div>
  ),
}

export const StaggeredContainerDemo: StoryObj = {
  render: () => (
    <StaggeredContainer className="space-y-4">
      <motion.div variants={itemVariants} className="p-4 bg-card rounded-lg">
        <h4 className="font-semibold">Item 1</h4>
        <p className="text-sm text-muted-foreground">This appears first</p>
      </motion.div>
      <motion.div variants={itemVariants} className="p-4 bg-card rounded-lg">
        <h4 className="font-semibold">Item 2</h4>
        <p className="text-sm text-muted-foreground">This appears second</p>
      </motion.div>
      <motion.div variants={itemVariants} className="p-4 bg-card rounded-lg">
        <h4 className="font-semibold">Item 3</h4>
        <p className="text-sm text-muted-foreground">This appears third</p>
      </motion.div>
    </StaggeredContainer>
  ),
}

export const CombinedShowcase: StoryObj = {
  render: () => (
    <div className="space-y-12">
      <div className="text-center space-y-4">
        <h2 className="text-2xl font-bold">AI Animation Showcase</h2>
        <p className="text-muted-foreground">
          A collection of animations for AI-powered interfaces
        </p>
      </div>
      
      <div className="relative h-96 bg-gradient-to-br from-primary/5 via-background to-secondary/5 rounded-xl overflow-hidden">
        <FloatingParticles active={true} />
        
        <div className="relative z-10 h-full flex flex-col items-center justify-center space-y-8">
          <GlowingBorder active={true}>
            <div className="bg-background/80 backdrop-blur-sm p-6 rounded-lg">
              <BreathingAvatar active={true}>
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                  <Bot className="w-8 h-8 text-white" />
                </div>
              </BreathingAvatar>
            </div>
          </GlowingBorder>
          
          <div className="bg-background/80 backdrop-blur-sm p-6 rounded-lg max-w-md">
            <TextReveal 
              text="Welcome to the AI Animation showcase. These animations create engaging and responsive user experiences."
            />
          </div>
          
          <TypingIndicator isVisible={true} speed="normal" />
        </div>
      </div>
    </div>
  ),
}

// Add React import for demos that use hooks
import * as React from 'react'
import { motion } from 'framer-motion'