import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { authApi, type User, type LoginDto, type RegisterDto, type LoginResponse } from '../api';
import { toast } from '../toast';

interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  sessionId: string | null;
  oauthProvider: string | null;

  // Actions
  login: (credentials: LoginDto) => Promise<LoginResponse | null>;
  register: (data: RegisterDto) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  updateUser: (user: Partial<User>) => void;
  clearError: () => void;
  checkAuth: () => Promise<boolean>;
  oauthLogin: (provider: string) => Promise<void>;
  handleOAuthCallback: (code: string, state: string, provider: string) => Promise<boolean>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      sessionId: null,
      oauthProvider: null,

      // Login action
      login: async (credentials: LoginDto) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authApi.login(credentials);
          
          set({
            user: response.user,
            isAuthenticated: true,
            sessionId: response.sessionId,
            oauthProvider: null, // Clear OAuth provider on regular login
            isLoading: false,
            error: null,
          });

          toast.success('Login successful!');
          return response;
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Login failed';
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          toast.error(errorMessage);
          return null;
        }
      },

      // Register action
      register: async (data: RegisterDto) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authApi.register(data);
          set({ isLoading: false, error: null });
          
          if (response.requiresEmailVerification) {
            toast.info('Please check your email to verify your account.');
          } else {
            toast.success('Registration successful! You can now login.');
          }
          
          return true;
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Registration failed';
          set({ isLoading: false, error: errorMessage });
          toast.error(errorMessage);
          return false;
        }
      },

      // Logout action
      logout: async () => {
        const { sessionId } = get();
        const refreshToken = authApi.getRefreshToken();
        
        set({ isLoading: true });
        try {
          await authApi.logout(refreshToken || undefined, sessionId || undefined);
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          // Clear state regardless of API response
          set({
            user: null,
            isAuthenticated: false,
            sessionId: null,
            oauthProvider: null,
            isLoading: false,
            error: null,
          });
          toast.info('You have been logged out.');
        }
      },

      // Refresh user data
      refreshUser: async () => {
        const { isAuthenticated } = get();
        if (!isAuthenticated) return;

        try {
          const user = await authApi.getProfile();
          set({ user });
        } catch (error) {
          console.error('Failed to refresh user:', error);
          // If profile fetch fails, user might be logged out
          if ((error as any).response?.status === 401) {
            get().logout();
          }
        }
      },

      // Update user locally
      updateUser: (updates: Partial<User>) => {
        const { user } = get();
        if (user) {
          set({ user: { ...user, ...updates } });
        }
      },

      // Clear error
      clearError: () => set({ error: null }),

      // Check authentication status on app load
      checkAuth: async () => {
        const token = authApi.getAccessToken();
        if (!token) {
          set({ isAuthenticated: false, user: null });
          return false;
        }

        set({ isLoading: true });
        try {
          const user = await authApi.getProfile();
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
          });
          return true;
        } catch (error) {
          console.error('Auth check failed:', error);
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
          });
          return false;
        }
      },

      // OAuth login
      oauthLogin: async (provider: string) => {
        const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
        const redirectUri = encodeURIComponent(`${window.location.origin}/auth/callback`);
        const state = Math.random().toString(36).substring(2, 15);
        
        // Store state for verification
        sessionStorage.setItem('oauth_state', state);
        sessionStorage.setItem('oauth_provider', provider);
        
        // Redirect to OAuth provider
        window.location.href = `${baseUrl}/auth/oauth/${provider}?redirect_uri=${redirectUri}&state=${state}`;
      },

      // Handle OAuth callback
      handleOAuthCallback: async (code: string, state: string, provider: string) => {
        set({ isLoading: true, error: null });
        
        try {
          // Verify state
          const storedState = sessionStorage.getItem('oauth_state');
          const storedProvider = sessionStorage.getItem('oauth_provider');
          
          if (storedState !== state || storedProvider !== provider) {
            throw new Error('Invalid OAuth state or provider');
          }
          
          // Exchange code for tokens
          const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
          const response = await fetch(`${baseUrl}/auth/oauth/callback`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              code,
              state,
              provider,
              redirect_uri: `${window.location.origin}/auth/callback`
            }),
          });

          if (!response.ok) {
            throw new Error('OAuth callback failed');
          }

          const data = await response.json();
          
          // Store tokens
          if (data.tokens) {
            authApi.setTokens(data.tokens);
          }
          
          set({
            user: data.user,
            isAuthenticated: true,
            sessionId: data.sessionId,
            oauthProvider: provider,
            isLoading: false,
            error: null,
          });
          
          // Clean up
          sessionStorage.removeItem('oauth_state');
          sessionStorage.removeItem('oauth_provider');
          
          toast.success(`Successfully authenticated with ${provider}!`);
          return true;
          
        } catch (error: any) {
          const errorMessage = error.message || 'OAuth authentication failed';
          set({
            user: null,
            isAuthenticated: false,
            sessionId: null,
            oauthProvider: null,
            isLoading: false,
            error: errorMessage,
          });
          
          // Clean up
          sessionStorage.removeItem('oauth_state');
          sessionStorage.removeItem('oauth_provider');
          
          toast.error(errorMessage);
          return false;
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        sessionId: state.sessionId,
        oauthProvider: state.oauthProvider,
      }),
    }
  )
);

// Helper hooks
export const useUser = () => useAuthStore((state) => state.user);
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated);
export const useAuthLoading = () => useAuthStore((state) => state.isLoading);
export const useAuthActions = () => {
  const { login, register, logout, refreshUser, checkAuth, oauthLogin, handleOAuthCallback } = useAuthStore();
  return { login, register, logout, refreshUser, checkAuth, oauthLogin, handleOAuthCallback };
};

// Permission checking helper
export const useHasPermission = (permission: string): boolean => {
  const user = useUser();
  if (!user?.roles) return false;
  
  return user.roles.some(role => 
    role.permissions.includes(permission)
  );
};

// Role checking helper
export const useHasRole = (roleName: string): boolean => {
  const user = useUser();
  if (!user?.roles) return false;
  
  return user.roles.some(role => role.name === roleName);
};

// Multiple roles checking helper
export const useHasAnyRole = (roleNames: string[]): boolean => {
  const user = useUser();
  if (!user?.roles) return false;
  
  return user.roles.some(role => roleNames.includes(role.name));
};