import React, { useState, useRef, useCallback, useMemo } from 'react';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface GraphNode {
  id: string;
  label: string;
  x: number;
  y: number;
  size?: number;
  color?: string;
  group?: string;
  data?: any;
}

export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  weight?: number;
  color?: string;
  style?: 'solid' | 'dashed' | 'dotted';
}

export interface KnowledgeGraphProps {
  nodes: GraphNode[];
  edges: GraphEdge[];
  width?: number;
  height?: number;
  onNodeClick?: (node: GraphNode) => void;
  onNodeHover?: (node: GraphNode | null) => void;
  onEdgeClick?: (edge: GraphEdge) => void;
  interactive?: boolean;
  showLabels?: boolean;
  theme?: 'light' | 'dark' | 'auto';
  className?: string;
}

export function LuminarKnowledgeGraph({
  nodes,
  edges,
  width = 800,
  height = 600,
  onNodeClick,
  onNodeHover,
  onEdgeClick,
  interactive = true,
  showLabels = true,
  theme = 'auto',
  className = ''
}: KnowledgeGraphProps) {
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);
  const [draggedNode, setDraggedNode] = useState<string | null>(null);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const svgRef = useRef<SVGSVGElement>(null);

  const glassClasses = getAdaptiveGlassClasses('card', 'md', 'neutral');

  // Node colors by group
  const nodeColors = useMemo(() => ({
    default: '#6366f1',
    primary: '#3b82f6',
    secondary: '#8b5cf6',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#06b6d4'
  }), []);

  // Handle node interactions
  const handleNodeClick = useCallback((node: GraphNode) => {
    setSelectedNode(node.id);
    onNodeClick?.(node);
  }, [onNodeClick]);

  const handleNodeMouseEnter = useCallback((node: GraphNode) => {
    if (interactive) {
      setHoveredNode(node.id);
      onNodeHover?.(node);
    }
  }, [interactive, onNodeHover]);

  const handleNodeMouseLeave = useCallback(() => {
    if (interactive) {
      setHoveredNode(null);
      onNodeHover?.(null);
    }
  }, [interactive, onNodeHover]);

  // Handle drag operations
  const handleMouseDown = useCallback((event: React.MouseEvent, nodeId: string) => {
    if (interactive) {
      setDraggedNode(nodeId);
      event.preventDefault();
    }
  }, [interactive]);

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (draggedNode && interactive) {
      const rect = svgRef.current?.getBoundingClientRect();
      if (rect) {
        const x = (event.clientX - rect.left) / zoom - pan.x;
        const y = (event.clientY - rect.top) / zoom - pan.y;
        // Update node position (would need state management for real implementation)
      }
    }
  }, [draggedNode, interactive, zoom, pan]);

  const handleMouseUp = useCallback(() => {
    setDraggedNode(null);
  }, []);

  // Zoom controls
  const handleZoomIn = () => setZoom(prev => Math.min(prev * 1.2, 3));
  const handleZoomOut = () => setZoom(prev => Math.max(prev / 1.2, 0.3));
  const handleZoomReset = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  };

  // Get node color
  const getNodeColor = (node: GraphNode) => {
    if (node.color) return node.color;
    if (node.group && nodeColors[node.group as keyof typeof nodeColors]) {
      return nodeColors[node.group as keyof typeof nodeColors];
    }
    return nodeColors.default;
  };

  // Get node size
  const getNodeSize = (node: GraphNode) => {
    const baseSize = node.size || 12;
    if (selectedNode === node.id) return baseSize * 1.3;
    if (hoveredNode === node.id) return baseSize * 1.1;
    return baseSize;
  };

  // Check if edge is connected to selected/hovered node
  const isEdgeHighlighted = (edge: GraphEdge) => {
    return selectedNode === edge.source || selectedNode === edge.target ||
           hoveredNode === edge.source || hoveredNode === edge.target;
  };

  return (
    <div className={`${glassClasses} rounded-lg border p-4 ${className}`}>
      {/* Controls */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <button
            onClick={handleZoomIn}
            className="p-2 rounded-md hover:bg-muted transition-colors"
            title="Zoom In"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>
          <button
            onClick={handleZoomOut}
            className="p-2 rounded-md hover:bg-muted transition-colors"
            title="Zoom Out"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 12H6" />
            </svg>
          </button>
          <button
            onClick={handleZoomReset}
            className="p-2 rounded-md hover:bg-muted transition-colors"
            title="Reset View"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>{nodes.length} nodes</span>
          <span>•</span>
          <span>{edges.length} edges</span>
        </div>
      </div>

      {/* Graph SVG */}
      <div className="relative overflow-hidden rounded-md bg-background/50">
        <svg
          ref={svgRef}
          width={width}
          height={height}
          viewBox={`0 0 ${width} ${height}`}
          className="cursor-grab active:cursor-grabbing"
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          {/* Background grid */}
          <defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="currentColor" strokeWidth="0.5" opacity="0.1" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* Zoom and pan group */}
          <g transform={`translate(${pan.x}, ${pan.y}) scale(${zoom})`}>
            {/* Edges */}
            {edges.map(edge => {
              const sourceNode = nodes.find(n => n.id === edge.source);
              const targetNode = nodes.find(n => n.id === edge.target);
              
              if (!sourceNode || !targetNode) return null;

              const isHighlighted = isEdgeHighlighted(edge);
              
              return (
                <g key={edge.id}>
                  <line
                    x1={sourceNode.x}
                    y1={sourceNode.y}
                    x2={targetNode.x}
                    y2={targetNode.y}
                    stroke={edge.color || (isHighlighted ? '#6366f1' : '#6b7280')}
                    strokeWidth={edge.weight || (isHighlighted ? 2 : 1)}
                    strokeDasharray={edge.style === 'dashed' ? '5,5' : edge.style === 'dotted' ? '2,2' : 'none'}
                    opacity={isHighlighted ? 1 : 0.6}
                    className={interactive ? 'cursor-pointer' : ''}
                    onClick={() => onEdgeClick?.(edge)}
                  />
                  {edge.label && showLabels && (
                    <text
                      x={(sourceNode.x + targetNode.x) / 2}
                      y={(sourceNode.y + targetNode.y) / 2}
                      textAnchor="middle"
                      dominantBaseline="middle"
                      fontSize="10"
                      fill="currentColor"
                      className="text-muted-foreground"
                    >
                      {edge.label}
                    </text>
                  )}
                </g>
              );
            })}

            {/* Nodes */}
            {nodes.map(node => {
              const nodeSize = getNodeSize(node);
              const nodeColor = getNodeColor(node);
              const isSelected = selectedNode === node.id;
              const hovered = hoveredNode === node.id;

              return (
                <g key={node.id}>
                  {/* Node selection ring */}
                  {isSelected && (
                    <circle
                      cx={node.x}
                      cy={node.y}
                      r={nodeSize + 4}
                      fill="none"
                      stroke={nodeColor}
                      strokeWidth="2"
                      opacity="0.5"
                    />
                  )}
                  
                  {/* Node */}
                  <circle
                    cx={node.x}
                    cy={node.y}
                    r={nodeSize}
                    fill={nodeColor}
                    stroke={hovered ? '#ffffff' : 'none'}
                    strokeWidth="2"
                    opacity={hovered ? 1 : 0.9}
                    className={interactive ? 'cursor-pointer' : ''}
                    onClick={() => handleNodeClick(node)}
                    onMouseEnter={() => handleNodeMouseEnter(node)}
                    onMouseLeave={handleNodeMouseLeave}
                    onMouseDown={(e) => handleMouseDown(e, node.id)}
                  />

                  {/* Node label */}
                  {showLabels && (
                    <text
                      x={node.x}
                      y={node.y + nodeSize + 12}
                      textAnchor="middle"
                      dominantBaseline="middle"
                      fontSize="11"
                      fill="currentColor"
                      className="text-foreground font-medium"
                    >
                      {node.label}
                    </text>
                  )}
                </g>
              );
            })}
          </g>
        </svg>

        {/* Node info tooltip */}
        {hoveredNode && (
          <div className="absolute top-4 left-4 bg-popover text-popover-foreground p-3 rounded-md shadow-lg border max-w-sm">
            {(() => {
              const node = nodes.find(n => n.id === hoveredNode);
              return node ? (
                <div>
                  <div className="font-medium">{node.label}</div>
                  <div className="text-sm text-muted-foreground">ID: {node.id}</div>
                  {node.group && (
                    <div className="text-sm text-muted-foreground">Group: {node.group}</div>
                  )}
                  {node.data && (
                    <div className="text-sm text-muted-foreground mt-1">
                      {Object.entries(node.data).map(([key, value]) => (
                        <div key={key}>{key}: {String(value)}</div>
                      ))}
                    </div>
                  )}
                </div>
              ) : null;
            })()}
          </div>
        )}
      </div>
    </div>
  );
}

// Sample data generator for demo purposes
export function generateSampleGraphData(nodeCount: number = 20, edgeCount: number = 30): { nodes: GraphNode[], edges: GraphEdge[] } {
  const nodes: GraphNode[] = [];
  const edges: GraphEdge[] = [];
  
  const groups = ['primary', 'secondary', 'success', 'warning', 'info'];
  const topics = ['AI', 'ML', 'Data', 'Web', 'Mobile', 'Cloud', 'Security', 'UI/UX', 'DevOps', 'Analytics'];

  // Generate nodes
  for (let i = 0; i < nodeCount; i++) {
    nodes.push({
      id: `node-${i}`,
      label: topics[i % topics.length] + ` ${Math.floor(i / topics.length) + 1}`,
      x: Math.random() * 600 + 100,
      y: Math.random() * 400 + 100,
      size: Math.random() * 8 + 8,
      group: groups[Math.floor(Math.random() * groups.length)],
      data: {
        importance: Math.random(),
        connections: Math.floor(Math.random() * 5) + 1
      }
    });
  }

  // Generate edges
  for (let i = 0; i < edgeCount; i++) {
    const source = nodes[Math.floor(Math.random() * nodes.length)];
    const target = nodes[Math.floor(Math.random() * nodes.length)];
    
    if (source.id !== target.id) {
      edges.push({
        id: `edge-${i}`,
        source: source.id,
        target: target.id,
        weight: Math.random() * 3 + 1,
        label: Math.random() > 0.7 ? `rel-${i}` : undefined,
        style: Math.random() > 0.8 ? 'dashed' : 'solid'
      });
    }
  }

  return { nodes, edges };
}