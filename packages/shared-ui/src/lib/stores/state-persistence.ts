import { StateStorage } from 'zustand/middleware';

// Enhanced storage with encryption support
export interface EnhancedStateStorage extends StateStorage {
  encrypt?: boolean;
  compress?: boolean;
  version?: number;
}

// Simple encryption for sensitive data
const encrypt = (text: string): string => {
  if (typeof window === 'undefined') return text;
  
  try {
    return btoa(text);
  } catch {
    return text;
  }
};

const decrypt = (encryptedText: string): string => {
  if (typeof window === 'undefined') return encryptedText;
  
  try {
    return atob(encryptedText);
  } catch {
    return encryptedText;
  }
};

// Simple compression using JSON stringification optimization
const compress = (data: string): string => {
  try {
    const parsed = JSON.parse(data);
    return JSON.stringify(parsed, null, 0);
  } catch {
    return data;
  }
};

const decompress = (compressedData: string): string => {
  return compressedData; // Already decompressed by JSON.parse
};

// Enhanced localStorage with features
export const createEnhancedStorage = (options: {
  encrypt?: boolean;
  compress?: boolean;
  version?: number;
  prefix?: string;
} = {}): EnhancedStateStorage => {
  const { encrypt: shouldEncrypt, compress: shouldCompress, version = 1, prefix = 'luminar' } = options;
  
  return {
    getItem: (name: string): string | null => {
      try {
        const key = `${prefix}_${name}`;
        let item = localStorage.getItem(key);
        
        if (!item) return null;
        
        // Handle versioning
        let parsedItem: any;
        try {
          parsedItem = JSON.parse(item);
        } catch {
          // Legacy format, migrate
          parsedItem = { data: item, version: 1 };
        }
        
        // Version check
        if (parsedItem.version !== version) {
          console.warn(`Storage version mismatch for ${name}. Expected ${version}, got ${parsedItem.version}`);
          return null;
        }
        
        let data = parsedItem.data;
        
        // Decrypt if needed
        if (shouldEncrypt) {
          data = decrypt(data);
        }
        
        // Decompress if needed
        if (shouldCompress) {
          data = decompress(data);
        }
        
        return data;
      } catch (error) {
        console.error(`Error getting item ${name}:`, error);
        return null;
      }
    },
    
    setItem: (name: string, value: string): void => {
      try {
        const key = `${prefix}_${name}`;
        let data = value;
        
        // Compress if needed
        if (shouldCompress) {
          data = compress(data);
        }
        
        // Encrypt if needed
        if (shouldEncrypt) {
          data = encrypt(data);
        }
        
        // Add versioning
        const versionedData = JSON.stringify({
          data,
          version,
          timestamp: Date.now(),
        });
        
        localStorage.setItem(key, versionedData);
      } catch (error) {
        console.error(`Error setting item ${name}:`, error);
      }
    },
    
    removeItem: (name: string): void => {
      try {
        const key = `${prefix}_${name}`;
        localStorage.removeItem(key);
      } catch (error) {
        console.error(`Error removing item ${name}:`, error);
      }
    },
  };
};

// Session storage variant
export const createSessionStorage = (options: {
  encrypt?: boolean;
  compress?: boolean;
  version?: number;
  prefix?: string;
} = {}): EnhancedStateStorage => {
  const { encrypt: shouldEncrypt, compress: shouldCompress, version = 1, prefix = 'luminar' } = options;
  
  return {
    getItem: (name: string): string | null => {
      try {
        const key = `${prefix}_${name}`;
        let item = sessionStorage.getItem(key);
        
        if (!item) return null;
        
        // Handle versioning
        let parsedItem: any;
        try {
          parsedItem = JSON.parse(item);
        } catch {
          // Legacy format, migrate
          parsedItem = { data: item, version: 1 };
        }
        
        // Version check
        if (parsedItem.version !== version) {
          console.warn(`Session storage version mismatch for ${name}. Expected ${version}, got ${parsedItem.version}`);
          return null;
        }
        
        let data = parsedItem.data;
        
        // Decrypt if needed
        if (shouldEncrypt) {
          data = decrypt(data);
        }
        
        // Decompress if needed
        if (shouldCompress) {
          data = decompress(data);
        }
        
        return data;
      } catch (error) {
        console.error(`Error getting session item ${name}:`, error);
        return null;
      }
    },
    
    setItem: (name: string, value: string): void => {
      try {
        const key = `${prefix}_${name}`;
        let data = value;
        
        // Compress if needed
        if (shouldCompress) {
          data = compress(data);
        }
        
        // Encrypt if needed
        if (shouldEncrypt) {
          data = encrypt(data);
        }
        
        // Add versioning
        const versionedData = JSON.stringify({
          data,
          version,
          timestamp: Date.now(),
        });
        
        sessionStorage.setItem(key, versionedData);
      } catch (error) {
        console.error(`Error setting session item ${name}:`, error);
      }
    },
    
    removeItem: (name: string): void => {
      try {
        const key = `${prefix}_${name}`;
        sessionStorage.removeItem(key);
      } catch (error) {
        console.error(`Error removing session item ${name}:`, error);
      }
    },
  };
};

// Memory storage for testing or when localStorage is not available
export const createMemoryStorage = (): EnhancedStateStorage => {
  const storage = new Map<string, string>();
  
  return {
    getItem: (name: string): string | null => {
      return storage.get(name) || null;
    },
    
    setItem: (name: string, value: string): void => {
      storage.set(name, value);
    },
    
    removeItem: (name: string): void => {
      storage.delete(name);
    },
  };
};

// IndexedDB storage for larger datasets
export const createIndexedDBStorage = (dbName: string = 'luminar-storage'): EnhancedStateStorage => {
  let db: IDBDatabase | null = null;
  
  // Initialize IndexedDB
  const initDB = async (): Promise<IDBDatabase> => {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(dbName, 1);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains('store')) {
          db.createObjectStore('store', { keyPath: 'key' });
        }
      };
    });
  };
  
  const getDB = async (): Promise<IDBDatabase> => {
    if (!db) {
      db = await initDB();
    }
    return db;
  };
  
  return {
    getItem: (name: string): string | null => {
      // Synchronous fallback - return null and load asynchronously
      // In a real implementation, you'd want to handle this differently
      return null;
    },
    
    setItem: (name: string, value: string): void => {
      getDB().then(db => {
        const transaction = db.transaction(['store'], 'readwrite');
        const store = transaction.objectStore('store');
        store.put({ key: name, value, timestamp: Date.now() });
      }).catch(error => {
        console.error('Error setting IndexedDB item:', error);
      });
    },
    
    removeItem: (name: string): void => {
      getDB().then(db => {
        const transaction = db.transaction(['store'], 'readwrite');
        const store = transaction.objectStore('store');
        store.delete(name);
      }).catch(error => {
        console.error('Error removing IndexedDB item:', error);
      });
    },
  };
};

// Utility to clear all storage
export const clearAllStorage = (prefix: string = 'luminar'): void => {
  try {
    // Clear localStorage
    const localStorageKeys = Object.keys(localStorage).filter(key => key.startsWith(prefix));
    localStorageKeys.forEach(key => localStorage.removeItem(key));
    
    // Clear sessionStorage
    const sessionStorageKeys = Object.keys(sessionStorage).filter(key => key.startsWith(prefix));
    sessionStorageKeys.forEach(key => sessionStorage.removeItem(key));
    
    console.log(`Cleared ${localStorageKeys.length} localStorage items and ${sessionStorageKeys.length} sessionStorage items`);
  } catch (error) {
    console.error('Error clearing storage:', error);
  }
};

// Storage migration utility
export const migrateStorage = async (from: EnhancedStateStorage, to: EnhancedStateStorage, keys: string[]): Promise<void> => {
  for (const key of keys) {
    try {
      const value = from.getItem(key);
      if (value !== null) {
        // Handle both sync and async storage APIs
        const resolvedValue = typeof value === 'string' ? value : String(value);
        if (resolvedValue !== null) {
          to.setItem(key, resolvedValue);
          from.removeItem(key);
        }
      }
    } catch (error) {
      console.error(`Error migrating key ${key}:`, error);
    }
  }
};

// Storage health check
export const checkStorageHealth = (): {
  localStorage: boolean;
  sessionStorage: boolean;
  indexedDB: boolean;
} => {
  const result = {
    localStorage: false,
    sessionStorage: false,
    indexedDB: false,
  };
  
  try {
    // Test localStorage
    const testKey = 'luminar-storage-test';
    localStorage.setItem(testKey, 'test');
    localStorage.removeItem(testKey);
    result.localStorage = true;
  } catch {
    result.localStorage = false;
  }
  
  try {
    // Test sessionStorage
    const testKey = 'luminar-session-test';
    sessionStorage.setItem(testKey, 'test');
    sessionStorage.removeItem(testKey);
    result.sessionStorage = true;
  } catch {
    result.sessionStorage = false;
  }
  
  try {
    // Test IndexedDB
    result.indexedDB = 'indexedDB' in window;
  } catch {
    result.indexedDB = false;
  }
  
  return result;
};

// Default storage based on availability
export const getDefaultStorage = (): EnhancedStateStorage => {
  const health = checkStorageHealth();
  
  if (health.localStorage) {
    return createEnhancedStorage();
  } else if (health.sessionStorage) {
    return createSessionStorage();
  } else {
    return createMemoryStorage();
  }
};