import React from "react"
import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, LabelHTMLAttributes } from "react"
import { microInteractions } from '../../../lib/micro-interactions'
import {
  type StandardComponentProps,
  type StandardFormComponentProps,
  type ComponentSize,
  type ComponentVariant,
  type GlassIntensity,
  type AnimationPreset,
  defaultComponentProps
} from '../../../types/component-props';
import { componentSizes, animationPresets, transitions, type ColorTheme } from '../../../design-system';
import { createGlassStyles } from '../../../lib/glass-utils';

export interface LuminarLabelProps extends LabelHTMLAttributes<HTMLLabelElement> {
  // Core properties
  variant?: ComponentVariant | "glass" | "inline" | "floating"
  size?: ComponentSize
  
  // Glass properties
  glassIntensity?: GlassIntensity
  colorTheme?: ColorTheme
  
  // Visual properties
  required?: boolean
  disabled?: boolean
  error?: boolean
  
  // Interactive properties
  interactive?: boolean
  hoverable?: boolean
  
  // Animation properties
  animation?: AnimationPreset
  disableAnimation?: boolean
  
  // Typography properties
  weight?: "normal" | "medium" | "semibold" | "bold"
  transform?: "none" | "uppercase" | "lowercase" | "capitalize"
  
  // Icon properties
  icon?: React.ElementType
  iconPosition?: "left" | "right"
  
  // Floating label properties
  floatingLabel?: string
  floatingPlaceholder?: string
  floatingActive?: boolean
}

const LuminarLabel = forwardRef<HTMLLabelElement, LuminarLabelProps>(
  ({ 
    className,
    children,
    // Core properties
    variant = defaultComponentProps.variant,
    size = defaultComponentProps.size,
    
    // Glass properties
    glassIntensity = "light",
    colorTheme = "neutral",
    
    // Visual properties
    required = false,
    disabled = false,
    error = false,
    
    // Interactive properties
    interactive = false,
    hoverable = true,
    
    // Animation properties
    animation = "fadeIn",
    disableAnimation = false,
    
    // Typography properties
    weight = "medium",
    transform = "none",
    
    // Icon properties
    icon: Icon,
    iconPosition = "left",
    
    // Floating label properties
    floatingLabel,
    floatingPlaceholder,
    floatingActive = false,
    
    ...props 
  }, ref) => {
    
    const sizeClasses = size ? componentSizes[size] : componentSizes.md
    const animationProps = disableAnimation ? {} : (animation ? animationPresets[animation] : {})

    // Base variant styles
    const variantStyles = {
      default: cn(
        "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
        error && "text-destructive",
        disabled && "text-muted-foreground cursor-not-allowed"
      ),
      glass: cn(
        "text-sm font-medium leading-none backdrop-blur-sm rounded-md px-2 py-1",
        createGlassStyles({
          element: 'card',
          profile: glassIntensity === 'subtle' ? 'soft' : glassIntensity === 'strong' ? 'hard' : 'standard',
          interactive: interactive || hoverable,
          frost: true,
          glow: false
        }),
        error && "border-destructive text-destructive",
        disabled && "opacity-50 cursor-not-allowed"
      ),
      inline: cn(
        "inline-flex items-center text-sm font-medium leading-none",
        error && "text-destructive",
        disabled && "text-muted-foreground cursor-not-allowed"
      ),
      floating: cn(
        "absolute left-3 transition-all duration-200 pointer-events-none",
        "text-sm font-medium leading-none",
        floatingActive || children 
          ? "top-2 text-xs text-muted-foreground" 
          : "top-1/2 -translate-y-1/2 text-muted-foreground",
        error && "text-destructive",
        disabled && "text-muted-foreground"
      )
    }

    // Weight classes
    const weightClasses = {
      normal: "font-normal",
      medium: "font-medium",
      semibold: "font-semibold",
      bold: "font-bold"
    }

    // Transform classes
    const transformClasses = {
      none: "",
      uppercase: "uppercase",
      lowercase: "lowercase",
      capitalize: "capitalize"
    }

    // Interactive effects
    const interactionProps = (interactive || hoverable) && !disableAnimation && !disabled ? {
      ...microInteractions.glassHover,
      whileTap: interactive ? microInteractions.buttonPress.whileTap : undefined
    } : {}

    // Floating label variant
    if (variant === "floating") {
      return (
        <motion.label
          ref={ref}
          className={cn(
            variantStyles[variant],
            sizeClasses.text,
            weightClasses[weight],
            transformClasses[transform],
            className
          )}
          {...(animationProps as any)}
          {...interactionProps}
          transition={transitions.spring}
          {...props}
        >
          {floatingActive ? floatingLabel : floatingPlaceholder}
          {required && <span className="text-destructive ml-1">*</span>}
        </motion.label>
      )
    }

    // Regular label variants
    return (
      <motion.label
        ref={ref}
        className={cn(
          variant && variantStyles[variant as keyof typeof variantStyles] ? variantStyles[variant as keyof typeof variantStyles] : variantStyles.default,
          sizeClasses.text,
          weightClasses[weight],
          transformClasses[transform],
          (interactive || hoverable) && !disabled && "cursor-pointer",
          className
        )}
        {...(animationProps as any)}
        {...interactionProps}
        transition={transitions.spring}
        {...props}
      >
        {/* Icon - Left */}
        {Icon && iconPosition === "left" && (
          <motion.div
            className={cn(
              "flex items-center mr-2",
              sizeClasses.icon,
              error && "text-destructive",
              disabled && "text-muted-foreground"
            )}
            {...(!disableAnimation && (microInteractions.pulse as any))}
          >
            <Icon className={cn(sizeClasses.icon)} />
          </motion.div>
        )}

        {/* Label content */}
        <span className="select-none">
          {children}
          {required && <span className="text-destructive ml-1">*</span>}
        </span>

        {/* Icon - Right */}
        {Icon && iconPosition === "right" && (
          <motion.div
            className={cn(
              "flex items-center ml-2",
              sizeClasses.icon,
              error && "text-destructive",
              disabled && "text-muted-foreground"
            )}
            {...(!disableAnimation && (microInteractions.pulse as any))}
          >
            <Icon className={cn(sizeClasses.icon)} />
          </motion.div>
        )}
      </motion.label>
    )
  }
)

// Form Label component (with form integration)
export interface LuminarFormLabelProps extends LuminarLabelProps {
  name?: string
  description?: string
  showOptional?: boolean
  optionalText?: string
}

const LuminarFormLabel = forwardRef<HTMLLabelElement, LuminarFormLabelProps>(
  ({ 
    className,
    children,
    name,
    description,
    showOptional = false,
    optionalText = "Optional",
    required = false,
    ...props 
  }, ref) => {
    return (
      <div className="space-y-1">
        <LuminarLabel
          ref={ref}
          htmlFor={name}
          required={required}
          className={cn(
            "flex items-center justify-between",
            className
          )}
          {...props}
        >
          <span>{children}</span>
          {!required && showOptional && (
            <span className="text-xs text-muted-foreground font-normal">
              {optionalText}
            </span>
          )}
        </LuminarLabel>
        
        {description && (
          <p className="text-sm text-muted-foreground">
            {description}
          </p>
        )}
      </div>
    )
  }
)

// Field Label component (for form fields)
export interface LuminarFieldLabelProps extends LuminarLabelProps {
  field?: string
  help?: string
  tooltip?: string
}

const LuminarFieldLabel = forwardRef<HTMLLabelElement, LuminarFieldLabelProps>(
  ({ 
    className,
    children,
    field,
    help,
    tooltip,
    ...props 
  }, ref) => {
    return (
      <div className="space-y-1">
        <LuminarLabel
          ref={ref}
          htmlFor={field}
          className={cn(
            "flex items-center gap-2",
            className
          )}
          {...props}
        >
          {children}
          {tooltip && (
            <span 
              className="text-xs text-muted-foreground cursor-help"
              title={tooltip}
            >
              ?
            </span>
          )}
        </LuminarLabel>
        
        {help && (
          <p className="text-xs text-muted-foreground">
            {help}
          </p>
        )}
      </div>
    )
  }
)

// Set display names
LuminarLabel.displayName = "LuminarLabel"
LuminarFormLabel.displayName = "LuminarFormLabel"
LuminarFieldLabel.displayName = "LuminarFieldLabel"

export {
  LuminarLabel,
  LuminarFormLabel,
  LuminarFieldLabel,
}

// Simplified API for quick usage (shadcn/ui style)
export const Label = LuminarLabel
export const FormLabel = LuminarFormLabel
export const FieldLabel = LuminarFieldLabel

// Utility functions
export const createFormLabel = (text: string, required: boolean = false) => {
  return (
    <LuminarLabel required={required}>
      {text}
    </LuminarLabel>
  )
}

export const createFloatingLabel = (
  label: string, 
  placeholder: string, 
  active: boolean = false
) => {
  return (
    <LuminarLabel
      variant="floating"
      floatingLabel={label}
      floatingPlaceholder={placeholder}
      floatingActive={active}
    />
  )
}