import { motion, AnimatePresence } from "framer-motion";
import { forwardRef, useEffect, useRef, useState } from "react";
import { Send, Bot, User, Co<PERSON>, ThumbsUp, ThumbsDown, RotateCcw } from "lucide-react";
import { cn } from '../../../lib/utils';
import { LuminarButton } from "../actions/button-advanced";
import { LuminarInput } from "../forms/input";
import { LuminarCard } from "../display/card";
import { LuminarText } from "../display/text";
import { LuminarTooltip } from "../feedback/tooltip";
import { LoadingSpinner } from "../display/loading-spinner";
import { 
  getGlassClasses, 
  animationPresets, 
  type ComponentSize
} from "../../../design-system";
import { ResearchMessage } from '../../../types/research';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface ResearchChatProps {
  messages: ResearchMessage[];
  currentMessage: string;
  isTyping: boolean;
  onMessageChange: (message: string) => void;
  onSendMessage: (message: string) => void;
  onClearChat: () => void;
  className?: string;
  size?: ComponentSize;
  disabled?: boolean;
  placeholder?: string;
  showTimestamps?: boolean;
  enableMarkdown?: boolean;
  maxHeight?: string;
}

const ResearchChat = forwardRef<HTMLDivElement, ResearchChatProps>(
  ({
    messages,
    currentMessage,
    isTyping,
    onMessageChange,
    onSendMessage,
    onClearChat,
    className,
    size = "md",
    disabled = false,
    placeholder = "Ask me anything about your research...",
    showTimestamps = true,
    enableMarkdown = true,
    maxHeight = "600px",
    ...props
  }, ref) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);

    // Auto-scroll to bottom when new messages arrive
    useEffect(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [messages, isTyping]);

    // Handle message submission
    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (currentMessage.trim() && !disabled && !isTyping) {
        onSendMessage(currentMessage.trim());
      }
    };

    // Handle keyboard shortcuts
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSubmit(e);
      }
    };

    // Copy message content
    const handleCopyMessage = async (messageId: string, content: string) => {
      try {
        await navigator.clipboard.writeText(content);
        setCopiedMessageId(messageId);
        setTimeout(() => setCopiedMessageId(null), 2000);
      } catch (error) {
        console.error('Failed to copy message:', error);
      }
    };

    // Message component
    const MessageBubble = ({ message, index }: { message: ResearchMessage; index: number }) => {
      const isUser = message.type === 'user';
      const isSystem = message.type === 'system';
      const glassClasses = getGlassClasses(
        isUser ? 'button' : 'card',
        {
          intensity: isUser ? 'md' : 'light',
          depth: 'surface',
          animated: true,
          interactive: false
        }
      );

      return (
        <motion.div
          key={message.id}
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ 
            type: "spring" as const,
            stiffness: 300,
            damping: 30,
            delay: index * 0.1 
          }}
          className={cn(
            "flex gap-3 max-w-[85%]",
            isUser ? "ml-auto flex-row-reverse" : "mr-auto",
            isSystem && "justify-center"
          )}
        >
          {/* Avatar */}
          {!isSystem && (
            <div className={cn(
              "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",
              glassClasses,
              isUser ? "bg-blue-500/20" : "bg-gray-500/20"
            )}>
              {isUser ? (
                <User className="w-4 h-4 text-blue-400" />
              ) : (
                <Bot className="w-4 h-4 text-gray-400" />
              )}
            </div>
          )}

          {/* Message Content */}
          <div className={cn(
            "flex flex-col gap-2",
            isSystem ? "max-w-xs" : "flex-1"
          )}>
            {/* Message bubble */}
            <div className={cn(
              "relative p-3 rounded-lg",
              glassClasses,
              isUser && "bg-blue-500/10",
              isSystem && "bg-yellow-500/10 text-center"
            )}>
              {/* Message text */}
              <LuminarText 
                text={message.content}
                className={cn(
                  "text-sm whitespace-pre-wrap",
                  isUser ? "text-blue-100" : "text-gray-100"
                )}
              />

              {/* Message actions */}
              {!isSystem && (
                <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="flex gap-1">
                    <LuminarTooltip content={copiedMessageId === message.id ? "Copied!" : "Copy message"}>
                      <LuminarButton
                        size="xs"
                        variant="glass"
                        onClick={() => handleCopyMessage(message.id, message.content)}
                      >
                        <Copy className="w-3 h-3" />
                      </LuminarButton>
                    </LuminarTooltip>
                    
                    {!isUser && (
                      <>
                        <LuminarTooltip content="Good response">
                          <LuminarButton
                            size="xs"
                            variant="glass"
                          >
                            <ThumbsUp className="w-3 h-3" />
                          </LuminarButton>
                        </LuminarTooltip>
                        
                        <LuminarTooltip content="Regenerate">
                          <LuminarButton
                            size="xs"
                            variant="glass"
                          >
                            <RotateCcw className="w-3 h-3" />
                          </LuminarButton>
                        </LuminarTooltip>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Timestamp and metadata */}
            {showTimestamps && (
              <div className={cn(
                "text-xs text-gray-400 px-2",
                isUser ? "text-right" : "text-left"
              )}>
                {message.timestamp.toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
                {message.metadata?.processingTime && (
                  <span className="ml-2 opacity-60">
                    ({message.metadata.processingTime}ms)
                  </span>
                )}
              </div>
            )}

            {/* Sources preview */}
            {message.sources && message.sources.length > 0 && (
              <div className="flex gap-2 mt-2">
                {message.sources.slice(0, 3).map((source, idx) => (
                  <motion.div
                    key={source.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: idx * 0.1 }}
                    className="text-xs px-2 py-1 rounded bg-gray-500/20 text-gray-300 truncate max-w-32"
                  >
                    {source.title}
                  </motion.div>
                ))}
                {message.sources.length > 3 && (
                  <div className="text-xs px-2 py-1 rounded bg-gray-500/20 text-gray-300">
                    +{message.sources.length - 3} more
                  </div>
                )}
              </div>
            )}
          </div>
        </motion.div>
      );
    };

    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col h-full",
          className
        )}
        {...props}
      >
        {/* Messages Container */}
        <div
          className="flex-1 overflow-y-auto p-4 space-y-4"
          style={{ maxHeight }}
        >
          <AnimatePresence mode="popLayout">
            {messages.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex flex-col items-center justify-center h-full text-center"
              >
                <motion.div
                  animate={{ 
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="mb-4"
                >
                  <Bot className="w-12 h-12 text-gray-400" />
                </motion.div>
                <LuminarText 
                  text="Start a Research Conversation"
                  className="text-lg font-medium text-gray-300 mb-2"
                />
                <LuminarText 
                  text="Ask me anything about your research topic. I can help you find information, analyze sources, and generate citations."
                  className="text-sm text-gray-500 max-w-md"
                />
              </motion.div>
            ) : (
              <>
                {messages.map((message, index) => (
                  <div key={message.id} className="group">
                    <MessageBubble message={message} index={index} />
                  </div>
                ))}
                
                {/* Typing indicator */}
                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="flex gap-3 max-w-[85%] mr-auto"
                  >
                    <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 bg-gray-500/20 backdrop-blur-sm">
                      <Bot className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="flex items-center gap-2 p-3 rounded-lg bg-gray-500/10 backdrop-blur-sm">
                      <LoadingSpinner size="sm" />
                      <LuminarText 
                        text="Researching..."
                        className="text-sm text-gray-400"
                      />
                    </div>
                  </motion.div>
                )}
              </>
            )}
          </AnimatePresence>
          
          {/* Scroll anchor */}
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 border-t border-white/10"
        >
          <form onSubmit={handleSubmit} className="flex gap-2">
            <div className="flex-1">
              <LuminarInput
                value={currentMessage}
                onChange={(e) => onMessageChange(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                disabled={disabled || isTyping}
                size={size}
                variant="input"
                className="w-full"
              />
            </div>
            
            <LuminarButton
              type="submit"
              variant="glass"
              size={size}
              disabled={disabled || isTyping || !currentMessage.trim()}
              loading={isTyping}
              icon={Send}
              animation="scale"
              magnetic
            />
          </form>

          {/* Chat actions */}
          <div className="flex justify-between items-center mt-2">
            <LuminarText 
              text="Press Enter to send, Shift+Enter for new line"
              className="text-xs text-gray-500"
            />
            
            {messages.length > 0 && (
              <LuminarButton
                size="xs"
                variant="ghost"
                onClick={onClearChat}
                className="text-gray-500 hover:text-gray-300"
              >
                Clear chat
              </LuminarButton>
            )}
          </div>
        </motion.div>
      </div>
    );
  }
);
ResearchChat.displayName = "ResearchChat";

export { ResearchChat };