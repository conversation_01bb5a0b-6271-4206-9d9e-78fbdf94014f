import React from "react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes, useState } from "react"
import { Plus, X } from "lucide-react"
import { createGlassStyles } from '../../../lib/glass-utils'
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface FabAction {
  id: string
  label: string
  icon: React.ElementType
  onClick: () => void
  color?: string
}

export interface LuminarFabProps extends HTMLAttributes<HTMLDivElement> {
  icon?: React.ElementType
  actions?: FabAction[]
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  size?: ComponentSize | 'md' | 'lg' | 'xl'
  variant?: ComponentVariant | 'secondary' | 'success' | 'warning' | 'error' | 'glass' | 'default' | 'outline'
  extended?: boolean
  label?: string
  glass?: boolean
  animated?: boolean
  disabled?: boolean
  hideOnScroll?: boolean
  onClick?: () => void
}

const positionClasses = {
  'bottom-right': 'bottom-6 right-6',
  'bottom-left': 'bottom-6 left-6',
  'top-right': 'top-6 right-6',
  'top-left': 'top-6 left-6'
}

const sizeClasses = {
  xs: 'w-10 h-10',
  sm: 'w-12 h-12',
  md: 'w-14 h-14',
  lg: 'w-16 h-16',
  xl: 'w-18 h-18'
}

const extendedSizeClasses = {
  xs: 'h-10 px-3',
  sm: 'h-12 px-4',
  md: 'h-14 px-6',
  lg: 'h-16 px-8',
  xl: 'h-18 px-10'
}

const iconSizeClasses = {
  xs: 'w-4 h-4',
  sm: 'w-5 h-5',
  md: 'w-6 h-6',
  lg: 'w-7 h-7',
  xl: 'w-8 h-8'
}

const variantClasses = {
  primary: 'bg-blue-500 hover:bg-blue-600 text-white',
  secondary: 'bg-gray-500 hover:bg-gray-600 text-white',
  success: 'bg-green-500 hover:bg-green-600 text-white',
  warning: 'bg-yellow-500 hover:bg-yellow-600 text-white',
  error: 'bg-red-500 hover:bg-red-600 text-white',
  glass: 'bg-white/10 hover:bg-white/20 text-white backdrop-blur-md',
  default: 'bg-gray-100 hover:bg-gray-200 text-gray-900',
  outline: 'bg-transparent border-2 border-current hover:bg-current/10'
}

const LuminarFab = forwardRef<HTMLDivElement, LuminarFabProps>(
  ({ 
    icon: Icon = Plus,
    actions = [],
    position = 'bottom-right',
    size = defaultComponentProps.size,
    variant = 'primary',
    extended = false,
    label,
    glass = true,
    animated = true,
    disabled = false,
    hideOnScroll = false,
    onClick,
    className,
    ...props 
  }, ref) => {
    const [open, setIsOpen] = useState(false)
    const [isVisible, setIsVisible] = useState(true)
    const [lastScrollY, setLastScrollY] = useState(0)

    // Handle hide on scroll
    if (hideOnScroll && typeof window !== 'undefined') {
      window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY
        
        if (currentScrollY > lastScrollY && currentScrollY > 100) {
          setIsVisible(false)
        } else {
          setIsVisible(true)
        }
        
        setLastScrollY(currentScrollY)
      })
    }

    const handleMainClick = () => {
      if (actions.length > 0) {
        setIsOpen(!open)
      } else {
        onClick?.()
      }
    }

    const handleActionClick = (action: FabAction) => {
      action.onClick()
      setIsOpen(false)
    }

    const mainButtonClasses = cn(
      "relative rounded-full shadow-lg transition-all duration-300 flex items-center justify-center",
      extended ? extendedSizeClasses[size] : sizeClasses[size],
      variantClasses[variant],
      glass && createGlassStyles({ element: 'control' }),
      disabled && "opacity-50 cursor-not-allowed",
      !isVisible && hideOnScroll && "translate-y-24",
      className
    )

    return (
      <div
        ref={ref}
        className={cn(
          "fixed z-50",
          positionClasses[position]
        )}
        {...props}
      >
        {/* Action buttons */}
        <AnimatePresence>
          {open && actions.map((action, index) => (
            <motion.div
              key={action.id}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ 
                scale: 1, 
                opacity: 1,
                y: position.includes('bottom') ? -(index + 1) * 60 : (index + 1) * 60
              }}
              exit={{ scale: 0, opacity: 0 }}
              transition={{ 
                delay: index * 0.05,
                type: "spring",
                stiffness: 500,
                damping: 25
              }}
              className="absolute right-0"
              style={{
                bottom: position.includes('bottom') ? 0 : undefined,
                top: position.includes('top') ? 0 : undefined
              }}
            >
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handleActionClick(action)}
                className={cn(
                  "rounded-full shadow-lg flex items-center justify-center",
                  sizeClasses.sm,
                  action.color || "bg-gray-700 hover:bg-gray-600 text-white",
                  glass && "backdrop-blur-md bg-opacity-90"
                )}
                title={action.label}
              >
                {React.createElement(action.icon, { className: iconSizeClasses.sm })}
              </motion.button>
              
              {/* Label */}
              <motion.span
                initial={{ opacity: 0, x: position.includes('right') ? 10 : -10 }}
                animate={{ opacity: 1, x: 0 }}
                className={cn(
                  "absolute whitespace-nowrap px-2 py-1 rounded text-sm",
                  position.includes('right') ? 'right-full mr-3' : 'left-full ml-3',
                  "top-1/2 -translate-y-1/2",
                  "bg-gray-800 text-white"
                )}
              >
                {action.label}
              </motion.span>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Main button */}
        <motion.button
          className={mainButtonClasses}
          onClick={handleMainClick}
          disabled={disabled}
          whileHover={animated && !disabled ? { scale: 1.05 } : {}}
          whileTap={animated && !disabled ? { scale: 0.95 } : {}}
          animate={open && actions.length > 0 ? { rotate: 45 } : { rotate: 0 }}
          transition={{ duration: 0.2 }}
        >
          {actions.length > 0 && open ? (
            <X className={iconSizeClasses[size]} />
          ) : (
            <>
              {React.createElement(Icon, { 
                className: cn(
                  iconSizeClasses[size],
                  extended && label && "mr-2"
                )
              })}
              {extended && label && (
                <span className="font-medium">{label}</span>
              )}
            </>
          )}
        </motion.button>

        {/* Backdrop */}
        <AnimatePresence>
          {open && actions.length > 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 -z-10"
              onClick={() => setIsOpen(false)}
            />
          )}
        </AnimatePresence>
      </div>
    )
  }
)
LuminarFab.displayName = "LuminarFab"

// Mini FAB variant
export const LuminarMiniFab = forwardRef<HTMLDivElement, Omit<LuminarFabProps, 'extended' | 'label'>>(
  (props, ref) => {
    return <LuminarFab ref={ref} {...props} size="sm" extended={false} />
  }
)
LuminarMiniFab.displayName = "LuminarMiniFab"

// Extended FAB variant
export const LuminarExtendedFab = forwardRef<HTMLDivElement, LuminarFabProps>(
  ({ label = "Action", ...props }, ref) => {
    return <LuminarFab ref={ref} {...props} extended label={label} />
  }
)
LuminarExtendedFab.displayName = "LuminarExtendedFab"

export { LuminarFab }