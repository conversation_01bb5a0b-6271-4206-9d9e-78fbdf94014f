import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface ScatterPlotData {
  x: number;
  y: number;
  size?: number;
  label?: string;
  color?: string;
  category?: string;
}

export interface AnimatedScatterPlotProps {
  data: ScatterPlotData[];
  width?: number;
  height?: number;
  xLabel?: string;
  yLabel?: string;
  showGrid?: boolean;
  showTrendLine?: boolean;
  pointStyle?: 'circle' | 'square' | 'diamond' | 'star';
  animate?: boolean;
  animationDuration?: number;
  className?: string;
}

export function LuminarAnimatedScatterPlot({
  data,
  width = 600,
  height = 400,
  xLabel = 'X Axis',
  yLabel = 'Y Axis',
  showGrid = true,
  showTrendLine = false,
  pointStyle = 'circle',
  animate = true,
  animationDuration = 1.5,
  className = ''
}: AnimatedScatterPlotProps) {
  const [hoveredPoint, setHoveredPoint] = useState<number | null>(null);
  const glassClasses = getAdaptiveGlassClasses('card', 'md', 'neutral');

  // Chart dimensions
  const padding = { top: 20, right: 20, bottom: 60, left: 60 };
  const chartWidth = width - padding.left - padding.right;
  const chartHeight = height - padding.top - padding.bottom;

  // Calculate scales
  const xValues = data.map(d => d.x);
  const yValues = data.map(d => d.y);
  const xMin = Math.min(...xValues);
  const xMax = Math.max(...xValues);
  const yMin = Math.min(...yValues);
  const yMax = Math.max(...yValues);

  const xScale = (value: number) => ((value - xMin) / (xMax - xMin)) * chartWidth;
  const yScale = (value: number) => chartHeight - ((value - yMin) / (yMax - yMin)) * chartHeight;

  // Default colors by category
  const categoryColors: { [key: string]: string } = {};
  const defaultColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];
  let colorIndex = 0;

  data.forEach(point => {
    if (point.category && !categoryColors[point.category]) {
      categoryColors[point.category] = defaultColors[colorIndex % defaultColors.length];
      colorIndex++;
    }
  });

  // Calculate trend line if needed
  const calculateTrendLine = () => {
    const n = data.length;
    const sumX = xValues.reduce((a, b) => a + b, 0);
    const sumY = yValues.reduce((a, b) => a + b, 0);
    const sumXY = data.reduce((sum, point) => sum + point.x * point.y, 0);
    const sumX2 = xValues.reduce((sum, x) => sum + x * x, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    const x1 = xMin;
    const y1 = slope * x1 + intercept;
    const x2 = xMax;
    const y2 = slope * x2 + intercept;

    return { x1, y1, x2, y2 };
  };

  const trendLine = showTrendLine ? calculateTrendLine() : null;

  // Get point shape
  const getPointShape = (x: number, y: number, size: number) => {
    switch (pointStyle) {
      case 'square':
        return `M ${x - size} ${y - size} L ${x + size} ${y - size} L ${x + size} ${y + size} L ${x - size} ${y + size} Z`;
      case 'diamond':
        return `M ${x} ${y - size} L ${x + size} ${y} L ${x} ${y + size} L ${x - size} ${y} Z`;
      case 'star':
        const outerRadius = size;
        const innerRadius = size * 0.5;
        let path = '';
        for (let i = 0; i < 10; i++) {
          const radius = i % 2 === 0 ? outerRadius : innerRadius;
          const angle = (i * Math.PI) / 5 - Math.PI / 2;
          const px = x + radius * Math.cos(angle);
          const py = y + radius * Math.sin(angle);
          path += i === 0 ? `M ${px} ${py}` : ` L ${px} ${py}`;
        }
        return path + ' Z';
      default:
        return null; // Use circle
    }
  };

  return (
    <div className={`${glassClasses} p-4 rounded-lg border ${className}`}>
      <svg width={width} height={height}>
        <defs>
          <filter id="pointGlow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        <g transform={`translate(${padding.left},${padding.top})`}>
          {/* Grid */}
          {showGrid && (
            <g className="grid">
              {/* Vertical grid lines */}
              {[0, 0.25, 0.5, 0.75, 1].map((tick, i) => {
                const x = tick * chartWidth;
                const value = xMin + tick * (xMax - xMin);
                return (
                  <g key={`v-${i}`}>
                    <motion.line
                      x1={x}
                      y1={0}
                      x2={x}
                      y2={chartHeight}
                      stroke="var(--border)"
                      strokeWidth="1"
                      opacity="0.3"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 0.3 }}
                      transition={{ delay: i * 0.05 }}
                    />
                    <motion.text
                      x={x}
                      y={chartHeight + 20}
                      textAnchor="middle"
                      fontSize="10"
                      fill="currentColor"
                      className="text-muted-foreground"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: i * 0.05 }}
                    >
                      {value.toFixed(1)}
                    </motion.text>
                  </g>
                );
              })}

              {/* Horizontal grid lines */}
              {[0, 0.25, 0.5, 0.75, 1].map((tick, i) => {
                const y = chartHeight - tick * chartHeight;
                const value = yMin + tick * (yMax - yMin);
                return (
                  <g key={`h-${i}`}>
                    <motion.line
                      x1={0}
                      y1={y}
                      x2={chartWidth}
                      y2={y}
                      stroke="var(--border)"
                      strokeWidth="1"
                      opacity="0.3"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 0.3 }}
                      transition={{ delay: i * 0.05 }}
                    />
                    <motion.text
                      x={-10}
                      y={y + 4}
                      textAnchor="end"
                      fontSize="10"
                      fill="currentColor"
                      className="text-muted-foreground"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: i * 0.05 }}
                    >
                      {value.toFixed(1)}
                    </motion.text>
                  </g>
                );
              })}
            </g>
          )}

          {/* Trend line */}
          {showTrendLine && trendLine && (
            <motion.line
              x1={xScale(trendLine.x1)}
              y1={yScale(trendLine.y1)}
              x2={xScale(trendLine.x2)}
              y2={yScale(trendLine.y2)}
              stroke="var(--muted-foreground)"
              strokeWidth="2"
              strokeDasharray="5,5"
              opacity="0.5"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: animationDuration, ease: "easeInOut" }}
            />
          )}

          {/* Data points */}
          {data.map((point, index) => {
            const x = xScale(point.x);
            const y = yScale(point.y);
            const size = point.size || 6;
            const color = point.color || (point.category ? categoryColors[point.category] : defaultColors[0]);
            const hovered = hoveredPoint === index;
            const pointPath = getPointShape(x, y, size);

            return (
              <g key={index}>
                {pointStyle === 'circle' ? (
                  <motion.circle
                    cx={x}
                    cy={y}
                    r={hovered ? size * 1.5 : size}
                    fill={color}
                    stroke="var(--background)"
                    strokeWidth="2"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{
                      delay: (index / data.length) * animationDuration,
                      type: "spring",
                      stiffness: 300,
                      damping: 10
                    }}
                    onMouseEnter={() => setHoveredPoint(index)}
                    onMouseLeave={() => setHoveredPoint(null)}
                    className="cursor-pointer"
                    filter={hovered ? "url(#pointGlow)" : ""}
                  />
                ) : (
                  <motion.path
                    d={pointPath!}
                    fill={color}
                    stroke="var(--background)"
                    strokeWidth="2"
                    initial={{ scale: 0 }}
                    animate={{ scale: hovered ? 1.5 : 1 }}
                    transition={{
                      delay: (index / data.length) * animationDuration,
                      type: "spring",
                      stiffness: 300,
                      damping: 10
                    }}
                    style={{ transformOrigin: `${x}px ${y}px` }}
                    onMouseEnter={() => setHoveredPoint(index)}
                    onMouseLeave={() => setHoveredPoint(null)}
                    className="cursor-pointer"
                    filter={hovered ? "url(#pointGlow)" : ""}
                  />
                )}
              </g>
            );
          })}

          {/* Axis labels */}
          <motion.text
            x={chartWidth / 2}
            y={chartHeight + 45}
            textAnchor="middle"
            fontSize="12"
            fill="currentColor"
            className="text-foreground font-medium"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: animationDuration }}
          >
            {xLabel}
          </motion.text>

          <motion.text
            x={-chartHeight / 2}
            y={-40}
            textAnchor="middle"
            fontSize="12"
            fill="currentColor"
            className="text-foreground font-medium"
            transform="rotate(-90)"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: animationDuration }}
          >
            {yLabel}
          </motion.text>
        </g>
      </svg>

      {/* Hover tooltip */}
      <AnimatePresence>
        {hoveredPoint !== null && (
          <motion.div
            className="absolute bg-popover text-popover-foreground p-2 rounded-md shadow-lg border text-sm"
            style={{
              left: xScale(data[hoveredPoint].x) + padding.left,
              top: yScale(data[hoveredPoint].y) + padding.top - 40
            }}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.2 }}
          >
            {data[hoveredPoint].label && (
              <div className="font-medium">{data[hoveredPoint].label}</div>
            )}
            <div>X: {data[hoveredPoint].x.toFixed(2)}</div>
            <div>Y: {data[hoveredPoint].y.toFixed(2)}</div>
            {data[hoveredPoint].category && (
              <div className="text-muted-foreground">{data[hoveredPoint].category}</div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Alias for consistency with other chart components
export const LuminarScatterPlot = LuminarAnimatedScatterPlot;