/**
 * Route guards for authentication and authorization
 */

import { RouteGuardConfig, RouteContext } from './types'

/**
 * Authentication guard
 */
export function createAuthGuard(config: RouteGuardConfig) {
  return (context: RouteContext) => {
    const { user } = context
    
    if (config.requireAuth && !user) {
      // Redirect to login or show fallback
      if (config.redirectTo) {
        return { redirect: config.redirectTo }
      }
      
      if (config.fallback) {
        return { fallback: config.fallback }
      }
      
      return { redirect: '/login' }
    }
    
    return { allowed: true }
  }
}

/**
 * Role-based guard
 */
export function createRoleGuard(config: RouteGuardConfig) {
  return (context: RouteContext) => {
    const { user, roles = [] } = context
    
    if (!user) {
      return { redirect: '/login' }
    }
    
    if (config.requireRole) {
      const requiredRoles = Array.isArray(config.requireRole) 
        ? config.requireRole 
        : [config.requireRole]
      
      const hasRole = requiredRoles.some(role => roles.includes(role))
      
      if (!hasRole) {
        return { redirect: config.redirectTo || '/unauthorized' }
      }
    }
    
    return { allowed: true }
  }
}

/**
 * Permission-based guard
 */
export function createPermissionGuard(config: RouteGuardConfig) {
  return (context: RouteContext) => {
    const { user, permissions = [] } = context
    
    if (!user) {
      return { redirect: '/login' }
    }
    
    if (config.requirePermission) {
      const requiredPermissions = Array.isArray(config.requirePermission)
        ? config.requirePermission
        : [config.requirePermission]
      
      const hasPermission = requiredPermissions.some(permission => 
        permissions.includes(permission)
      )
      
      if (!hasPermission) {
        return { redirect: config.redirectTo || '/unauthorized' }
      }
    }
    
    return { allowed: true }
  }
}

/**
 * Combined guard that checks auth, roles, and permissions
 */
export function createCombinedGuard(config: RouteGuardConfig) {
  return (context: RouteContext) => {
    const { user, roles = [], permissions = [] } = context
    
    // Check authentication
    if (config.requireAuth && !user) {
      return { redirect: config.redirectTo || '/login' }
    }
    
    // Check roles
    if (config.requireRole && user) {
      const requiredRoles = Array.isArray(config.requireRole)
        ? config.requireRole
        : [config.requireRole]
      
      const hasRole = requiredRoles.some(role => roles.includes(role))
      
      if (!hasRole) {
        return { redirect: config.redirectTo || '/unauthorized' }
      }
    }
    
    // Check permissions
    if (config.requirePermission && user) {
      const requiredPermissions = Array.isArray(config.requirePermission)
        ? config.requirePermission
        : [config.requirePermission]
      
      const hasPermission = requiredPermissions.some(permission =>
        permissions.includes(permission)
      )
      
      if (!hasPermission) {
        return { redirect: config.redirectTo || '/unauthorized' }
      }
    }
    
    return { allowed: true }
  }
}

/**
 * Guest guard (for routes that should only be accessible to non-authenticated users)
 */
export function createGuestGuard(config: RouteGuardConfig) {
  return (context: RouteContext) => {
    const { user } = context
    
    if (user) {
      return { redirect: config.redirectTo || '/' }
    }
    
    return { allowed: true }
  }
}

/**
 * Admin guard
 */
export function createAdminGuard(config: RouteGuardConfig) {
  return (context: RouteContext) => {
    const { user, roles = [] } = context
    
    if (!user) {
      return { redirect: '/login' }
    }
    
    if (!roles.includes('admin') && !roles.includes('super_admin')) {
      return { redirect: config.redirectTo || '/unauthorized' }
    }
    
    return { allowed: true }
  }
}

/**
 * Development guard (only allows access in development mode)
 */
export function createDevelopmentGuard(config: RouteGuardConfig) {
  return (context: RouteContext) => {
    const isDevelopment = import.meta.env.DEV
    
    if (!isDevelopment) {
      return { redirect: config.redirectTo || '/' }
    }
    
    return { allowed: true }
  }
}

/**
 * Feature flag guard
 */
export function createFeatureFlagGuard(featureFlag: string, config: RouteGuardConfig) {
  return (context: RouteContext) => {
    const { user } = context
    
    // Check if feature is enabled
    // This would typically check against a feature flag service
    const isFeatureEnabled = checkFeatureFlag(featureFlag, user)
    
    if (!isFeatureEnabled) {
      return { redirect: config.redirectTo || '/' }
    }
    
    return { allowed: true }
  }
}

/**
 * Time-based guard (allows access only during certain hours)
 */
export function createTimeBasedGuard(
  startHour: number,
  endHour: number,
  config: RouteGuardConfig
) {
  return (context: RouteContext) => {
    const currentHour = new Date().getHours()
    
    if (currentHour < startHour || currentHour > endHour) {
      return { redirect: config.redirectTo || '/' }
    }
    
    return { allowed: true }
  }
}

/**
 * IP-based guard
 */
export function createIPGuard(allowedIPs: string[], config: RouteGuardConfig) {
  return (context: RouteContext) => {
    const userIP = getUserIP()
    
    if (!allowedIPs.includes(userIP)) {
      return { redirect: config.redirectTo || '/unauthorized' }
    }
    
    return { allowed: true }
  }
}

/**
 * Device-based guard
 */
export function createDeviceGuard(allowedDevices: string[], config: RouteGuardConfig) {
  return (context: RouteContext) => {
    const deviceType = getDeviceType()
    
    if (!allowedDevices.includes(deviceType)) {
      return { redirect: config.redirectTo || '/' }
    }
    
    return { allowed: true }
  }
}

/**
 * Subscription guard
 */
export function createSubscriptionGuard(
  requiredPlan: string,
  config: RouteGuardConfig
) {
  return (context: RouteContext) => {
    const { user } = context
    
    if (!user) {
      return { redirect: '/login' }
    }
    
    const userPlan = user.subscription?.plan
    const planHierarchy = ['free', 'basic', 'premium', 'enterprise']
    
    const userPlanIndex = planHierarchy.indexOf(userPlan)
    const requiredPlanIndex = planHierarchy.indexOf(requiredPlan)
    
    if (userPlanIndex < requiredPlanIndex) {
      return { redirect: config.redirectTo || '/upgrade' }
    }
    
    return { allowed: true }
  }
}

/**
 * Compose multiple guards
 */
export function composeGuards(...guards: Array<(context: RouteContext) => any>) {
  return (context: RouteContext) => {
    for (const guard of guards) {
      const result = guard(context)
      if (!result.allowed) {
        return result
      }
    }
    
    return { allowed: true }
  }
}

/**
 * Create a guard middleware for TanStack Router
 */
export function createRouteGuardMiddleware(
  guardConfig: RouteGuardConfig,
  context: RouteContext
) {
  return (opts: any) => {
    const guard = createCombinedGuard(guardConfig)
    const result = guard(context)
    
    if (!result.allowed) {
      if (result.redirect) {
        throw new Error(`Redirect to ${result.redirect}`)
      }
      
      // No fallback handling - guards only support redirect or allowed
    }
    
    return opts
  }
}

/**
 * Helper functions
 */

function checkFeatureFlag(featureFlag: string, user: any): boolean {
  // This would typically integrate with a feature flag service
  // For now, we'll return true for demonstration
  return true
}

function getUserIP(): string {
  // This would typically be provided by the server or a service
  return '127.0.0.1'
}

function getDeviceType(): string {
  const userAgent = navigator.userAgent
  
  if (/Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(userAgent)) {
    return 'mobile'
  }
  
  if (/iPad|Android(?!.*Mobile)/.test(userAgent)) {
    return 'tablet'
  }
  
  return 'desktop'
}

/**
 * Route guard component wrapper
 */
export function withRouteGuard<T extends object>(
  Component: React.ComponentType<T>,
  guardConfig: RouteGuardConfig
) {
  return (props: T & { context: RouteContext }) => {
    const { context, ...componentProps } = props
    const guard = createCombinedGuard(guardConfig)
    const result = guard(context)
    
    if (!result.allowed) {
      // No fallback handling - guards only support redirect or allowed
      
      // This would typically trigger a redirect
      return null
    }
    
    return <Component {...(componentProps as T)} />
  }
}

/**
 * Hook for using route guards
 */
export function useRouteGuard(guardConfig: RouteGuardConfig, context: RouteContext) {
  const guard = createCombinedGuard(guardConfig)
  const result = guard(context)
  
  return {
    allowed: result.allowed,
    redirect: result.redirect
  }
}