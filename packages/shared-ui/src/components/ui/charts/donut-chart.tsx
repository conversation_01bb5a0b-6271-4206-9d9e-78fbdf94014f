import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface DonutChartData {
  label: string;
  value: number;
  color?: string;
}

export interface AnimatedDonutChartProps {
  data: DonutChartData[];
  width?: number;
  height?: number;
  innerRadius?: number;
  outerRadius?: number;
  showLabels?: boolean;
  showValues?: boolean;
  showPercentages?: boolean;
  centerContent?: React.ReactNode;
  animate?: boolean;
  animationDuration?: number;
  className?: string;
}

export function LuminarAnimatedDonutChart({
  data,
  width = 300,
  height = 300,
  innerRadius = 60,
  outerRadius = 100,
  showLabels = true,
  showValues = false,
  showPercentages = true,
  centerContent,
  animate = true,
  animationDuration = 1.5,
  className = ''
}: AnimatedDonutChartProps) {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const glassClasses = getAdaptiveGlassClasses('card', 'md', 'neutral');

  // Default colors
  const defaultColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#ec4899', '#14b8a6'];

  // Calculate total
  const total = data.reduce((sum, item) => sum + item.value, 0);

  // Calculate angles
  let currentAngle = -90; // Start from top
  const segments = data.map((item, index) => {
    const percentage = (item.value / total) * 100;
    const angle = (item.value / total) * 360;
    const startAngle = currentAngle;
    const endAngle = currentAngle + angle;
    currentAngle += angle;

    return {
      ...item,
      percentage,
      startAngle,
      endAngle,
      angle,
      color: item.color || defaultColors[index % defaultColors.length]
    };
  });

  // Convert polar to cartesian
  const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
    const angleInRadians = (angleInDegrees * Math.PI) / 180.0;
    return {
      x: centerX + radius * Math.cos(angleInRadians),
      y: centerY + radius * Math.sin(angleInRadians)
    };
  };

  // Create arc path
  const createArcPath = (centerX: number, centerY: number, innerR: number, outerR: number, startAngle: number, endAngle: number) => {
    const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;
    
    const innerStart = polarToCartesian(centerX, centerY, innerR, startAngle);
    const innerEnd = polarToCartesian(centerX, centerY, innerR, endAngle);
    const outerStart = polarToCartesian(centerX, centerY, outerR, startAngle);
    const outerEnd = polarToCartesian(centerX, centerY, outerR, endAngle);

    const path = [
      `M ${innerStart.x} ${innerStart.y}`,
      `L ${outerStart.x} ${outerStart.y}`,
      `A ${outerR} ${outerR} 0 ${largeArcFlag} 1 ${outerEnd.x} ${outerEnd.y}`,
      `L ${innerEnd.x} ${innerEnd.y}`,
      `A ${innerR} ${innerR} 0 ${largeArcFlag} 0 ${innerStart.x} ${innerStart.y}`,
      'Z'
    ].join(' ');

    return path;
  };

  const centerX = width / 2;
  const centerY = height / 2;

  return (
    <div className={`${glassClasses} p-4 rounded-lg border ${className}`}>
      <div className="relative">
        <svg width={width} height={height}>
          <defs>
            {segments.map((segment, index) => (
              <linearGradient key={`gradient-${index}`} id={`donutGradient-${index}`}>
                <stop offset="0%" stopColor={segment.color} stopOpacity="0.9" />
                <stop offset="100%" stopColor={segment.color} stopOpacity="0.7" />
              </linearGradient>
            ))}
            <filter id="glow">
              <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>

          {/* Segments */}
          {segments.map((segment, index) => {
            const hovered = hoveredIndex === index;
            const radius = hovered ? outerRadius + 5 : outerRadius;
            const path = createArcPath(centerX, centerY, innerRadius, radius, segment.startAngle, segment.endAngle);

            return (
              <motion.g key={index}>
                <motion.path
                  d={path}
                  fill={`url(#donutGradient-${index})`}
                  stroke="var(--background)"
                  strokeWidth="2"
                  initial={{ 
                    scale: 0,
                    opacity: 0
                  }}
                  animate={{ 
                    scale: 1,
                    opacity: 1
                  }}
                  transition={{
                    duration: animationDuration,
                    delay: index * 0.1,
                    ease: [0.16, 1, 0.3, 1]
                  }}
                  style={{ transformOrigin: `${centerX}px ${centerY}px` }}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                  className="cursor-pointer"
                  filter={hovered ? "url(#glow)" : ""}
                />

                {/* Segment label */}
                {showLabels && segment.angle > 15 && (
                  <motion.text
                    x={polarToCartesian(centerX, centerY, (innerRadius + radius) / 2, segment.startAngle + segment.angle / 2).x}
                    y={polarToCartesian(centerX, centerY, (innerRadius + radius) / 2, segment.startAngle + segment.angle / 2).y}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    fontSize="12"
                    fill="white"
                    className="font-medium pointer-events-none"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{
                      duration: 0.3,
                      delay: index * 0.1 + animationDuration
                    }}
                  >
                    {showPercentages ? `${segment.percentage.toFixed(1)}%` : segment.value}
                  </motion.text>
                )}
              </motion.g>
            );
          })}

          {/* Center content */}
          {centerContent && (
            <motion.g
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: animationDuration * 0.8, duration: 0.5 }}
            >
              <foreignObject x={centerX - innerRadius + 10} y={centerY - innerRadius + 10} width={innerRadius * 2 - 20} height={innerRadius * 2 - 20}>
                <div className="flex items-center justify-center h-full">
                  {centerContent}
                </div>
              </foreignObject>
            </motion.g>
          )}
        </svg>

        {/* Hover tooltip */}
        <AnimatePresence>
          {hoveredIndex !== null && (
            <motion.div
              className="absolute top-4 right-4 bg-popover text-popover-foreground p-3 rounded-md shadow-lg border"
              initial={{ opacity: 0, scale: 0.8, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <div className="font-medium">{segments[hoveredIndex].label}</div>
              <div className="text-sm text-muted-foreground">
                Value: {segments[hoveredIndex].value}
              </div>
              <div className="text-sm text-muted-foreground">
                {segments[hoveredIndex].percentage.toFixed(1)}% of total
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Legend */}
        <div className="mt-4 grid grid-cols-2 gap-2">
          {segments.map((segment, index) => (
            <motion.div
              key={index}
              className="flex items-center gap-2"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 + animationDuration }}
            >
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: segment.color }}
              />
              <span className="text-sm text-muted-foreground">{segment.label}</span>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Alias for backward compatibility
export const LuminarDonutChart = LuminarAnimatedDonutChart;