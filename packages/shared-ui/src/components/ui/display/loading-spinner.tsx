import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes } from "react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface LoadingSpinnerProps extends HTMLAttributes<HTMLDivElement> {
  variant?: ComponentVariant | "dots" | "pulse" | "orbit" | "bars"
  size?: ComponentSize | "md" | "lg" | "xl"
  color?: string
}

const LoadingSpinner = forwardRef<HTMLDivElement, LoadingSpinnerProps>(
  ({ variant = defaultComponentProps.variant, size = defaultComponentProps.size, color, className, ...props }, ref) => {
    const sizeMap = {
      xs: 16,
      sm: 20,
      md: 40,
      lg: 60,
      xl: 80
    }

    const spinnerSize = sizeMap[size as keyof typeof sizeMap] || sizeMap.md

    const defaultVariants = {
      default: (
        <motion.svg
          width={spinnerSize}
          height={spinnerSize}
          viewBox="0 0 50 50"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <circle
            cx="25"
            cy="25"
            r="20"
            stroke={color || "currentColor"}
            strokeWidth="5"
            fill="none"
            strokeLinecap="round"
            strokeDasharray="80 20"
          />
        </motion.svg>
      ),
      
      dots: (
        <div className="flex gap-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className={cn(
                "rounded-full",
                size === "sm" && "w-2 h-2",
                size === "md" && "w-3 h-3",
                size === "lg" && "w-4 h-4",
                size === "xl" && "w-5 h-5"
              )}
              style={{ backgroundColor: color || "currentColor" }}
              animate={{
                y: [0, -10, 0],
                opacity: [0.3, 1, 0.3]
              }}
              transition={{
                duration: 0.6,
                repeat: Infinity,
                delay: i * 0.15
              }}
            />
          ))}
        </div>
      ),
      
      pulse: (
        <motion.div
          className={cn(
            "rounded-full border-4",
            size === "sm" && "w-5 h-5",
            size === "md" && "w-10 h-10",
            size === "lg" && "w-15 h-15",
            size === "xl" && "w-20 h-20"
          )}
          style={{ borderColor: color || "currentColor" }}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [1, 0.5, 1]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      ),
      
      orbit: (
        <div className="relative" style={{ width: spinnerSize, height: spinnerSize }}>
          <motion.div
            className="absolute inset-0"
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            {[0, 120, 240].map((rotation) => (
              <motion.div
                key={rotation}
                className={cn(
                  "absolute rounded-full",
                  size === "sm" && "w-2 h-2",
                  size === "md" && "w-3 h-3",
                  size === "lg" && "w-4 h-4",
                  size === "xl" && "w-5 h-5"
                )}
                style={{
                  backgroundColor: color || "currentColor",
                  top: "10%",
                  left: "50%",
                  transform: `translateX(-50%) rotate(${rotation}deg) translateY(-${spinnerSize / 2.5}px)`
                }}
                animate={{
                  scale: [1, 0.5, 1],
                  opacity: [1, 0.3, 1]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: rotation / 360
                }}
              />
            ))}
          </motion.div>
        </div>
      ),
      
      bars: (
        <div className="flex gap-1">
          {[0, 1, 2, 3, 4].map((i) => (
            <motion.div
              key={i}
              className={cn(
                "rounded-sm",
                size === "sm" && "w-1 h-4",
                size === "md" && "w-1.5 h-8",
                size === "lg" && "w-2 h-12",
                size === "xl" && "w-3 h-16"
              )}
              style={{ backgroundColor: color || "currentColor" }}
              animate={{
                scaleY: [0.5, 1, 0.5],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 0.8,
                repeat: Infinity,
                delay: i * 0.1
              }}
            />
          ))}
        </div>
      )
    }

    return (
      <div
        ref={ref}
        className={cn("inline-flex items-center justify-center", className)}
        {...props}
      >
        {defaultVariants[variant as keyof typeof defaultVariants] || defaultVariants.default}
      </div>
    )
  }
)
LoadingSpinner.displayName = "LoadingSpinner"

export { LoadingSpinner }