import { ReactNode, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  ShoppingCart,
  Heart,
  User,
  Search,
  Menu,
  X,
  Star,
  Filter,
  Grid,
  List,
  MapPin,
  Phone,
  Mail,
  Facebook,
  Twitter,
  Instagram,
  ChevronDown,
  Truck,
  Shield,
  RotateCcw,
  CreditCard
} from "lucide-react";

import { LuminarCard } from '../../ui/display';
import { Button } from '../../ui/actions';
import { LuminarInput } from '../../ui/forms';
import { LuminarBadge, LuminarAvatar } from '../../ui/display';
import { ThemeToggle } from '../../ui/utilities';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface EcommerceLayoutProps {
  className?: string;
  children: ReactNode;
  storeName?: string;
  logo?: string;
  categories?: Category[];
  cartItemCount?: number;
  wishlistCount?: number;
  user?: EcommerceUser;
  showSearch?: boolean;
  showMobileMenu?: boolean;
  onMobileMenuToggle?: (open: boolean) => void;
  announcements?: Announcement[];
  footerLinks?: FooterSection[];
  trustSignals?: TrustSignal[];
  onCategorySelect?: (categoryId: string) => void;
  onSearch?: (query: string) => void;
  onCartClick?: () => void;
}

export interface Category {
  id: string;
  name: string;
  href?: string;
  children?: Category[];
  featured?: boolean;
  image?: string;
}

export interface EcommerceUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  isGuest?: boolean;
}

export interface Announcement {
  id: string;
  message: string;
  type: 'sale' | 'shipping' | 'info';
  link?: string;
  dismissible?: boolean;
}

export interface FooterSection {
  title: string;
  links: FooterLink[];
}

export interface FooterLink {
  label: string;
  href: string;
  external?: boolean;
}

export interface TrustSignal {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
}

const defaultCategories: Category[] = [
  { 
    id: 'electronics', 
    name: 'Electronics',
    children: [
      { id: 'phones', name: 'Smartphones' },
      { id: 'laptops', name: 'Laptops' },
      { id: 'headphones', name: 'Headphones' }
    ]
  },
  { 
    id: 'clothing', 
    name: 'Clothing',
    children: [
      { id: 'mens', name: "Men's" },
      { id: 'womens', name: "Women's" },
      { id: 'kids', name: 'Kids' }
    ]
  },
  { id: 'home', name: 'Home & Garden' },
  { id: 'sports', name: 'Sports & Outdoors' },
  { id: 'books', name: 'Books' },
];

const defaultTrustSignals: TrustSignal[] = [
  {
    icon: Truck,
    title: 'Free Shipping',
    description: 'On orders over $50'
  },
  {
    icon: RotateCcw,
    title: '30-Day Returns',
    description: 'Hassle-free returns'
  },
  {
    icon: Shield,
    title: 'Secure Payment',
    description: '100% secure checkout'
  },
  {
    icon: CreditCard,
    title: 'Multiple Payment',
    description: 'Accept all major cards'
  }
];

const defaultFooterLinks: FooterSection[] = [
  {
    title: 'Customer Service',
    links: [
      { label: 'Contact Us', href: '/contact' },
      { label: 'Shipping Info', href: '/shipping' },
      { label: 'Returns', href: '/returns' },
      { label: 'Size Guide', href: '/size-guide' }
    ]
  },
  {
    title: 'Company',
    links: [
      { label: 'About Us', href: '/about' },
      { label: 'Careers', href: '/careers' },
      { label: 'Press', href: '/press' },
      { label: 'Sustainability', href: '/sustainability' }
    ]
  },
  {
    title: 'Legal',
    links: [
      { label: 'Privacy Policy', href: '/privacy' },
      { label: 'Terms of Service', href: '/terms' },
      { label: 'Cookie Policy', href: '/cookies' }
    ]
  }
];

export function EcommerceLayout({
  className,
  children,
  storeName = "Your Store",
  logo,
  categories = defaultCategories,
  cartItemCount = 0,
  wishlistCount = 0,
  user,
  showSearch = true,
  showMobileMenu: controlledMobileMenu,
  onMobileMenuToggle,
  announcements = [],
  footerLinks = defaultFooterLinks,
  trustSignals = defaultTrustSignals,
  onCategorySelect,
  onSearch,
  ...props
}: EcommerceLayoutProps) {
  const [internalMobileMenu, setInternalMobileMenu] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showCategoryDropdown, setShowCategoryDropdown] = useState<string | null>(null);

  const mobileMenuOpen = controlledMobileMenu ?? internalMobileMenu;
  const setMobileMenuOpen = onMobileMenuToggle ?? setInternalMobileMenu;

  const handleSearch = () => {
    if (searchQuery.trim()) {
      onSearch?.(searchQuery);
    }
  };

  return (
    <div className={cn(
      "min-h-screen bg-white dark:bg-gray-900",
      className
    )} {...props}>
      
      {/* Announcements Bar */}
      {announcements.length > 0 && (
        <div className="bg-blue-600 text-white text-center py-2 px-4">
          <p className="text-sm">
            {announcements[0].message}
            {announcements[0].link && (
              <a href={announcements[0].link} className="underline ml-2 hover:no-underline">
                Shop Now
              </a>
            )}
          </p>
        </div>
      )}

      {/* Top Header */}
      <header className="border-b border-gray-200 dark:border-gray-700">
        {/* Top Bar */}
        <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2">
          <div className="max-w-7xl mx-auto flex items-center justify-between text-sm">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <Phone className="w-3 h-3" />
                +****************
              </span>
              <span className="flex items-center gap-1">
                <Mail className="w-3 h-3" />
                <EMAIL>
              </span>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Facebook className="w-4 h-4 cursor-pointer hover:text-blue-600" />
                <Twitter className="w-4 h-4 cursor-pointer hover:text-blue-400" />
                <Instagram className="w-4 h-4 cursor-pointer hover:text-pink-600" />
              </div>
              <ThemeToggle />
            </div>
          </div>
        </div>

        {/* Main Header */}
        <div className="px-4 py-4">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden"
                onClick={() => setMobileMenuOpen(true)}
              >
                <Menu className="w-5 h-5" />
              </Button>
              
              <a href="/" className="flex items-center gap-2">
                {logo ? (
                  <img src={logo} alt={storeName} className="h-8 w-auto" />
                ) : (
                  <div className="w-8 h-8 bg-blue-600 rounded text-white flex items-center justify-center font-bold">
                    {storeName.charAt(0)}
                  </div>
                )}
                <span className="text-xl font-bold text-gray-900 dark:text-white">
                  {storeName}
                </span>
              </a>
            </div>

            {/* Search */}
            {showSearch && (
              <div className="flex-1 max-w-lg mx-8 hidden md:block">
                <div className="flex">
                  <LuminarInput
                    placeholder="Search products..."
                    value={searchQuery}
                    onChange={(value) => setSearchQuery(value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="rounded-r-none"
                  />
                  <Button
                    onClick={handleSearch}
                    className="rounded-l-none border-l-0"
                  >
                    <Search className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* User Actions */}
            <div className="flex items-center gap-4">
              {user ? (
                <div className="flex items-center gap-2">
                  <LuminarAvatar
                    src={user.avatar}
                    alt={user.name}
                    size="sm"
                    fallback={user.name.charAt(0)}
                  />
                  <span className="hidden md:block text-sm">
                    Hi, {user.name.split(' ')[0]}
                  </span>
                </div>
              ) : (
                <Button variant="ghost" size="sm">
                  <User className="w-4 h-4 mr-2" />
                  Sign In
                </Button>
              )}

              <Button variant="ghost" size="sm" className="relative">
                <Heart className="w-5 h-5" />
                {wishlistCount > 0 && (
                  <LuminarBadge
                    variant="error"
                    size="sm"
                    className="absolute -top-1 -right-1 min-w-[1.25rem] h-5"
                  >
                    {wishlistCount}
                  </LuminarBadge>
                )}
              </Button>

              <Button variant="ghost" size="sm" className="relative">
                <ShoppingCart className="w-5 h-5" />
                {cartItemCount > 0 && (
                  <LuminarBadge
                    variant="error"
                    size="sm"
                    className="absolute -top-1 -right-1 min-w-[1.25rem] h-5"
                  >
                    {cartItemCount}
                  </LuminarBadge>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="hidden lg:block border-t border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex items-center space-x-8">
              {categories.map((category) => (
                <div
                  key={category.id}
                  className="relative"
                  onMouseEnter={() => setShowCategoryDropdown(category.id)}
                  onMouseLeave={() => setShowCategoryDropdown(null)}
                >
                  <button
                    onClick={() => onCategorySelect?.(category.id)}
                    className="flex items-center gap-1 py-4 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  >
                    {category.name}
                    {category.children && category.children.length > 0 && (
                      <ChevronDown className="w-3 h-3" />
                    )}
                  </button>

                  {/* Dropdown */}
                  {category.children && showCategoryDropdown === category.id && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="absolute top-full left-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 min-w-48 mt-1"
                    >
                      <div className="py-2">
                        {category.children.map((child) => (
                          <button
                            key={child.id}
                            onClick={() => onCategorySelect?.(child.id)}
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                          >
                            {child.name}
                          </button>
                        ))}
                      </div>
                    </motion.div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </nav>
      </header>

      {/* Mobile Menu */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 z-40 lg:hidden"
              onClick={() => setMobileMenuOpen(false)}
            />
            
            <motion.div
              initial={{ x: -300 }}
              animate={{ x: 0 }}
              exit={{ x: -300 }}
              className="fixed inset-y-0 left-0 w-80 bg-white dark:bg-gray-800 z-50 overflow-y-auto"
            >
              <div className="p-4">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-semibold">Menu</h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <X className="w-5 h-5" />
                  </Button>
                </div>

                {/* Mobile Search */}
                <div className="mb-6">
                  <div className="flex">
                    <LuminarInput
                      placeholder="Search products..."
                      value={searchQuery}
                      onChange={(value) => setSearchQuery(value)}
                      className="rounded-r-none"
                    />
                    <Button onClick={handleSearch} className="rounded-l-none border-l-0">
                      <Search className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Mobile Categories */}
                <nav className="space-y-2">
                  {categories.map((category) => (
                    <div key={category.id}>
                      <button
                        onClick={() => onCategorySelect?.(category.id)}
                        className="w-full text-left p-3 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      >
                        {category.name}
                      </button>
                      {category.children && (
                        <div className="ml-4 space-y-1">
                          {category.children.map((child) => (
                            <button
                              key={child.id}
                              onClick={() => onCategorySelect?.(child.id)}
                              className="block w-full text-left p-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                            >
                              {child.name}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </nav>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Trust Signals */}
      {trustSignals.length > 0 && (
        <section className="border-y border-gray-200 dark:border-gray-700 py-8">
          <div className="max-w-7xl mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {trustSignals.map((signal, index) => (
                <div key={index} className="flex items-center gap-3 text-center md:text-left">
                  <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                    <signal.icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {signal.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {signal.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Footer */}
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Store Info */}
            <div>
              <div className="flex items-center gap-2 mb-4">
                {logo ? (
                  <img src={logo} alt={storeName} className="h-8 w-auto" />
                ) : (
                  <div className="w-8 h-8 bg-blue-600 rounded text-white flex items-center justify-center font-bold">
                    {storeName.charAt(0)}
                  </div>
                )}
                <span className="text-xl font-bold">{storeName}</span>
              </div>
              <p className="text-gray-400 mb-4">
                Your trusted online shopping destination for quality products at great prices.
              </p>
              <div className="flex items-center gap-3">
                <Facebook className="w-5 h-5 cursor-pointer hover:text-blue-400" />
                <Twitter className="w-5 h-5 cursor-pointer hover:text-blue-400" />
                <Instagram className="w-5 h-5 cursor-pointer hover:text-pink-400" />
              </div>
            </div>

            {/* Footer Links */}
            {footerLinks.map((section, index) => (
              <div key={index}>
                <h3 className="font-semibold mb-4">{section.title}</h3>
                <ul className="space-y-2">
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <a
                        href={link.href}
                        className="text-gray-400 hover:text-white transition-colors"
                        target={link.external ? "_blank" : undefined}
                        rel={link.external ? "noopener noreferrer" : undefined}
                      >
                        {link.label}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* Bottom Footer */}
          <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row items-center justify-between">
            <p className="text-gray-400 text-sm">
              © 2024 {storeName}. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">Payment Methods:</span>
              <div className="flex items-center gap-2">
                <CreditCard className="w-6 h-6 text-gray-400" />
                {/* Add payment method icons */}
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}