import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

  useNeuralColorIntelligence, 
  type BiometricData, 
  type EnvironmentalData, 
  type UserPreferences,
  type ColorPalette

interface NeuralColorDemoProps {
  className?: string;
}

export const NeuralColorDemo: React.FC<NeuralColorDemoProps> = ({ className = '' }) => {
  const [biometricData, setBiometricData] = useState<Partial<BiometricData>>({
    heartRate: 72,
    stressLevel: 0.3,
    attentionSpan: 0.7,
    fatigueLevel: 0.2,
    emotionalState: 'calm'
  });

  const [environmentalData, setEnvironmentalData] = useState<Partial<EnvironmentalData>>({
    timeOfDay: new Date().getHours(),
    seasonIndex: Math.floor((new Date().getMonth() / 12) * 4),
    weatherCondition: 'sunny',
    ambientLight: 0.8
  });

  const [userPreferences, setUserPreferences] = useState<Partial<UserPreferences>>({
    colorTemperature: 'neutral',
    contrastPreference: 'md',
    culturalBackground: 'western'
  });

  const { currentPalette, isAdapting, regeneratePalette } = useNeuralColorIntelligence(
    biometricData,
    environmentalData,
    userPreferences
  );

  const [activeDemo, setActiveDemo] = useState<'biometric' | 'environmental' | 'cultural' | 'accessibility'>('biometric');

  // Simulate real-time biometric changes
  useEffect(() => {
    const interval = setInterval(() => {
      if (activeDemo === 'biometric') {
        setBiometricData(prev => ({
          ...prev,
          heartRate: 60 + Math.random() * 40,
          stressLevel: Math.random(),
          attentionSpan: Math.random(),
          fatigueLevel: Math.random()
        }));
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [activeDemo]);

  const ColorSwatch = ({ color, label }: { color: string; label: string }) => (
    <motion.div
      className="flex flex-col items-center space-y-2"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: 'spring', stiffness: 300, damping: 20 }}
    >
      <motion.div
        className="w-16 h-16 rounded-lg shadow-lg border-2 border-white/20"
        style={{ backgroundColor: color }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
      />
      <span className="text-xs font-medium text-center">{label}</span>
    </motion.div>
  );

  const BiometricControls = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">🫀 Biometric Data</h3>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="block text-sm font-medium">Heart Rate: {biometricData.heartRate?.toFixed(0)} BPM</label>
          <input
            type="range"
            min="50"
            max="120"
            value={biometricData.heartRate || 72}
            onChange={(e) => setBiometricData(prev => ({ ...prev, heartRate: parseInt(e.target.value) }))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>
        
        <div className="space-y-2">
          <label className="block text-sm font-medium">Stress Level: {(biometricData.stressLevel! * 100).toFixed(0)}%</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={biometricData.stressLevel || 0.3}
            onChange={(e) => setBiometricData(prev => ({ ...prev, stressLevel: parseFloat(e.target.value) }))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>
        
        <div className="space-y-2">
          <label className="block text-sm font-medium">Attention Span: {(biometricData.attentionSpan! * 100).toFixed(0)}%</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={biometricData.attentionSpan || 0.7}
            onChange={(e) => setBiometricData(prev => ({ ...prev, attentionSpan: parseFloat(e.target.value) }))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>
        
        <div className="space-y-2">
          <label className="block text-sm font-medium">Fatigue Level: {(biometricData.fatigueLevel! * 100).toFixed(0)}%</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={biometricData.fatigueLevel || 0.2}
            onChange={(e) => setBiometricData(prev => ({ ...prev, fatigueLevel: parseFloat(e.target.value) }))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <label className="block text-sm font-medium">Emotional State</label>
        <select
          value={biometricData.emotionalState || 'calm'}
          onChange={(e) => setBiometricData(prev => ({ ...prev, emotionalState: e.target.value as any }))}
          className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg"
        >
          <option value="joy">😄 Joy</option>
          <option value="calm">😌 Calm</option>
          <option value="stress">😰 Stress</option>
          <option value="excitement">🤩 Excitement</option>
          <option value="sadness">😢 Sadness</option>
          <option value="neutral">😐 Neutral</option>
        </select>
      </div>
    </div>
  );

  const EnvironmentalControls = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">🌍 Environmental Data</h3>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="block text-sm font-medium">Time of Day: {environmentalData.timeOfDay}:00</label>
          <input
            type="range"
            min="0"
            max="23"
            value={environmentalData.timeOfDay || 12}
            onChange={(e) => setEnvironmentalData(prev => ({ ...prev, timeOfDay: parseInt(e.target.value) }))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>
        
        <div className="space-y-2">
          <label className="block text-sm font-medium">Ambient Light: {(environmentalData.ambientLight! * 100).toFixed(0)}%</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={environmentalData.ambientLight || 0.8}
            onChange={(e) => setEnvironmentalData(prev => ({ ...prev, ambientLight: parseFloat(e.target.value) }))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <label className="block text-sm font-medium">Weather Condition</label>
        <select
          value={environmentalData.weatherCondition || 'sunny'}
          onChange={(e) => setEnvironmentalData(prev => ({ ...prev, weatherCondition: e.target.value as any }))}
          className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg"
        >
          <option value="sunny">☀️ Sunny</option>
          <option value="cloudy">☁️ Cloudy</option>
          <option value="rainy">🌧️ Rainy</option>
          <option value="stormy">⛈️ Stormy</option>
          <option value="snowy">🌨️ Snowy</option>
        </select>
      </div>
      
      <div className="space-y-2">
        <label className="block text-sm font-medium">Season</label>
        <select
          value={environmentalData.seasonIndex || 0}
          onChange={(e) => setEnvironmentalData(prev => ({ ...prev, seasonIndex: parseInt(e.target.value) }))}
          className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg"
        >
          <option value={0}>🌸 Spring</option>
          <option value={1}>☀️ Summer</option>
          <option value={2}>🍂 Fall</option>
          <option value={3}>❄️ Winter</option>
        </select>
      </div>
    </div>
  );

  const CulturalControls = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">🌏 Cultural Preferences</h3>
      
      <div className="space-y-2">
        <label className="block text-sm font-medium">Cultural Background</label>
        <select
          value={userPreferences.culturalBackground || 'western'}
          onChange={(e) => setUserPreferences(prev => ({ ...prev, culturalBackground: e.target.value }))}
          className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg"
        >
          <option value="western">🇺🇸 Western</option>
          <option value="eastern">🇯🇵 Eastern</option>
          <option value="nordic">🇳🇴 Nordic</option>
          <option value="mediterranean">🇮🇹 Mediterranean</option>
          <option value="tropical">🌴 Tropical</option>
        </select>
      </div>
      
      <div className="space-y-2">
        <label className="block text-sm font-medium">Color Temperature</label>
        <select
          value={userPreferences.colorTemperature || 'neutral'}
          onChange={(e) => setUserPreferences(prev => ({ ...prev, colorTemperature: e.target.value as any }))}
          className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg"
        >
          <option value="warm">🔥 Warm</option>
          <option value="neutral">⚖️ Neutral</option>
          <option value="cool">❄️ Cool</option>
        </select>
      </div>
      
      <div className="space-y-2">
        <label className="block text-sm font-medium">Contrast Preference</label>
        <select
          value={userPreferences.contrastPreference || 'md'}
          onChange={(e) => setUserPreferences(prev => ({ ...prev, contrastPreference: e.target.value as any }))}
          className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg"
        >
          <option value="low">🌙 Low</option>
          <option value="medium">⚡ Medium</option>
          <option value="high">☀️ High</option>
        </select>
      </div>
    </div>
  );

  const AccessibilityControls = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">♿ Accessibility</h3>
      
      <div className="space-y-2">
        <label className="block text-sm font-medium">Color Blindness Type</label>
        <select
          value={userPreferences.accessibilityNeeds?.colorBlindType || 'none'}
          onChange={(e) => setUserPreferences(prev => ({ 
            ...prev, 
            accessibilityNeeds: { 
              ...prev.accessibilityNeeds, 
              colorBlindType: e.target.value === 'none' ? undefined : e.target.value as any 
            } 
          }))}
          className="w-full px-3 py-2 bg-white border border-gray-300 rounded-lg"
        >
          <option value="none">None</option>
          <option value="protanopia">Protanopia (Red-blind)</option>
          <option value="deuteranopia">Deuteranopia (Green-blind)</option>
          <option value="tritanopia">Tritanopia (Blue-blind)</option>
        </select>
      </div>
      
      <div className="flex items-center space-x-3">
        <input
          type="checkbox"
          id="highContrast"
          checked={userPreferences.accessibilityNeeds?.highContrast || false}
          onChange={(e) => setUserPreferences(prev => ({ 
            ...prev, 
            accessibilityNeeds: { 
              ...prev.accessibilityNeeds, 
              highContrast: e.target.checked 
            } 
          }))}
          className="w-4 h-4 text-blue-600 rounded"
        />
        <label htmlFor="highContrast" className="text-sm font-medium">High Contrast Mode</label>
      </div>
      
      <div className="flex items-center space-x-3">
        <input
          type="checkbox"
          id="reducedColor"
          checked={userPreferences.accessibilityNeeds?.reducedColor || false}
          onChange={(e) => setUserPreferences(prev => ({ 
            ...prev, 
            accessibilityNeeds: { 
              ...prev.accessibilityNeeds, 
              reducedColor: e.target.checked 
            } 
          }))}
          className="w-4 h-4 text-blue-600 rounded"
        />
        <label htmlFor="reducedColor" className="text-sm font-medium">Reduced Color Mode</label>
      </div>
    </div>
  );

  if (!currentPalette) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-2">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto" />
          <p className="text-sm text-muted-foreground">Generating neural color palette...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
          🧠 Neural Color Intelligence
        </h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          AI-powered adaptive color palettes that respond to biometric data, environmental conditions, 
          cultural preferences, and accessibility needs in real-time.
        </p>
      </div>

      {/* Current Palette Display */}
      <motion.div
        className="p-6 rounded-xl border"
        style={{ 
          backgroundColor: currentPalette.background,
          borderColor: currentPalette.accent + '40'
        }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold" style={{ color: currentPalette.text }}>
            Current Adaptive Palette: {currentPalette.name}
          </h3>
          <motion.button
            onClick={regeneratePalette}
            disabled={isAdapting}
            className="px-4 py-2 rounded-lg font-medium transition-all"
            style={{ 
              backgroundColor: currentPalette.primary,
              color: currentPalette.background
            }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {isAdapting ? 'Adapting...' : 'Regenerate'}
          </motion.button>
        </div>
        
        <div className="grid grid-cols-5 gap-4 mb-6">
          <ColorSwatch color={currentPalette.primary} label="Primary" />
          <ColorSwatch color={currentPalette.secondary} label="Secondary" />
          <ColorSwatch color={currentPalette.accent} label="Accent" />
          <ColorSwatch color={currentPalette.success} label="Success" />
          <ColorSwatch color={currentPalette.warning} label="Warning" />
        </div>
        
        <div className="grid grid-cols-5 gap-4">
          <ColorSwatch color={currentPalette.error} label="Error" />
          <ColorSwatch color={currentPalette.info} label="Info" />
          <ColorSwatch color={currentPalette.background} label="Background" />
          <ColorSwatch color={currentPalette.text} label="Text" />
          <ColorSwatch color={currentPalette.muted} label="Muted" />
        </div>
      </motion.div>

      {/* Control Tabs */}
      <div className="flex space-x-2 border-b">
        {[
          { key: 'biometric', label: '🫀 Biometric', icon: '📊' },
          { key: 'environmental', label: '🌍 Environmental', icon: '🌤️' },
          { key: 'cultural', label: '🌏 Cultural', icon: '🎨' },
          { key: 'accessibility', label: '♿ Accessibility', icon: '🔧' }
        ].map(({ key, label }) => (
          <button
            key={key}
            onClick={() => setActiveDemo(key as any)}
            className={`px-4 py-2 rounded-t-lg font-medium transition-all ${
              activeDemo === key
                ? 'bg-blue-500 text-white'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            {label}
          </button>
        ))}
      </div>

      {/* Control Panels */}
      <div className="bg-white rounded-lg border p-6">
        <AnimatePresence mode="wait">
          {activeDemo === 'biometric' && (
            <motion.div
              key="biometric"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <BiometricControls />
            </motion.div>
          )}
          
          {activeDemo === 'environmental' && (
            <motion.div
              key="environmental"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <EnvironmentalControls />
            </motion.div>
          )}
          
          {activeDemo === 'cultural' && (
            <motion.div
              key="cultural"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <CulturalControls />
            </motion.div>
          )}
          
          {activeDemo === 'accessibility' && (
            <motion.div
              key="accessibility"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <AccessibilityControls />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Demo Preview */}
      <div className="p-6 rounded-xl border space-y-4" style={{ backgroundColor: currentPalette.background }}>
        <h3 className="text-xl font-semibold" style={{ color: currentPalette.text }}>
          Live Preview
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <motion.div
            className="p-4 rounded-lg"
            style={{ backgroundColor: currentPalette.primary }}
            whileHover={{ scale: 1.02 }}
          >
            <h4 className="font-semibold" style={{ color: currentPalette.background }}>
              Primary Button
            </h4>
            <p className="text-sm opacity-90" style={{ color: currentPalette.background }}>
              Main call-to-action
            </p>
          </motion.div>
          
          <motion.div
            className="p-4 rounded-lg border"
            style={{ 
              backgroundColor: currentPalette.background,
              borderColor: currentPalette.accent
            }}
            whileHover={{ scale: 1.02 }}
          >
            <h4 className="font-semibold" style={{ color: currentPalette.text }}>
              Card Component
            </h4>
            <p className="text-sm" style={{ color: currentPalette.muted }}>
              Content container
            </p>
          </motion.div>
          
          <motion.div
            className="p-4 rounded-lg"
            style={{ backgroundColor: currentPalette.accent }}
            whileHover={{ scale: 1.02 }}
          >
            <h4 className="font-semibold" style={{ color: currentPalette.text }}>
              Accent Element
            </h4>
            <p className="text-sm opacity-90" style={{ color: currentPalette.text }}>
              Highlighted content
            </p>
          </motion.div>
        </div>
      </div>
    </div>
  );
};