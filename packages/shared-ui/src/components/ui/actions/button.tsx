import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { motion, AnimatePresence } from "framer-motion"
import { Loader2 } from "lucide-react"

import { cn } from '../../../lib/utils'
import { createGlassStyles } from '../../../lib/glass-utils'
import { animationPresets, transitions } from '../../../design-system'
import type {
  ButtonBaseProps,
  ExtendedHTMLProps,
  AnimationPreset
} from '../../../types/component-props'
import { sizeToPixels, defaultComponentProps } from '../../../types/component-props'

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 relative overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl",
        primary: "bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        glass: "backdrop-blur-md bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10 text-gray-900 dark:text-white hover:bg-white/20 dark:hover:bg-white/10 shadow-lg hover:shadow-xl",
        gradient: "bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 shadow-lg hover:shadow-xl",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-lg hover:shadow-xl",
        success: "bg-green-600 text-white hover:bg-green-700 shadow-lg hover:shadow-xl",
        warning: "bg-yellow-600 text-white hover:bg-yellow-700 shadow-lg hover:shadow-xl",
        info: "bg-blue-600 text-white hover:bg-blue-700 shadow-lg hover:shadow-xl"
      },
      size: {
        xs: "h-7 px-2 py-1 text-xs",
        sm: "h-9 px-3 py-2 text-sm",
        md: "h-10 px-4 py-2 text-sm",
        lg: "h-11 px-6 py-3 text-base",
        xl: "h-12 px-8 py-4 text-lg"
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
)

/**
 * Button component props extending the standardized interface
 */
export interface ButtonProps extends 
  ButtonBaseProps,
  ExtendedHTMLProps<React.ButtonHTMLAttributes<HTMLButtonElement>>,
  VariantProps<typeof buttonVariants> {
  /**
   * Whether to render as a child component (using Radix Slot)
   */
  asChild?: boolean
  
  /**
   * Button content
   */
  children?: React.ReactNode
  
  /**
   * Whether to show a shimmer effect
   */
  shimmer?: boolean
  
  /**
   * Whether to show a glow effect
   */
  glow?: boolean
  
  /**
   * Loading spinner variant
   */
  loadingSpinner?: 'default' | 'dots' | 'pulse'
}

/**
 * Standardized Button Component
 * 
 * Implements the complete standardized props interface for consistent API
 * across all Luminar UI components.
 */
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    // Standard component props
    className,
    id,
    style,
    'data-testid': dataTestId,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy,
    
    // Variant props
    variant = defaultComponentProps.variant,
    size = defaultComponentProps.size,
    
    // Glass props
    glass = defaultComponentProps.glass,
    glassIntensity = defaultComponentProps.glassIntensity,
    glassDepth,
    glassConfig,
    
    // Animation props
    animation = defaultComponentProps.animation,
    disableAnimation = defaultComponentProps.disableAnimation,
    animationDuration,
    animationDelay,
    motionProps,
    
    // Interactive props
    disabled = false,
    loading = false,
    loadingText,
    interactive = defaultComponentProps.interactive,
    hoverable = defaultComponentProps.hoverable,
    
    // Icon props
    icon: Icon,
    iconPosition = 'left',
    iconSize,
    iconAnimation,
    
    // Button specific props
    type = 'button',
    onClick,
    fullWidth = false,
    asChild = false,
    shimmer = false,
    glow = false,
    loadingSpinner = 'default',
    
    // Children and remaining props
    children,
    ...props
  }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    // Filter out Framer Motion props that shouldn't be passed to DOM
    const {
      whileHover,
      whileTap,
      whileFocus,
      whileInView,
      animate,
      initial,
      exit,
      variants: motionVariants,
      transition,
      ...domProps
    } = props as any

    const isGlassVariant = variant === 'glass'
    const isPrimary = variant === 'default' || variant === 'primary'
    
    // Calculate icon size based on component size
    const calculatedIconSize = iconSize || sizeToPixels[size || 'md'] * 0.5
    
    // Apply glass styles if enabled
    const glassStyles = glass || isGlassVariant ? createGlassStyles({
      element: 'button',
      profile: glassIntensity === 'subtle' ? 'soft' : 
               glassIntensity === 'intense' ? 'hard' : 'standard',
      interactive: interactive && !disabled && !loading,
      ...glassConfig
    }) : ''
    
    // Get animation configuration
    const animationConfig = disableAnimation || animation === 'none' ? {} : {
      ...(animationPresets[animation || 'fadeIn'] || animationPresets.fadeIn),
      transition: {
        ...transitions.default,
        duration: animationDuration || transitions.default.duration,
        delay: animationDelay || 0
      }
    }
    
    // Loading spinner component
    const LoadingSpinner = () => (
      <motion.div
        className="absolute inset-0 flex items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        {loadingSpinner === 'default' ? (
          <Loader2 className="animate-spin" size={calculatedIconSize} />
        ) : loadingSpinner === 'dots' ? (
          <div className="flex gap-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-current rounded-full"
                animate={{ opacity: [0.3, 1, 0.3] }}
                transition={{ duration: 0.8, delay: i * 0.2, repeat: Infinity }}
              />
            ))}
          </div>
        ) : (
          <motion.div
            className="w-6 h-6 border-2 border-current border-t-transparent rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
        )}
      </motion.div>
    )
    
    const handleClick = () => {
      if (!disabled && !loading && onClick) {
        onClick()
      }
    }
    
    return (
      <motion.div
        whileHover={hoverable && !disabled && !loading ? { scale: 1.02 } : {}}
        whileTap={interactive && !disabled && !loading ? { scale: 0.98 } : {}}
        transition={{ type: "spring" as const, stiffness: 400, damping: 17 }}
        {...animationConfig}
        {...motionProps}
      >
        <Comp
          ref={ref}
          id={id}
          type={!asChild ? type : undefined}
          className={cn(
            buttonVariants({ variant, size }),
            glass && glassStyles,
            loading && "cursor-not-allowed opacity-70",
            fullWidth && "w-full",
            className
          )}
          style={style}
          disabled={disabled || loading}
          onClick={handleClick}
          data-testid={dataTestId}
          aria-label={ariaLabel}
          aria-describedby={ariaDescribedBy}
          aria-busy={loading}
          {...domProps}
        >
          {/* Loading overlay */}
          <AnimatePresence>
            {loading && <LoadingSpinner />}
          </AnimatePresence>
          
          {/* Button content */}
          <span className={cn(
            "relative z-10 flex items-center justify-center gap-2",
            loading && "opacity-0"
          )}>
            {/* Icon on the left */}
            {Icon && iconPosition === 'left' && (
              <Icon 
                size={calculatedIconSize}
                className={cn(
                  "shrink-0",
                  iconAnimation && !disableAnimation && `animate-${iconAnimation}`
                )}
              />
            )}
            
            {/* Children or loading text */}
            {loading && loadingText ? loadingText : children}
            
            {/* Icon on the right */}
            {Icon && iconPosition === 'right' && (
              <Icon 
                size={calculatedIconSize}
                className={cn(
                  "shrink-0",
                  iconAnimation && !disableAnimation && `animate-${iconAnimation}`
                )}
              />
            )}
          </span>

          {/* Shimmer Effect */}
          {shimmer && !loading && !disabled && (
            <motion.div
              className="absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/30 to-transparent"
              initial={{ x: "-100%" }}
              animate={{ x: "200%" }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                repeatDelay: 3,
                ease: "easeInOut"
              }}
            />
          )}

          {/* Glow Effect for Primary Buttons */}
          {glow && isPrimary && !disabled && (
            <motion.div
              className="absolute inset-0 rounded-md bg-gradient-to-r from-blue-400/0 via-blue-400/40 to-blue-400/0"
              animate={{
                opacity: [0, 0.8, 0],
                scale: [0.8, 1.1, 0.8],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          )}
        </Comp>
      </motion.div>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
