import { ReactNode, useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";

import {
  Command,
  ArrowRight,
  History,
  Star,
  Hash,
  User,
  File,
  Folder,
  Settings,
  HelpCircle,
  Zap,
  Calendar,
  Mail,
  MessageSquare,
  Plus,
  ChevronRight,
  Keyboard,
  Search,
  X
} from "lucide-react";

import { LuminarCard } from '../../ui/display';
import { Button } from '../../ui/actions';
import { LuminarInput } from '../../ui/forms';
import { LuminarBadge } from '../../ui/display';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface CommandBarProps {
  className?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  placeholder?: string;
  commands?: CommandGroup[];
  recentCommands?: Command[];
  favorites?: Command[];
  onCommandSelect?: (command: Command) => void;
  onSearch?: (query: string) => void;
  theme?: 'light' | 'dark' | 'glass';
  showKeyboardShortcuts?: boolean;
  showCategories?: boolean;
  showRecents?: boolean;
  showFavorites?: boolean;
  maxResults?: number;
  emptyState?: ReactNode;
  header?: ReactNode;
  footer?: ReactNode;
}

export interface CommandGroup {
  id: string;
  title: string;
  icon?: React.ComponentType<{ className?: string }>;
  commands: Command[];
  priority?: number;
}

export interface Command {
  id: string;
  title: string;
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
  category?: string;
  keywords?: string[];
  shortcut?: string[];
  action: () => void;
  badge?: string;
  isRecent?: boolean;
  isFavorite?: boolean;
  disabled?: boolean;
  subCommands?: Command[];
  metadata?: Record<string, any>;
}

const defaultCommands: CommandGroup[] = [
  {
    id: 'navigation',
    title: 'Navigation',
    icon: ArrowRight,
    priority: 1,
    commands: [
      {
        id: 'dashboard',
        title: 'Go to Dashboard',
        description: 'View your main dashboard',
        icon: ArrowRight,
        shortcut: ['⌘', 'D'],
        action: () => console.log('Navigate to dashboard')
      },
      {
        id: 'settings',
        title: 'Open Settings',
        description: 'Manage your account settings',
        icon: Settings,
        shortcut: ['⌘', ','],
        action: () => console.log('Open settings')
      },
      {
        id: 'help',
        title: 'Help Center',
        description: 'Get help and support',
        icon: HelpCircle,
        shortcut: ['⌘', '?'],
        action: () => console.log('Open help')
      }
    ]
  },
  {
    id: 'actions',
    title: 'Actions',
    icon: Zap,
    priority: 2,
    commands: [
      {
        id: 'new-project',
        title: 'New Project',
        description: 'Create a new project',
        icon: Plus,
        shortcut: ['⌘', 'N'],
        action: () => console.log('Create new project'),
        badge: 'Popular'
      },
      {
        id: 'send-message',
        title: 'Send Message',
        description: 'Send a message to your team',
        icon: MessageSquare,
        shortcut: ['⌘', 'M'],
        action: () => console.log('Send message')
      },
      {
        id: 'schedule-meeting',
        title: 'Schedule Meeting',
        description: 'Schedule a new meeting',
        icon: Calendar,
        shortcut: ['⌘', 'S', 'M'],
        action: () => console.log('Schedule meeting')
      }
    ]
  },
  {
    id: 'search',
    title: 'Search',
    icon: Search,
    priority: 3,
    commands: [
      {
        id: 'search-files',
        title: 'Search Files',
        description: 'Find files in your workspace',
        icon: File,
        shortcut: ['⌘', 'F'],
        action: () => console.log('Search files')
      },
      {
        id: 'search-people',
        title: 'Search People',
        description: 'Find team members',
        icon: User,
        shortcut: ['⌘', 'P'],
        action: () => console.log('Search people')
      }
    ]
  }
];

export function CommandBar({
  className,
  open: controlledOpen,
  onOpenChange,
  placeholder = 'Search for commands...',
  commands = defaultCommands,
  recentCommands = [],
  favorites = [],
  onCommandSelect,
  onSearch,
  theme = 'glass',
  showKeyboardShortcuts = true,
  showCategories = true,
  showRecents = true,
  showFavorites = true,
  maxResults = 50,
  emptyState,
  header,
  footer,
  ...props
}: CommandBarProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [activeGroup, setActiveGroup] = useState<string | null>(null);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  const open = controlledOpen ?? internalOpen;
  const setIsOpen = onOpenChange ?? setInternalOpen;

  // Filter commands based on query
  const filteredCommands = query.length > 0 
    ? commands.flatMap(group => 
        group.commands.filter(command =>
          command.title.toLowerCase().includes(query.toLowerCase()) ||
          command.description?.toLowerCase().includes(query.toLowerCase()) ||
          command.keywords?.some(keyword => 
            keyword.toLowerCase().includes(query.toLowerCase())
          )
        ).map(command => ({
          ...command,
          groupTitle: group.title,
          groupIcon: group.icon
        }))
      ).slice(0, maxResults)
    : [];

  const allCommands = filteredCommands.length > 0 ? filteredCommands : 
    commands.flatMap(group => group.commands.map(command => ({
      ...command,
      groupTitle: group.title,
      groupIcon: group.icon
    })));

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open) {
        // Global shortcut to open command bar
        if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
          e.preventDefault();
          setIsOpen(true);
        }
        return;
      }

      switch (e.key) {
        case 'Escape':
          setIsOpen(false);
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < allCommands.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : prev);
          break;
        case 'Enter':
          e.preventDefault();
          if (allCommands[selectedIndex]) {
            handleCommandSelect(allCommands[selectedIndex]);
          }
          break;
        case 'Tab':
          if (showCategories && activeGroup) {
            e.preventDefault();
            setActiveGroup(null);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [open, selectedIndex, allCommands, activeGroup, showCategories]);

  // Focus input when opened
  useEffect(() => {
    if (open && inputRef.current) {
      inputRef.current.focus();
    }
  }, [open]);

  // Reset state when closing
  useEffect(() => {
    if (!open) {
      setQuery('');
      setSelectedIndex(0);
      setActiveGroup(null);
    }
  }, [open]);

  const handleCommandSelect = (command: Command) => {
    command.action();
    onCommandSelect?.(command);
    setIsOpen(false);
  };

  const handleSearch = (value: string) => {
    setQuery(value);
    setSelectedIndex(0);
    onSearch?.(value);
  };

  const themeClasses = {
    light: 'bg-white border-gray-200',
    dark: 'bg-gray-900 border-gray-700', 
    glass: 'bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-gray-200/20 dark:border-gray-700/20'
  };

  if (!open) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-start justify-center p-4 bg-black/50"
        onClick={() => setIsOpen(false)}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: -20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: -20 }}
          onClick={(e) => e.stopPropagation()}
          className={cn(
            "w-full max-w-2xl mt-20 rounded-lg border shadow-2xl",
            themeClasses[theme],
            className
          )}
          {...props}
        >
          {/* Header */}
          {header || (
            <div className="flex items-center gap-3 p-4 border-b border-gray-200/20 dark:border-gray-700/20">
              <Search className="w-5 h-5 text-gray-400" />
              <input
                ref={inputRef}
                type="text"
                value={query}
                onChange={(e) => handleSearch(e.target.value)}
                placeholder={placeholder}
                className="flex-1 bg-transparent outline-none text-gray-900 dark:text-white placeholder-gray-500"
              />
              <div className="flex items-center gap-2">
                {showKeyboardShortcuts && (
                  <div className="hidden sm:flex items-center gap-1 text-xs text-gray-500">
                    <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs">↑↓</kbd>
                    <span>navigate</span>
                  </div>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                  className="p-1"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}

          {/* Content */}
          <div className="max-h-96 overflow-y-auto" ref={listRef}>
            {/* Recent Commands */}
            {showRecents && recentCommands.length > 0 && query.length === 0 && (
              <div className="p-4 border-b border-gray-200/20 dark:border-gray-700/20">
                <div className="flex items-center gap-2 mb-3">
                  <History className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  </span>
                </div>
                <div className="space-y-1">
                  {recentCommands.slice(0, 3).map((command, index) => (
                    <CommandItem
                      key={command.id}
                      command={command}
                      isSelected={false}
                      onClick={() => handleCommandSelect(command)}
                      showKeyboardShortcuts={showKeyboardShortcuts}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Favorites */}
            {showFavorites && favorites.length > 0 && query.length === 0 && (
              <div className="p-4 border-b border-gray-200/20 dark:border-gray-700/20">
                <div className="flex items-center gap-2 mb-3">
                  <Star className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  </span>
                </div>
                <div className="space-y-1">
                  {favorites.slice(0, 3).map((command) => (
                    <CommandItem
                      key={command.id}
                      command={command}
                      isSelected={false}
                      onClick={() => handleCommandSelect(command)}
                      showKeyboardShortcuts={showKeyboardShortcuts}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Commands */}
            <div className="p-2">
              {query.length > 0 ? (
                // Search Results
                <div className="space-y-1">
                  {filteredCommands.length > 0 ? (
                    filteredCommands.map((command, index) => (
                      <CommandItem
                        key={command.id}
                        command={command}
                        isSelected={index === selectedIndex}
                        onClick={() => handleCommandSelect(command)}
                        showKeyboardShortcuts={showKeyboardShortcuts}
                        showGroup={true}
                      />
                    ))
                  ) : (
                    <div className="p-8 text-center">
                      {emptyState || (
                        <div>
                          <Search className="w-8 h-8 text-gray-400 mx-auto mb-3" />
                          <p className="text-gray-600 dark:text-gray-400">
                            No results found for "{query}"
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ) : showCategories ? (
                // Categories
                <div className="space-y-4">
                  {commands
                    .sort((a, b) => (a.priority || 999) - (b.priority || 999))
                    .map((group) => (
                      <div key={group.id}>
                        <div className="flex items-center gap-2 mb-2 px-3 py-1">
                          {group.icon && <group.icon className="w-4 h-4 text-gray-400" />}
                          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            {group.title}
                          </span>
                        </div>
                        <div className="space-y-1">
                          {group.commands.slice(0, 3).map((command, index) => {
                            const globalIndex = commands
                              .slice(0, commands.findIndex(g => g.id === group.id))
                              .reduce((acc, g) => acc + Math.min(g.commands.length, 3), 0) + index;
                            
                            return (
                              <CommandItem
                                key={command.id}
                                command={command}
                                isSelected={globalIndex === selectedIndex}
                                onClick={() => handleCommandSelect(command)}
                                showKeyboardShortcuts={showKeyboardShortcuts}
                              />
                            );
                          })}
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                // All Commands
                <div className="space-y-1">
                  {allCommands.map((command, index) => (
                    <CommandItem
                      key={command.id}
                      command={command}
                      isSelected={index === selectedIndex}
                      onClick={() => handleCommandSelect(command)}
                      showKeyboardShortcuts={showKeyboardShortcuts}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          {footer || (showKeyboardShortcuts && (
            <div className="flex items-center justify-between p-3 border-t border-gray-200/20 dark:border-gray-700/20 text-xs text-gray-500">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded">⌘K</kbd>
                  <span>to open</span>
                </div>
                <div className="flex items-center gap-1">
                  <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded">↵</kbd>
                  <span>to select</span>
                </div>
                <div className="flex items-center gap-1">
                  <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded">esc</kbd>
                  <span>to close</span>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <Keyboard className="w-3 h-3" />
                <span>Keyboard navigation</span>
              </div>
            </div>
          ))}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}

interface CommandItemProps {
  command: Command & { groupTitle?: string; groupIcon?: React.ComponentType<{ className?: string }> };
  isSelected: boolean;
  onClick: () => void;
  showKeyboardShortcuts: boolean;
  showGroup?: boolean;
}

function CommandItem({ 
  command, 
  isSelected, 
  onClick, 
  showKeyboardShortcuts,
  showGroup = false 
}: CommandItemProps) {
  return (
    <button
      onClick={onClick}
      disabled={command.disabled}
      className={cn(
        "w-full flex items-center gap-3 p-3 rounded-lg text-left transition-colors",
        isSelected 
          ? "bg-blue-50 dark:bg-blue-950/50" 
          : "hover:bg-gray-50 dark:hover:bg-gray-800",
        command.disabled && "opacity-50 cursor-not-allowed"
      )}
    >
      {/* Icon */}
      <div className="flex-shrink-0">
        {command.icon ? (
          <command.icon className={cn(
            "w-5 h-5",
            isSelected ? "text-blue-600 dark:text-blue-400" : "text-gray-400"
          )} />
        ) : (
          <div className="w-5 h-5" />
        )}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className={cn(
            "font-medium",
            isSelected ? "text-blue-600 dark:text-blue-400" : "text-gray-900 dark:text-white"
          )}>
            {command.title}
          </span>
          {command.badge && (
            <LuminarBadge size="sm" variant="default">
              {command.badge}
            </LuminarBadge>
          )}
          {showGroup && command.groupTitle && (
            <div className="flex items-center gap-1 text-xs text-gray-500">
              {command.groupIcon && <command.groupIcon className="w-3 h-3" />}
              <span>{command.groupTitle}</span>
            </div>
          )}
        </div>
        {command.description && (
          <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
            {command.description}
          </p>
        )}
      </div>

      {/* Keyboard Shortcut */}
      {showKeyboardShortcuts && command.shortcut && (
        <div className="flex items-center gap-1">
          {command.shortcut.map((key, index) => (
            <kbd 
              key={index}
              className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs"
            >
              {key}
            </kbd>
          ))}
        </div>
      )}

      {/* Sub-commands indicator */}
      {command.subCommands && command.subCommands.length > 0 && (
        <ChevronRight className="w-4 h-4 text-gray-400" />
      )}
    </button>
  );
}