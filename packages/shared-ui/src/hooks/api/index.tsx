/**
 * API Hooks - Unified React Query hooks for all services
 */

import { 
  UsersService, 
  TrainingService, 
  VendorsService, 
  EmailService, 
  WinsService 
} from '@luminar/shared-core';

// Import hook factories
import { createUsersHooks, userKeys } from './users.hooks';
import { createTrainingHooks, trainingKeys } from './training.hooks';

export { createUsersHooks, userKeys, createTrainingHooks, trainingKeys };
export type { UsersHooks } from './users.hooks';
export type { TrainingHooks } from './training.hooks';

// Services interface for hook factory
export interface ApiServices {
  users: UsersService;
  training: TrainingService;
  vendors: VendorsService;
  email: EmailService;
  wins: WinsService;
}

// Unified hook factory that creates all API hooks
export function createApiHooks(services: ApiServices) {
  const usersHooks = createUsersHooks(services.users);
  const trainingHooks = createTrainingHooks(services.training);

  return {
    users: usersHooks,
    training: trainingHooks,
    
    // Query keys for all services
    queryKeys: {
      users: usersHooks.userKeys,
      training: trainingHooks.trainingKeys,
    }
  };
}

// Hook provider context setup
import { createContext, useContext, ReactNode } from 'react';

const ApiHooksContext = createContext<ReturnType<typeof createApiHooks> | null>(null);

export interface ApiHooksProviderProps {
  children: ReactNode;
  services: ApiServices;
}

export function ApiHooksProvider({ children, services }: ApiHooksProviderProps) {
  const hooks = createApiHooks(services);
  
  return (
    <ApiHooksContext.Provider value={hooks}>
      {children}
    </ApiHooksContext.Provider>
  );
}

// Hook to use API hooks from context
export function useApiHooks() {
  const context = useContext(ApiHooksContext);
  if (!context) {
    throw new Error('useApiHooks must be used within an ApiHooksProvider');
  }
  return context;
}

// Individual hook accessors
export function useUsersHooks() {
  return useApiHooks().users;
}

export function useTrainingHooks() {
  return useApiHooks().training;
}

// Utility hook to invalidate all queries
export function useInvalidateAllQueries() {
  const { queryKeys } = useApiHooks();
  
  return {
    invalidateUsers: () => {
      // Implementation would use queryClient.invalidateQueries
      // This is a placeholder for the pattern
    },
    invalidateTrainings: () => {
      // Implementation would use queryClient.invalidateQueries
    },
    invalidateAll: () => {
      // Invalidate all queries
    }
  };
}

// Re-export commonly used React Query utilities
export {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
  useSuspenseQuery
} from '@tanstack/react-query';