import React from 'react';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface MetricCardProps {
  title: string;
  value: string | number;
  unit?: string;
  change?: {
    value: number;
    period: string;
    trend: 'positive' | 'negative' | 'neutral';
  };
  status?: 'success' | 'warning' | 'error' | 'info' | 'neutral';
  chart?: React.ReactNode;
  className?: string;
}

export function LuminarMetricCard({
  title,
  value,
  unit,
  change,
  status = 'neutral',
  chart,
  className = ''
}: MetricCardProps) {
  const glassClasses = getAdaptiveGlassClasses('card', 'md', status);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 dark:text-green-400';
      case 'warning': return 'text-yellow-600 dark:text-yellow-400';
      case 'error': return 'text-red-600 dark:text-red-400';
      case 'info': return 'text-blue-600 dark:text-blue-400';
      default: return 'text-foreground';
    }
  };

  const getChangeColor = (trend: string) => {
    switch (trend) {
      case 'positive': return 'text-green-600 dark:text-green-400';
      case 'negative': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getChangeIcon = (trend: string) => {
    switch (trend) {
      case 'positive': return '↗';
      case 'negative': return '↘';
      default: return '→';
    }
  };

  return (
    <div className={`${glassClasses} p-6 rounded-lg border relative overflow-hidden ${className}`}>
      {/* Background chart if provided */}
      {chart && (
        <div className="absolute inset-0 opacity-10">
          {chart}
        </div>
      )}
      
      <div className="relative z-10">
        <div className="mb-4">
          <h3 className="text-sm font-medium text-muted-foreground mb-1">{title}</h3>
          <div className="flex items-baseline gap-1">
            <span className={`text-3xl font-bold ${getStatusColor(status)}`}>
              {value}
            </span>
            {unit && (
              <span className="text-sm text-muted-foreground">{unit}</span>
            )}
          </div>
        </div>

        {change && (
          <div className={`flex items-center gap-1 text-sm ${getChangeColor(change.trend)}`}>
            <span>{getChangeIcon(change.trend)}</span>
            <span>{Math.abs(change.value)}%</span>
            <span className="text-muted-foreground">{change.period}</span>
          </div>
        )}
      </div>
    </div>
  );
}