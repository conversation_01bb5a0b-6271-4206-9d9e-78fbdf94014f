import { ReactNode, useState } from "react";
import { motion } from "framer-motion";

import {
  Check,
  Star,
  Play,
  Download,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Menu,
  ChevronDown,
  Zap,
  Shield,
  Rocket,
  Users,
  Award,
  TrendingUp,
  X,
  ArrowRight
} from "lucide-react";

import { LuminarCard } from '../../ui/display';
import { Button } from '../../ui/actions';
import { LuminarInput } from '../../ui/forms';
import { LuminarBadge, LuminarAvatar } from '../../ui/display';
import { ThemeToggle } from '../../ui/utilities';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface LandingPageProps {
  className?: string;
  // Header
  logo?: string;
  brandName?: string;
  navigation?: NavItem[];
  ctaButton?: CTAButton;
  
  // Hero Section
  heroTitle?: string;
  heroSubtitle?: string;
  heroDescription?: string;
  heroImage?: string;
  heroVideo?: string;
  heroCTA?: CTAButton[];
  
  // Features Section
  features?: Feature[];
  featuresTitle?: string;
  featuresSubtitle?: string;
  
  // Testimonials
  testimonials?: Testimonial[];
  testimonialsTitle?: string;
  
  // Pricing
  pricingPlans?: PricingPlan[];
  pricingTitle?: string;
  pricingSubtitle?: string;
  
  // FAQ
  faqs?: FAQ[];
  faqTitle?: string;
  
  // Footer
  footerSections?: FooterSection[];
  footerSocial?: SocialLink[];
  footerCopyright?: string;
  
  // Customization
  theme?: 'light' | 'dark' | 'gradient';
  showMobileMenu?: boolean;
  onMobileMenuToggle?: (open: boolean) => void;
  onSignup?: (email: string) => void;
  onCTAClick?: (ctaId: string) => void;
}

export interface NavItem {
  id: string;
  label: string;
  href?: string;
  onClick?: () => void;
  children?: NavItem[];
}

export interface CTAButton {
  id: string;
  label: string;
  variant?: ComponentVariant | 'secondary' | 'outline';
  onClick?: () => void;
  href?: string;
}

export interface Feature {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  image?: string;
  benefits?: string[];
}

export interface Testimonial {
  id: string;
  name: string;
  role: string;
  company: string;
  avatar?: string;
  rating: number;
  content: string;
}

export interface PricingPlan {
  id: string;
  name: string;
  price: number;
  period: 'month' | 'year';
  description: string;
  features: string[];
  popular?: boolean;
  cta: CTAButton;
}

export interface FAQ {
  id: string;
  question: string;
  answer: string;
}

export interface FooterSection {
  title: string;
  links: FooterLink[];
}

export interface FooterLink {
  label: string;
  href: string;
}

export interface SocialLink {
  platform: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
}

const defaultFeatures: Feature[] = [
  {
    id: '1',
    title: 'Lightning Fast',
    description: 'Built for speed with optimal performance',
    icon: Zap,
    benefits: ['99.9% uptime', 'Global CDN', 'Edge computing']
  },
  {
    id: '2', 
    title: 'Secure by Design',
    description: 'Enterprise-grade security and privacy',
    icon: Shield,
    benefits: ['End-to-end encryption', 'SOC 2 compliant', 'GDPR ready']
  },
  {
    id: '3',
    title: 'Scale Effortlessly',
    description: 'Grows with your business needs',
    icon: Rocket,
    benefits: ['Auto-scaling', 'Load balancing', 'Global infrastructure']
  }
];

const defaultTestimonials: Testimonial[] = [
  {
    id: '1',
    name: 'Sarah Johnson',
    role: 'CEO',
    company: 'TechCorp',
    rating: 5,
    content: 'This platform has transformed how we work. The results speak for themselves.'
  },
  {
    id: '2',
    name: 'Mike Chen',
    role: 'CTO',
    company: 'StartupXYZ',
    rating: 5,
    content: 'Incredible value and ease of use. Our team adopted it instantly.'
  }
];

export function LandingPage({
  className,
  logo,
  brandName = "YourBrand",
  navigation = [],
  ctaButton = { id: 'header-cta', label: 'Get Started', variant: 'primary' },
  heroTitle = "Build Something Amazing",
  heroSubtitle = "The modern platform for",
  heroDescription = "Transform your ideas into reality with our powerful, easy-to-use platform designed for creators, entrepreneurs, and teams.",
  heroImage,
  heroVideo,
  heroCTA = [
    { id: 'hero-primary', label: 'Start Free Trial', variant: 'primary' },
    { id: 'hero-secondary', label: 'Watch Demo', variant: 'outline' }
  ],
  features = defaultFeatures,
  featuresTitle = "Everything you need to succeed",
  featuresSubtitle = "Powerful features designed to help you achieve your goals",
  testimonials = defaultTestimonials,
  testimonialsTitle = "Loved by thousands",
  pricingPlans = [],
  pricingTitle = "Simple, transparent pricing",
  pricingSubtitle = "Choose the plan that's right for you",
  faqs = [],
  faqTitle = "Frequently asked questions",
  footerSections = [],
  footerSocial = [],
  footerCopyright = `© 2024 ${brandName}. All rights reserved.`,
  theme = 'gradient',
  showMobileMenu: controlledMobileMenu,
  onMobileMenuToggle,
  onSignup,
  onCTAClick,
  ...props
}: LandingPageProps) {
  const [internalMobileMenu, setInternalMobileMenu] = useState(false);
  const [email, setEmail] = useState("");
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);

  const mobileMenuOpen = controlledMobileMenu ?? internalMobileMenu;
  const setMobileMenuOpen = onMobileMenuToggle ?? setInternalMobileMenu;

  const handleSignup = () => {
    if (email.trim()) {
      onSignup?.(email);
      setEmail("");
    }
  };

  const backgroundClasses = {
    light: "bg-white text-gray-900",
    dark: "bg-gray-900 text-white",
    gradient: "bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-950 dark:to-purple-950"
  };

  return (
    <div className={cn(
      "min-h-screen",
      backgroundClasses[theme],
      className
    )} {...props}>
      
      {/* Header */}
      <header className="sticky top-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg border-b border-gray-200/20 dark:border-gray-700/20 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center gap-3">
              {logo ? (
                <img src={logo} alt={brandName} className="h-8 w-auto" />
              ) : (
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center font-bold text-white">
                  {brandName.charAt(0)}
                </div>
              )}
              <span className="text-xl font-bold">{brandName}</span>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              {navigation.map((item) => (
                <a
                  key={item.id}
                  href={item.href}
                  onClick={item.onClick}
                  className="text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors"
                >
                  {item.label}
                </a>
              ))}
            </nav>

            {/* Header CTA */}
            <div className="hidden md:flex items-center gap-4">
              <ThemeToggle />
              <Button
                variant={ctaButton.variant as any}
                onClick={() => onCTAClick?.(ctaButton.id)}
              >
                {ctaButton.label}
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden border-t border-gray-200 dark:border-gray-700"
          >
            <div className="px-4 py-6 space-y-4">
              {navigation.map((item) => (
                <a
                  key={item.id}
                  href={item.href}
                  onClick={item.onClick}
                  className="block text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white"
                >
                  {item.label}
                </a>
              ))}
              <Button
                variant={ctaButton.variant as any}
                onClick={() => onCTAClick?.(ctaButton.id)}
                className="w-full"
              >
                {ctaButton.label}
              </Button>
            </div>
          </motion.div>
        )}
      </header>

      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <div className="space-y-4">
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight">
                  <span className="block">{heroSubtitle}</span>
                  <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    {heroTitle}
                  </span>
                </h1>
                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-lg">
                  {heroDescription}
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                {heroCTA.map((cta) => (
                  <Button
                    key={cta.id}
                    variant={cta.variant as any}
                    size="lg"
                    onClick={() => onCTAClick?.(cta.id)}
                    className="flex items-center gap-2"
                  >
                    {cta.variant === 'outline' && <Play className="w-4 h-4" />}
                    {cta.label}
                    {cta.variant === 'primary' && <ArrowRight className="w-4 h-4" />}
                  </Button>
                ))}
              </div>

              {/* Social Proof */}
              <div className="flex items-center gap-6 pt-8">
                <div className="flex -space-x-2">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div
                      key={i}
                      className="w-8 h-8 rounded-full bg-gray-300 border-2 border-white"
                    />
                  ))}
                </div>
                <div>
                  <p className="text-sm font-medium">10,000+ happy customers</p>
                  <div className="flex items-center gap-1">
                    {[1, 2, 3, 4, 5].map((i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                    <span className="text-sm text-gray-600 ml-1">4.9/5</span>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              {heroImage && (
                <img
                  src={heroImage}
                  alt="Hero"
                  className="w-full rounded-2xl shadow-2xl"
                />
              )}
              {heroVideo && (
                <video
                  src={heroVideo}
                  className="w-full rounded-2xl shadow-2xl"
                  autoPlay
                  muted
                  loop
                />
              )}
              {!heroImage && !heroVideo && (
                <div className="w-full h-96 bg-gradient-to-br from-blue-400 to-purple-600 rounded-2xl shadow-2xl flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Play className="w-8 h-8" />
                    </div>
                    <p className="text-lg font-medium">Product Demo</p>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white/50 dark:bg-gray-800/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl font-bold mb-4">
              {featuresTitle}
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              {featuresSubtitle}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
              >
                <LuminarCard className="p-8 text-center h-full hover:shadow-lg transition-shadow">
                  <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-6">
                    <feature.icon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4">{feature.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    {feature.description}
                  </p>
                  {feature.benefits && (
                    <ul className="space-y-2">
                      {feature.benefits.map((benefit, idx) => (
                        <li key={idx} className="flex items-center gap-2 text-sm">
                          <Check className="w-4 h-4 text-green-500" />
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  )}
                </LuminarCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      {testimonials.length > 0 && (
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl sm:text-4xl font-bold mb-4">
                {testimonialsTitle}
              </h2>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial.id}
                  initial={{ opacity: 0, scale: 0.95 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                >
                  <LuminarCard className="p-8">
                    <div className="flex items-center gap-1 mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 mb-6 italic">
                      "{testimonial.content}"
                    </p>
                    <div className="flex items-center gap-4">
                      <LuminarAvatar
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        fallback={testimonial.name.charAt(0)}
                        size="md"
                      />
                      <div>
                        <p className="font-semibold">{testimonial.name}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {testimonial.role} at {testimonial.company}
                        </p>
                      </div>
                    </div>
                  </LuminarCard>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <h2 className="text-3xl sm:text-4xl font-bold">
              Ready to get started?
            </h2>
            <p className="text-xl opacity-90">
              Join thousands of satisfied customers and transform your workflow today.
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 max-w-md mx-auto">
              <LuminarInput
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                className="flex-1"
                onKeyPress={(e) => e.key === 'Enter' && handleSignup()}
              />
              <Button
                variant="outline"
                onClick={handleSignup}
                disabled={!email.trim()}
              >
                Get Started Free
              </Button>
            </div>
            
            <p className="text-sm opacity-75">
              No credit card required. Start your free trial today.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            {/* Brand */}
            <div>
              <div className="flex items-center gap-3 mb-4">
                {logo ? (
                  <img src={logo} alt={brandName} className="h-8 w-auto" />
                ) : (
                  <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center font-bold">
                    {brandName.charAt(0)}
                  </div>
                )}
                <span className="text-xl font-bold">{brandName}</span>
              </div>
              <p className="text-gray-400 mb-4">
                Building the future, one solution at a time.
              </p>
              <div className="flex gap-4">
                {footerSocial.map((social) => (
                  <a
                    key={social.platform}
                    href={social.url}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <social.icon className="w-5 h-5" />
                  </a>
                ))}
              </div>
            </div>

            {/* Footer Links */}
            {footerSections.map((section) => (
              <div key={section.title}>
                <h3 className="font-semibold mb-4">{section.title}</h3>
                <ul className="space-y-2">
                  {section.links.map((link) => (
                    <li key={link.label}>
                      <a
                        href={link.href}
                        className="text-gray-400 hover:text-white transition-colors"
                      >
                        {link.label}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="border-t border-gray-800 pt-8">
            <p className="text-gray-400 text-center">
              {footerCopyright}
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}