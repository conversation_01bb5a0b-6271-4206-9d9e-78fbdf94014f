import { motion, AnimatePresence } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, useState } from "react"
import { LuminarIcon } from "../utilities/icon"
import { LucideIcon } from "lucide-react"
import { Link } from "@tanstack/react-router"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface NavItem {
  id: string
  label: string
  href: string
  icon?: LucideIcon
  badge?: number
}

export interface LuminarNavbarProps {
  items: NavItem[]
  variant?: ComponentVariant | "floating" | "sidebar"
  logo?: React.ReactNode
  activeItemId?: string
  onItemClick?: (item: NavItem) => void
  className?: string
}

const LuminarNavbar = forwardRef<HTMLElement, LuminarNavbarProps>(
  ({ 
    items, 
    variant = defaultComponentProps.variant, 
    logo,
    activeItemId,
    onItemClick,
    className 
  }, ref) => {
    const [hoveredId, setHoveredId] = useState<string | null>(null)
    
    const variantClasses = {
      default: "w-full border-b",
      floating: "max-w-max mx-auto rounded-full shadow-2xl",
      sidebar: "h-full w-64 border-r flex-col"
    }

    const containerVariants = {
      hidden: { opacity: 0, y: -20 },
      visible: {
        opacity: 1,
        y: 0,
        transition: {
          duration: 0.5,
          staggerChildren: 0.1
        }
      }
    }

    const itemVariants = {
      hidden: { opacity: 0, y: -10 },
      visible: { opacity: 1, y: 0 }
    }

    return (
      <motion.nav
        ref={ref}
        className={cn(
          "backdrop-blur-xl bg-white/5 dark:bg-gray-900/5",
          "border-white/10 dark:border-gray-700/20",
          variantClasses[variant],
          className
        )}
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <div className={cn(
          "flex items-center px-6 py-4",
          variant === "sidebar" && "flex-col items-start"
        )}>
          {logo && (
            <motion.div 
              className={cn(
                "mr-8",
                variant === "sidebar" && "mb-8 mr-0"
              )}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {logo}
            </motion.div>
          )}
          
          <motion.ul 
            className={cn(
              "flex items-center gap-2",
              variant === "sidebar" && "flex-col w-full"
            )}
            variants={containerVariants}
          >
            {items.map((item) => {
              const active = activeItemId === item.id
              const hovered = hoveredId === item.id

              return (
                <motion.li
                  key={item.id}
                  variants={itemVariants}
                  className="relative"
                  onMouseEnter={() => setHoveredId(item.id)}
                  onMouseLeave={() => setHoveredId(null)}
                >
                  <Link
                    to={item.href}
                    onClick={() => onItemClick?.(item)}
                    className={cn(
                      "relative flex items-center gap-3 px-4 py-2 rounded-lg transition-colors",
                      "hover:bg-white/10 dark:hover:bg-gray-700/20",
                      active && "text-primary",
                      variant === "sidebar" && "w-full"
                    )}
                  >
                    {item.icon && (
                      <LuminarIcon
                        icon={item.icon}
                        size={20}
                        animation={hovered ? "pulse" : "none"}
                        trigger="none"
                      />
                    )}
                    <span className="font-medium">{item.label}</span>
                    
                    {item.badge && item.badge > 0 && (
                      <motion.span
                        className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 500, damping: 15 }}
                      >
                        {item.badge > 99 ? "99+" : item.badge}
                      </motion.span>
                    )}
                    
                    <AnimatePresence>
                      {active && (
                        <motion.div
                          className={cn(
                            "absolute bg-primary",
                            variant === "sidebar" 
                              ? "left-0 top-0 bottom-0 w-1 rounded-r-full" 
                              : "bottom-0 left-0 right-0 h-0.5 rounded-t-full"
                          )}
                          layoutId="activeIndicator"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ type: "spring", stiffness: 400, damping: 30 }}
                        />
                      )}
                    </AnimatePresence>
                  </Link>
                </motion.li>
              )
            })}
          </motion.ul>
        </div>
      </motion.nav>
    )
  }
)
LuminarNavbar.displayName = "LuminarNavbar"

export { LuminarNavbar }