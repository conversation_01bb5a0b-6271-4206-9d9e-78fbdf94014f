import React from 'react';
import { LuminarButton } from '../ui/actions/button-advanced';
import { toast } from '../../lib/toast';

interface OAuthProvider {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  enabled: boolean;
}

interface OAuthProvidersProps {
  onProviderClick?: (provider: string) => void;
  loading?: boolean;
  disabled?: boolean;
  className?: string;
  providers?: OAuthProvider[];
}

const defaultProviders: OAuthProvider[] = [
  {
    id: 'google',
    name: 'Google',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24">
        <path
          fill="#4285f4"
          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
        />
        <path
          fill="#34a853"
          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
        />
        <path
          fill="#fbbc05"
          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
        />
        <path
          fill="#ea4335"
          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
        />
      </svg>
    ),
    color: 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50',
    enabled: true
  },
  {
    id: 'github',
    name: 'GitHub',
    icon: (
      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
      </svg>
    ),
    color: 'bg-gray-900 text-white hover:bg-gray-800',
    enabled: true
  }
];

export const OAuthProviders: React.FC<OAuthProvidersProps> = ({
  onProviderClick,
  loading = false,
  disabled = false,
  className = '',
  providers = defaultProviders
}) => {
  const handleProviderClick = async (providerId: string) => {
    if (disabled || loading) return;
    
    try {
      if (onProviderClick) {
        onProviderClick(providerId);
      } else {
        // Default OAuth flow
        const baseUrl = (import.meta as any).env?.VITE_API_URL || 'http://localhost:3000';
        const redirectUri = encodeURIComponent(`${window.location.origin}/auth/callback`);
        
        // Redirect to OAuth provider
        window.location.href = `${baseUrl}/auth/oauth/${providerId}?redirect_uri=${redirectUri}`;
      }
    } catch (error) {
      console.error(`OAuth ${providerId} failed:`, error);
      toast.error(`Failed to authenticate with ${providerId}`);
    }
  };

  const enabledProviders = providers.filter(p => p.enabled);

  if (enabledProviders.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-gray-300" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">Or continue with</span>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-3">
        {enabledProviders.map((provider) => (
          <LuminarButton
            key={provider.id}
            type="button"
            variant="outline"
            className={`w-full ${provider.color}`}
            onClick={() => handleProviderClick(provider.id)}
            disabled={disabled || loading}
            loading={loading}
          >
            <div className="flex items-center justify-center space-x-2">
              {provider.icon}
              <span>Continue with {provider.name}</span>
            </div>
          </LuminarButton>
        ))}
      </div>
    </div>
  );
};

// OAuth callback handler component
interface OAuthCallbackProps {
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
  children?: React.ReactNode;
}

export const OAuthCallback: React.FC<OAuthCallbackProps> = ({
  onSuccess,
  onError,
  children
}) => {
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const handleCallback = async () => {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const errorParam = urlParams.get('error');
        const provider = urlParams.get('provider');

        if (errorParam) {
          const errorMessage = `OAuth error: ${errorParam}`;
          setError(errorMessage);
          onError?.(errorMessage);
          return;
        }

        if (!code) {
          const errorMessage = 'No authorization code received';
          setError(errorMessage);
          onError?.(errorMessage);
          return;
        }

        // Exchange code for tokens
        const baseUrl = (import.meta as any).env?.VITE_API_URL || 'http://localhost:3000';
        const response = await fetch(`${baseUrl}/auth/oauth/callback`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            code,
            state,
            provider,
            redirect_uri: `${window.location.origin}/auth/callback`
          }),
        });

        if (!response.ok) {
          throw new Error('OAuth callback failed');
        }

        const data = await response.json();
        
        // Store tokens if needed
        if (data.tokens) {
          localStorage.setItem('ld_access_token', data.tokens.accessToken);
          localStorage.setItem('ld_refresh_token', data.tokens.refreshToken);
        }

        toast.success('Successfully authenticated!');
        onSuccess?.(data);
        
        // Redirect to dashboard or intended page
        const redirectTo = sessionStorage.getItem('auth_redirect') || '/dashboard';
        sessionStorage.removeItem('auth_redirect');
        window.location.href = redirectTo;
        
      } catch (error) {
        console.error('OAuth callback error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Authentication failed';
        setError(errorMessage);
        onError?.(errorMessage);
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    handleCallback();
  }, [onSuccess, onError]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Completing authentication...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Authentication Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <LuminarButton
            onClick={() => window.location.href = '/auth/signin'}
            variant="outline"
          >
            Back to Sign In
          </LuminarButton>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default OAuthProviders;