import * as React from 'react';
import { cn } from '../../../lib/utils';
import { createGlassStyles, glassPresets } from '../../lib/glass-utils';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  open?: boolean;
  onClose?: () => void;
  children?: React.ReactNode;
}

const Sidebar = React.forwardRef<HTMLDivElement, SidebarProps>(
  ({ className, children, open = true, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out',
          'glass-text-safe glass-sidebar-safe',
          open ? 'translate-x-0' : '-translate-x-full',
          className
        )}
        {...props}
      >
        <div className="h-full p-4 overflow-y-auto">
          {children}
        </div>
      </div>
    );
  }
);
Sidebar.displayName = 'Sidebar';

export { Sidebar };
