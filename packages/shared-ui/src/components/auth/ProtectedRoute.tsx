import React from 'react';
import { useAuth } from '../../providers/auth-provider';
import { useRouter } from '@tanstack/react-router';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  requireAll?: boolean; // If true, user must have ALL permissions/roles, otherwise ANY
  fallback?: React.ReactNode;
  redirectTo?: string;
  loadingComponent?: React.ReactNode;
  unauthorizedComponent?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  requireAll = false,
  fallback,
  redirectTo = '/auth/signin',
  loadingComponent,
  unauthorizedComponent
}) => {
  const { 
    isAuthenticated, 
    isLoading, 
    hasPermission, 
    hasRole, 
    hasAnyRole, 
    hasAllRoles 
  } = useAuth();
  const router = useRouter();

  // Show loading state
  if (isLoading) {
    return loadingComponent ? (
      <>{loadingComponent}</>
    ) : (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.navigate({ to: redirectTo, replace: true });
    return null;
  }

  // Check role requirements
  if (requiredRoles.length > 0) {
    const hasRequiredRoles = requireAll 
      ? hasAllRoles(requiredRoles)
      : hasAnyRole(requiredRoles);

    if (!hasRequiredRoles) {
      if (unauthorizedComponent) {
        return <>{unauthorizedComponent}</>;
      }
      if (fallback) {
        return <>{fallback}</>;
      }
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
            <p className="text-gray-600 mb-4">You don't have the required role to access this page.</p>
            <p className="text-sm text-gray-500">
              Required roles: {requiredRoles.join(requireAll ? ' AND ' : ' OR ')}
            </p>
          </div>
        </div>
      );
    }
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const hasRequiredPermissions = requireAll
      ? requiredPermissions.every(permission => hasPermission(permission))
      : requiredPermissions.some(permission => hasPermission(permission));

    if (!hasRequiredPermissions) {
      if (unauthorizedComponent) {
        return <>{unauthorizedComponent}</>;
      }
      if (fallback) {
        return <>{fallback}</>;
      }
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
            <p className="text-gray-600 mb-4">You don't have the required permissions to access this page.</p>
            <p className="text-sm text-gray-500">
              Required permissions: {requiredPermissions.join(requireAll ? ' AND ' : ' OR ')}
            </p>
          </div>
        </div>
      );
    }
  }

  return <>{children}</>;
};

// Conditional rendering component
interface ConditionalRenderProps {
  children: React.ReactNode;
  condition?: boolean;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  showWhenLoading?: boolean;
  showWhenUnauthenticated?: boolean;
}

export const ConditionalRender: React.FC<ConditionalRenderProps> = ({
  children,
  condition = true,
  requiredPermissions = [],
  requiredRoles = [],
  requireAll = false,
  fallback = null,
  showWhenLoading = false,
  showWhenUnauthenticated = false
}) => {
  const { 
    isAuthenticated, 
    isLoading, 
    hasPermission, 
    hasRole, 
    hasAnyRole, 
    hasAllRoles 
  } = useAuth();

  // Handle loading state
  if (isLoading) {
    return showWhenLoading ? <>{children}</> : <>{fallback}</>;
  }

  // Handle unauthenticated state
  if (!isAuthenticated) {
    return showWhenUnauthenticated ? <>{children}</> : <>{fallback}</>;
  }

  // Check custom condition
  if (!condition) {
    return <>{fallback}</>;
  }

  // Check role requirements
  if (requiredRoles.length > 0) {
    const hasRequiredRoles = requireAll 
      ? hasAllRoles(requiredRoles)
      : hasAnyRole(requiredRoles);

    if (!hasRequiredRoles) {
      return <>{fallback}</>;
    }
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const hasRequiredPermissions = requireAll
      ? requiredPermissions.every(permission => hasPermission(permission))
      : requiredPermissions.some(permission => hasPermission(permission));

    if (!hasRequiredPermissions) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
};

// Permission-based button component
interface PermissionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  requiredPermissions?: string[];
  requiredRoles?: string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const PermissionButton: React.FC<PermissionButtonProps> = ({
  requiredPermissions = [],
  requiredRoles = [],
  requireAll = false,
  fallback = null,
  children,
  ...buttonProps
}) => {
  return (
    <ConditionalRender
      requiredPermissions={requiredPermissions}
      requiredRoles={requiredRoles}
      requireAll={requireAll}
      fallback={fallback}
    >
      <button {...buttonProps}>
        {children}
      </button>
    </ConditionalRender>
  );
};

// Role-based component display
interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles: string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
}

export const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  allowedRoles,
  requireAll = false,
  fallback = null
}) => {
  return (
    <ConditionalRender
      requiredRoles={allowedRoles}
      requireAll={requireAll}
      fallback={fallback}
    >
      {children}
    </ConditionalRender>
  );
};

// Permission-based component display
interface PermissionGuardProps {
  children: React.ReactNode;
  allowedPermissions: string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  allowedPermissions,
  requireAll = false,
  fallback = null
}) => {
  return (
    <ConditionalRender
      requiredPermissions={allowedPermissions}
      requireAll={requireAll}
      fallback={fallback}
    >
      {children}
    </ConditionalRender>
  );
};

export default ProtectedRoute;