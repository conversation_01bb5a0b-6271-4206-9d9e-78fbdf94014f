import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import React from 'react';
import { AIAssistantChat } from './ai-assistant-chat';
import { TeamChat } from './team-chat';
import { 
  Send, 
  Paperclip, 
  Smile, 
  Bot,
  User,
  Phone,
  Video,
  Search,
  Settings,
  Users,
  Hash,
  Lock,
  Globe,
  Zap,
  Brain,
  MessageSquare
} from 'lucide-react';

const meta: Meta = {
  title: 'Layouts/Chat',
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Chat layout components for AI assistants and team communication.'
      }
    }
  },
  argTypes: {
    theme: {
      control: 'select',
      options: ['light', 'dark'],
      description: 'Theme variant'
    },
    showSidebar: {
      control: 'boolean',
      description: 'Show chat sidebar'
    },
    isTyping: {
      control: 'boolean',
      description: 'Show typing indicator'
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data for AI chat
const mockAIMessages = [
  {
    id: '1',
    content: 'Hello! I\'m <PERSON><PERSON>, your AI assistant. How can I help you today?',
    role: 'assistant' as const,
    timestamp: new Date(Date.now() - 300000)
  },
  {
    id: '2',
    content: 'I need help with creating a marketing strategy for my new product.',
    role: 'user' as const,
    timestamp: new Date(Date.now() - 240000)
  },
  {
    id: '3',
    content: 'I\'d be happy to help you create a marketing strategy! Let me break this down into key components:\n\n**1. Target Audience Analysis**\n- Define your ideal customer persona\n- Analyze demographics and psychographics\n- Identify pain points and needs\n\n**2. Market Research**\n- Competitive analysis\n- Market size and trends\n- Pricing strategy\n\n**3. Marketing Channels**\n- Digital marketing (social media, content marketing)\n- Traditional marketing (if applicable)\n- Influencer partnerships\n\n**4. Content Strategy**\n- Brand messaging\n- Content calendar\n- Visual identity\n\nWould you like me to dive deeper into any of these areas? What type of product are you launching?',
    role: 'assistant' as const,
    timestamp: new Date(Date.now() - 180000)
  },
  {
    id: '4',
    content: 'This is really helpful! It\'s a sustainable water bottle with smart features.',
    role: 'user' as const,
    timestamp: new Date(Date.now() - 120000)
  },
  {
    id: '5',
    content: 'Excellent! A sustainable smart water bottle has great market potential. Here\'s a tailored strategy:\n\n**Target Audience:**\n- Health-conscious millennials and Gen Z\n- Fitness enthusiasts\n- Environmentally aware consumers\n- Tech-savvy professionals\n\n**Key Messaging:**\n- \"Smart hydration for a sustainable future\"\n- Highlight both tech features and eco-benefits\n- Focus on lifestyle integration\n\n**Marketing Channels:**\n- Instagram/TikTok for visual appeal\n- Fitness and wellness blogs\n- Sustainability-focused communities\n- Tech review channels\n\n**Launch Strategy:**\n- Influencer partnerships with fitness/eco influencers\n- Crowdfunding campaign for early adopters\n- Content series showing daily use cases\n\nWould you like me to create a detailed content calendar or dive deeper into any specific channel?',
    role: 'assistant' as const,
    timestamp: new Date(Date.now() - 60000)
  }
];

// Mock data for team chat
const mockTeamMessages = [
  {
    id: '1',
    content: 'Good morning team! Ready for our sprint planning meeting?',
    user: {
      id: 'user1',
      name: 'Sarah Johnson',
      username: 'sarah.johnson',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b09e6c8b?w=32&h=32&fit=crop&crop=face',
      role: 'Product Manager',
      status: 'online' as const
    },
    timestamp: new Date(Date.now() - 1800000)
  },
  {
    id: '2',
    content: 'Yes! I\'ve prepared the user stories and acceptance criteria.',
    user: {
      id: 'user2',
      name: 'Mike Chen',
      username: 'mike.chen',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
      role: 'Developer',
      status: 'online' as const
    },
    timestamp: new Date(Date.now() - 1740000)
  },
  {
    id: '3',
    content: 'Great! I\'ve also completed the wireframes for the new dashboard feature.',
    user: {
      id: 'user3',
      name: 'Emma Davis',
      username: 'emma.davis',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face',
      role: 'Designer',
      status: 'online' as const
    },
    timestamp: new Date(Date.now() - 1680000)
  },
  {
    id: '4',
    content: 'Perfect! Should we review the wireframes before the meeting?',
    user: {
      id: 'user1',
      name: 'Sarah Johnson',
      username: 'sarah.johnson',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b09e6c8b?w=32&h=32&fit=crop&crop=face',
      role: 'Product Manager',
      status: 'online' as const
    },
    timestamp: new Date(Date.now() - 1620000)
  },
  {
    id: '5',
    content: 'I can share them in the channel. One moment...',
    user: {
      id: 'user3',
      name: 'Emma Davis',
      username: 'emma.davis',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face',
      role: 'Designer',
      status: 'online' as const
    },
    timestamp: new Date(Date.now() - 1560000)
  }
];

const mockChannels = [
  {
    id: 'general',
    name: 'general',
    type: 'public' as const,
    unreadCount: 3,
    lastMessage: 'Ready for our sprint planning meeting?',
    lastMessageTime: new Date(Date.now() - 1800000)
  },
  {
    id: 'development',
    name: 'development',
    type: 'public' as const,
    unreadCount: 0,
    lastMessage: 'Fixed the bug in the authentication flow',
    lastMessageTime: new Date(Date.now() - 3600000)
  },
  {
    id: 'design',
    name: 'design',
    type: 'public' as const,
    unreadCount: 1,
    lastMessage: 'New design system components ready',
    lastMessageTime: new Date(Date.now() - 7200000)
  },
  {
    id: 'project-alpha',
    name: 'project-alpha',
    type: 'private' as const,
    unreadCount: 0,
    lastMessage: 'Timeline looks good for next week',
    lastMessageTime: new Date(Date.now() - 14400000)
  }
];

const mockDirectMessages = [
  {
    id: 'dm-mike',
    user: {
      id: 'user2',
      name: 'Mike Chen',
      username: 'mike.chen',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
      status: 'online' as const,
      role: 'Developer'
    },
    unreadCount: 2,
    lastMessage: 'Can you review my pull request?',
    isOnline: true
  },
  {
    id: 'dm-emma',
    user: {
      id: 'user3',
      name: 'Emma Davis',
      username: 'emma.davis',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face',
      status: 'offline' as const,
      role: 'Designer'
    },
    unreadCount: 0,
    lastMessage: 'Thanks for the feedback!',
    isOnline: false
  }
];

export const AIAssistantChatDemo: Story = {
  args: {
    isTyping: false,
    theme: 'light'
  },
  render: (args) => (
    <AIAssistantChat
      messages={mockAIMessages}
      isTyping={args.isTyping}
      onSendMessage={(message) => console.log('Send message:', message)}
      onUploadFile={(file) => console.log('Upload file:', file)}
      aiName="AMNA"
      aiAvatar={<Bot className="w-6 h-6" />}
      userAvatar={<User className="w-6 h-6" />}
      placeholder="Ask AMNA anything..."
      suggestions={[
        'Help me write a marketing email',
        'Create a project timeline',
        'Analyze this data',
        'Suggest improvements for my code'
      ]}
      capabilities={[
        { 
          icon: <Brain className="w-4 h-4" />, 
          title: 'Smart Analysis', 
          description: 'Analyze data and provide insights' 
        },
        { 
          icon: <MessageSquare className="w-4 h-4" />, 
          title: 'Content Creation', 
          description: 'Write emails, documents, and more' 
        },
        { 
          icon: <Zap className="w-4 h-4" />, 
          title: 'Quick Actions', 
          description: 'Automate repetitive tasks' 
        }
      ]}
    />
  )
};

export const AIAssistantTyping: Story = {
  args: {
    isTyping: true,
    theme: 'light'
  },
  render: (args) => (
    <AIAssistantChat
      messages={mockAIMessages}
      isTyping={args.isTyping}
      onSendMessage={(message) => console.log('Send message:', message)}
      onUploadFile={(file) => console.log('Upload file:', file)}
      aiName="AMNA"
      aiAvatar={<Bot className="w-6 h-6" />}
      userAvatar={<User className="w-6 h-6" />}
      placeholder="Ask AMNA anything..."
      suggestions={[
        'Help me write a marketing email',
        'Create a project timeline',
        'Analyze this data',
        'Suggest improvements for my code'
      ]}
      capabilities={[
        { 
          icon: <Brain className="w-4 h-4" />, 
          title: 'Smart Analysis', 
          description: 'Analyze data and provide insights' 
        },
        { 
          icon: <MessageSquare className="w-4 h-4" />, 
          title: 'Content Creation', 
          description: 'Write emails, documents, and more' 
        },
        { 
          icon: <Zap className="w-4 h-4" />, 
          title: 'Quick Actions', 
          description: 'Automate repetitive tasks' 
        }
      ]}
    />
  )
};

export const TeamChatDemo: Story = {
  args: {
    showSidebar: true,
    theme: 'light'
  },
  render: (args) => (
    <TeamChat
      messages={mockTeamMessages}
      channels={mockChannels}
      directMessages={mockDirectMessages}
      currentChannel="general"
      currentUser={{
        id: 'currentUser',
        name: 'You',
        username: 'you',
        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=32&h=32&fit=crop&crop=face',
        role: 'Team Lead',
        status: 'online' as const
      }}
      showSidebar={args.showSidebar}
      onSendMessage={(message) => console.log('Send message:', message)}
      onChannelSelect={(channelId) => console.log('Select channel:', channelId)}
      onDirectMessageSelect={(userId) => console.log('Select DM:', userId)}
      onToggleSidebar={() => console.log('Toggle sidebar')}
      onUploadFile={(file) => console.log('Upload file:', file)}
      onStartCall={(type) => console.log('Start call:', type)}
      onSearchMessages={(query) => console.log('Search:', query)}
      teamName="Product Team"
      teamAvatar={<Users className="w-6 h-6" />}
    />
  )
};

export const TeamChatCollapsed: Story = {
  args: {
    showSidebar: false,
    theme: 'light'
  },
  render: (args) => (
    <TeamChat
      messages={mockTeamMessages}
      channels={mockChannels}
      directMessages={mockDirectMessages}
      currentChannel="development"
      currentUser={{
        id: 'currentUser',
        name: 'You',
        username: 'you',
        avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=32&h=32&fit=crop&crop=face',
        role: 'Team Lead',
        status: 'online' as const
      }}
      showSidebar={args.showSidebar}
      onSendMessage={(message) => console.log('Send message:', message)}
      onChannelSelect={(channelId) => console.log('Select channel:', channelId)}
      onDirectMessageSelect={(userId) => console.log('Select DM:', userId)}
      onToggleSidebar={() => console.log('Toggle sidebar')}
      onUploadFile={(file) => console.log('Upload file:', file)}
      onStartCall={(type) => console.log('Start call:', type)}
      onSearchMessages={(query) => console.log('Search:', query)}
      teamName="Product Team"
      teamAvatar={<Users className="w-6 h-6" />}
    />
  )
};

export const EmptyStates: Story = {
  render: () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">AI Assistant - First Visit</h3>
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden h-[400px]">
          <AIAssistantChat
            messages={[]}
            isTyping={false}
            onSendMessage={(message) => console.log('Send message:', message)}
            onUploadFile={(file) => console.log('Upload file:', file)}
            aiName="AMNA"
            aiAvatar={<Bot className="w-6 h-6" />}
            userAvatar={<User className="w-6 h-6" />}
            placeholder="Ask AMNA anything..."
            suggestions={[
              'Help me write a marketing email',
              'Create a project timeline',
              'Analyze this data',
              'Suggest improvements for my code'
            ]}
            capabilities={[
              { 
                icon: <Brain className="w-4 h-4" />, 
                title: 'Smart Analysis', 
                description: 'Analyze data and provide insights' 
              },
              { 
                icon: <MessageSquare className="w-4 h-4" />, 
                title: 'Content Creation', 
                description: 'Write emails, documents, and more' 
              },
              { 
                icon: <Zap className="w-4 h-4" />, 
                title: 'Quick Actions', 
                description: 'Automate repetitive tasks' 
              }
            ]}
          />
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Team Chat - Empty Channel</h3>
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden h-[400px]">
          <TeamChat
            messages={[]}
            channels={mockChannels}
            directMessages={mockDirectMessages}
            currentChannel="general"
            currentUser={{
              id: 'currentUser',
              name: 'You',
              avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=32&h=32&fit=crop&crop=face',
              role: 'Team Lead'
            }}
            showSidebar={true}
            onSendMessage={(message) => console.log('Send message:', message)}
            onChannelSelect={(channelId) => console.log('Select channel:', channelId)}
            onDirectMessageSelect={(userId) => console.log('Select DM:', userId)}
            onToggleSidebar={() => console.log('Toggle sidebar')}
            onUploadFile={(file) => console.log('Upload file:', file)}
            onStartCall={(type) => console.log('Start call:', type)}
            onSearchMessages={(query) => console.log('Search:', query)}
            teamName="Product Team"
            teamAvatar={<Users className="w-6 h-6" />}
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: 'Empty state examples showing how the chat layouts appear when there are no messages.'
      }
    }
  }
};

export const MobileResponsive: Story = {
  render: () => (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Mobile Responsive Chat Layouts
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Chat layouts automatically adapt to mobile screens
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">AI Assistant - Mobile</h3>
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden mx-auto" style={{ width: '375px', height: '600px' }}>
            <AIAssistantChat
              messages={mockAIMessages.slice(0, 3)}
              isTyping={false}
              onSendMessage={(message) => console.log('Send message:', message)}
              onUploadFile={(file) => console.log('Upload file:', file)}
              aiName="AMNA"
              aiAvatar={<Bot className="w-6 h-6" />}
              userAvatar={<User className="w-6 h-6" />}
              placeholder="Ask AMNA anything..."
              suggestions={[
                'Help me write a marketing email',
                'Create a project timeline'
              ]}
              capabilities={[
                { 
                  icon: <Brain className="w-4 h-4" />, 
                  title: 'Smart Analysis', 
                  description: 'Analyze data and provide insights' 
                },
                { 
                  icon: <MessageSquare className="w-4 h-4" />, 
                  title: 'Content Creation', 
                  description: 'Write emails, documents, and more' 
                }
              ]}
            />
          </div>
        </div>
        
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Team Chat - Mobile</h3>
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden mx-auto" style={{ width: '375px', height: '600px' }}>
            <TeamChat
              messages={mockTeamMessages.slice(0, 3)}
              channels={mockChannels}
              directMessages={mockDirectMessages}
              currentChannel="general"
              currentUser={{
                id: 'currentUser',
                name: 'You',
                avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=32&h=32&fit=crop&crop=face',
                role: 'Team Lead'
              }}
              showSidebar={false}
              onSendMessage={(message) => console.log('Send message:', message)}
              onChannelSelect={(channelId) => console.log('Select channel:', channelId)}
              onDirectMessageSelect={(userId) => console.log('Select DM:', userId)}
              onToggleSidebar={() => console.log('Toggle sidebar')}
              onUploadFile={(file) => console.log('Upload file:', file)}
              onStartCall={(type) => console.log('Start call:', type)}
              onSearchMessages={(query) => console.log('Search:', query)}
              teamName="Product Team"
              teamAvatar={<Users className="w-6 h-6" />}
            />
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Mobile responsive versions of the chat layouts, optimized for smaller screens.'
      }
    }
  }
};