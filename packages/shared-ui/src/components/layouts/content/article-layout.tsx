import { ReactNode, useState } from "react";
import { motion } from "framer-motion";
import {
  User,
  Calendar,
  Tag,
  Share2,
  Bookmark,
  Heart,
  MessageCircle,
  Eye,
  ArrowLeft,
  ChevronUp,
  Facebook,
  Twitter,
  Linkedin,
  Link,
  Search,
  Filter,
  ArrowRight,
  Clock
} from "lucide-react";

import { LuminarCard } from '../../ui/display';
import { Button } from '../../ui/actions';
import { LuminarInput } from '../../ui/forms';
import { LuminarBadge, LuminarAvatar } from '../../ui/display';
import { ThemeToggle } from '../../ui/utilities';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface ArticleLayoutProps {
  className?: string;
  article?: Article;
  author?: Author;
  sidebar?: ReactNode;
  showSidebar?: boolean;
  relatedArticles?: RelatedArticle[];
  comments?: Comment[];
  showComments?: boolean;
  onShare?: (platform: string) => void;
  onLike?: () => void;
  onBookmark?: () => void;
  onCommentSubmit?: (content: string) => void;
  loading?: boolean;
  showTableOfContents?: boolean;
  tableOfContents?: TOCItem[];
  showProgress?: boolean;
  breadcrumbs?: Breadcrumb[];
}

export interface Article {
  id: string;
  title: string;
  content: ReactNode;
  excerpt?: string;
  publishedAt: Date;
  updatedAt?: Date;
  readTime: number;
  views: number;
  likes: number;
  bookmarks: number;
  isLiked?: boolean;
  isBookmarked?: boolean;
  tags: string[];
  category: string;
  featured?: boolean;
  coverImage?: string;
  seo?: {
    description: string;
    keywords: string[];
  };
}

export interface Author {
  id: string;
  name: string;
  avatar?: string;
  bio?: string;
  role?: string;
  socialLinks?: SocialLink[];
  articlesCount?: number;
  followers?: number;
}

export interface SocialLink {
  platform: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
}

export interface RelatedArticle {
  id: string;
  title: string;
  excerpt: string;
  coverImage?: string;
  publishedAt: Date;
  readTime: number;
  author: {
    name: string;
    avatar?: string;
  };
}

export interface Comment {
  id: string;
  author: {
    name: string;
    avatar?: string;
  };
  content: string;
  publishedAt: Date;
  likes: number;
  replies?: Comment[];
}

export interface TOCItem {
  id: string;
  title: string;
  level: number;
  children?: TOCItem[];
}

export interface Breadcrumb {
  label: string;
  href?: string;
  onClick?: () => void;
}

export function ArticleLayout({
  className,
  article,
  author,
  sidebar,
  showSidebar = true,
  relatedArticles = [],
  comments = [],
  showComments = true,
  onShare,
  onLike,
  onBookmark,
  onCommentSubmit,
  loading = false,
  showTableOfContents = true,
  tableOfContents = [],
  showProgress = true,
  breadcrumbs = [],
  ...props
}: ArticleLayoutProps) {
  const [newComment, setNewComment] = useState("");
  const [readingProgress, setReadingProgress] = useState(0);
  const [showShareMenu, setShowShareMenu] = useState(false);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  };

  return (
    <div className={cn(
      "min-h-screen bg-gray-50 dark:bg-gray-900",
      className
    )} {...props}>
      
      {/* Reading Progress Bar */}
      {showProgress && (
        <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 dark:bg-gray-800 z-50">
          <motion.div
            className="h-full bg-blue-500"
            style={{ width: `${readingProgress}%` }}
            transition={{ duration: 0.1 }}
          />
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Breadcrumbs */}
        {breadcrumbs.length > 0 && (
          <nav className="flex items-center gap-2 text-sm mb-6">
            {breadcrumbs.map((crumb, index) => (
              <div key={index} className="flex items-center gap-2">
                {index > 0 && <span className="text-gray-400">/</span>}
                <button
                  onClick={crumb.onClick}
                  className={cn(
                    "hover:text-blue-600 dark:hover:text-blue-400",
                    index === breadcrumbs.length - 1
                      ? "text-gray-900 dark:text-white font-medium"
                      : "text-gray-600 dark:text-gray-400"
                  )}
                >
                  {crumb.label}
                </button>
              </div>
            ))}
          </nav>
        )}

        <div className="flex gap-8">
          {/* Table of Contents - Left Sidebar */}
          {showTableOfContents && tableOfContents.length > 0 && (
            <aside className="hidden xl:block w-64 sticky top-8 h-fit">
              <LuminarCard className="p-6">
                <h3 className="font-semibold mb-4 text-gray-900 dark:text-white">
                  Table of Contents
                </h3>
                <nav className="space-y-2">
                  {tableOfContents.map((item) => (
                    <a
                      key={item.id}
                      href={`#${item.id}`}
                      className={cn(
                        "block text-sm hover:text-blue-600 dark:hover:text-blue-400 transition-colors",
                        `ml-${item.level * 4}`,
                        "text-gray-600 dark:text-gray-400"
                      )}
                    >
                      {item.title}
                    </a>
                  ))}
                </nav>
              </LuminarCard>
            </aside>
          )}

          {/* Main Content */}
          <main className="flex-1 max-w-4xl">
            {article && (
              <article>
                {/* Article Header */}
                <header className="mb-8">
                  {article.coverImage && (
                    <img
                      src={article.coverImage}
                      alt={article.title}
                      className="w-full h-64 object-cover rounded-lg mb-6"
                    />
                  )}

                  <div className="space-y-4">
                    {/* Category & Tags */}
                    <div className="flex items-center gap-2 flex-wrap">
                      <LuminarBadge variant="default">
                        {article.category}
                      </LuminarBadge>
                      {article.tags.map((tag) => (
                        <LuminarBadge key={tag} variant="info" size="sm">
                          <Tag className="w-3 h-3 mr-1" />
                          {tag}
                        </LuminarBadge>
                      ))}
                    </div>

                    {/* Title */}
                    <h1 className="text-4xl font-bold text-gray-900 dark:text-white leading-tight">
                      {article.title}
                    </h1>

                    {/* Excerpt */}
                    {article.excerpt && (
                      <p className="text-xl text-gray-600 dark:text-gray-400 leading-relaxed">
                        {article.excerpt}
                      </p>
                    )}

                    {/* Article Meta */}
                    <div className="flex items-center justify-between py-4 border-y border-gray-200 dark:border-gray-700">
                      <div className="flex items-center gap-6">
                        {/* Author */}
                        {author && (
                          <div className="flex items-center gap-3">
                            <LuminarAvatar
                              src={author.avatar}
                              alt={author.name}
                              size="md"
                              fallback={author.name.slice(0, 2)}
                            />
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">
                                {author.name}
                              </p>
                              {author.role && (
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                  {author.role}
                                </p>
                              )}
                            </div>
                          </div>
                        )}

                        {/* Publication Info */}
                        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <span className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {formatDate(article.publishedAt)}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            {article.readTime} min read
                          </span>
                          <span className="flex items-center gap-1">
                            <Eye className="w-4 h-4" />
                            {formatNumber(article.views)} views
                          </span>
                        </div>
                      </div>

                      {/* Social Actions */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={onLike}
                          className={cn(
                            "gap-2",
                            article.isLiked && "text-red-500"
                          )}
                        >
                          <Heart className={cn("w-4 h-4", article.isLiked && "fill-current")} />
                          {formatNumber(article.likes)}
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={onBookmark}
                          className={cn(
                            "gap-2",
                            article.isBookmarked && "text-blue-500"
                          )}
                        >
                          <Bookmark className={cn("w-4 h-4", article.isBookmarked && "fill-current")} />
                          {formatNumber(article.bookmarks)}
                        </Button>

                        <div className="relative">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setShowShareMenu(!showShareMenu)}
                          >
                            <Share2 className="w-4 h-4" />
                          </Button>

                          {showShareMenu && (
                            <motion.div
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="absolute top-full right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10 p-2"
                            >
                              <div className="flex gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => onShare?.('twitter')}
                                >
                                  <Twitter className="w-4 h-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => onShare?.('facebook')}
                                >
                                  <Facebook className="w-4 h-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => onShare?.('linkedin')}
                                >
                                  <Linkedin className="w-4 h-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => onShare?.('copy')}
                                >
                                  <Link className="w-4 h-4" />
                                </Button>
                              </div>
                            </motion.div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </header>

                {/* Article Content */}
                <div className="prose prose-lg max-w-none dark:prose-invert prose-headings:font-bold prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-img:rounded-lg">
                  {article.content}
                </div>

                {/* Article Footer */}
                <footer className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
                  {/* Author Bio */}
                  {author && author.bio && (
                    <LuminarCard className="p-6 mb-8">
                      <div className="flex gap-4">
                        <LuminarAvatar
                          src={author.avatar}
                          alt={author.name}
                          size="lg"
                          fallback={author.name.slice(0, 2)}
                        />
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                            About {author.name}
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400 mb-3">
                            {author.bio}
                          </p>
                          <div className="flex items-center gap-4">
                            {author.socialLinks && (
                              <div className="flex gap-2">
                                {author.socialLinks.map((link) => (
                                  <Button
                                    key={link.platform}
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => window.open(link.url, '_blank')}
                                  >
                                    <link.icon className="w-4 h-4" />
                                  </Button>
                                ))}
                              </div>
                            )}
                            {author.articlesCount && (
                              <span className="text-sm text-gray-500">
                                {author.articlesCount} articles
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </LuminarCard>
                  )}

                  {/* Related Articles */}
                  {relatedArticles.length > 0 && (
                    <section className="mb-12">
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                        Related Articles
                      </h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {relatedArticles.map((relatedArticle) => (
                          <LuminarCard key={relatedArticle.id} className="hover:shadow-lg transition-shadow">
                            {relatedArticle.coverImage && (
                              <img
                                src={relatedArticle.coverImage}
                                alt={relatedArticle.title}
                                className="w-full h-32 object-cover rounded-t-lg"
                              />
                            )}
                            <div className="p-4">
                              <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                                {relatedArticle.title}
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                                {relatedArticle.excerpt}
                              </p>
                              <div className="flex items-center justify-between text-xs text-gray-500">
                                <div className="flex items-center gap-2">
                                  <LuminarAvatar
                                    src={relatedArticle.author.avatar}
                                    alt={relatedArticle.author.name}
                                    size="sm"
                                    fallback={relatedArticle.author.name.charAt(0)}
                                  />
                                  <span>{relatedArticle.author.name}</span>
                                </div>
                                <span>{relatedArticle.readTime} min read</span>
                              </div>
                            </div>
                          </LuminarCard>
                        ))}
                      </div>
                    </section>
                  )}

                  {/* Comments */}
                  {showComments && (
                    <section>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                        Comments ({comments.length})
                      </h2>

                      {/* Comment Form */}
                      <LuminarCard className="p-6 mb-6">
                        <div className="space-y-4">
                          <textarea
                            value={newComment}
                            onChange={(e) => setNewComment(e.target.value)}
                            placeholder="Share your thoughts..."
                            rows={4}
                            className="w-full p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                          <div className="flex justify-end">
                            <Button
                              onClick={() => {
                                onCommentSubmit?.(newComment);
                                setNewComment("");
                              }}
                              disabled={!newComment.trim()}
                            >
                              Post Comment
                            </Button>
                          </div>
                        </div>
                      </LuminarCard>

                      {/* Comments List */}
                      <div className="space-y-6">
                        {comments.map((comment) => (
                          <div key={comment.id} className="flex gap-4">
                            <LuminarAvatar
                              src={comment.author.avatar}
                              alt={comment.author.name}
                              size="md"
                              fallback={comment.author.name.charAt(0)}
                            />
                            <div className="flex-1">
                              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="font-medium text-gray-900 dark:text-white">
                                    {comment.author.name}
                                  </span>
                                  <span className="text-sm text-gray-500">
                                    {formatDate(comment.publishedAt)}
                                  </span>
                                </div>
                                <p className="text-gray-700 dark:text-gray-300">
                                  {comment.content}
                                </p>
                              </div>
                              <div className="mt-2 flex items-center gap-4">
                                <Button variant="ghost" size="sm">
                                  <Heart className="w-4 h-4 mr-1" />
                                  {comment.likes}
                                </Button>
                                <Button variant="ghost" size="sm">
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </section>
                  )}
                </footer>
              </article>
            )}
          </main>

          {/* Right Sidebar */}
          {showSidebar && (
            <aside className="hidden lg:block w-80 sticky top-8 h-fit">
              {sidebar || (
                <div className="space-y-6">
                  {/* Popular Articles */}
                  <LuminarCard className="p-6">
                    <h3 className="font-semibold mb-4 text-gray-900 dark:text-white">
                      Popular Articles
                    </h3>
                    <div className="space-y-4">
                      {relatedArticles.slice(0, 3).map((article) => (
                        <div key={article.id} className="flex gap-3">
                          {article.coverImage && (
                            <img
                              src={article.coverImage}
                              alt=""
                              className="w-16 h-16 object-cover rounded"
                            />
                          )}
                          <div className="flex-1">
                            <h4 className="font-medium text-sm line-clamp-2 text-gray-900 dark:text-white">
                              {article.title}
                            </h4>
                            <p className="text-xs text-gray-500 mt-1">
                              {article.readTime} min read
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </LuminarCard>

                  {/* Newsletter Signup */}
                  <LuminarCard className="p-6">
                    <h3 className="font-semibold mb-2 text-gray-900 dark:text-white">
                      Stay Updated
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      Get the latest articles delivered to your inbox.
                    </p>
                    <div className="space-y-3">
                      <LuminarInput
                        placeholder="Enter your email"
                        type="email"
                        size="sm"
                      />
                      <Button className="w-full" size="sm">
                      </Button>
                    </div>
                  </LuminarCard>
                </div>
              )}
            </aside>
          )}
        </div>
      </div>

      {/* Floating Actions */}
      <div className="fixed bottom-6 right-6 flex flex-col gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className="rounded-full p-2"
        >
          <ChevronUp className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}