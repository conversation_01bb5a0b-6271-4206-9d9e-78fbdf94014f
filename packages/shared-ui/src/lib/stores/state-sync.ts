import React from 'react';
import { useCrossAppStore } from './cross-app.store';
import { useAuthStore } from './auth.store';

// Cross-app state synchronization
export interface StateSyncMessage {
  type: 'state-sync';
  sourceApp: string;
  targetApp: string;
  data: any;
  timestamp: number;
}

export interface StateSyncConfig {
  appName: string;
  enableBroadcast?: boolean;
  enableStorage?: boolean;
  syncInterval?: number;
  debugMode?: boolean;
}

// State sync manager
export class StateSyncManager {
  private config: StateSyncConfig;
  private syncInterval: NodeJS.Timeout | null = null;
  private messageHandlers: Map<string, (data: any) => void> = new Map();
  private broadcastChannel: BroadcastChannel | null = null;
  
  constructor(config: StateSyncConfig) {
    this.config = {
      enableBroadcast: true,
      enableStorage: true,
      syncInterval: 5000,
      debugMode: false,
      ...config,
    };
    
    this.init();
  }
  
  private init(): void {
    // Initialize BroadcastChannel if supported
    if (this.config.enableBroadcast && typeof BroadcastChannel !== 'undefined') {
      this.broadcastChannel = new BroadcastChannel('luminar-state-sync');
      this.broadcastChannel.onmessage = this.handleBroadcastMessage.bind(this);
    }
    
    // Initialize storage event listener
    if (this.config.enableStorage) {
      window.addEventListener('storage', this.handleStorageChange.bind(this));
    }
    
    // Initialize periodic sync
    if (this.config.syncInterval && this.config.syncInterval > 0) {
      this.syncInterval = setInterval(() => {
        this.performPeriodicSync();
      }, this.config.syncInterval);
    }
    
    // Initialize custom event listeners
    window.addEventListener('cross-app-sync', this.handleCustomEvent.bind(this) as EventListener);
    
    this.log('State sync manager initialized');
  }
  
  private handleBroadcastMessage(event: MessageEvent): void {
    try {
      const message: StateSyncMessage = event.data;
      
      if (message.type === 'state-sync' && message.targetApp === this.config.appName) {
        this.log('Received broadcast message:', message);
        this.processMessage(message);
      }
    } catch (error) {
      console.error('Error handling broadcast message:', error);
    }
  }
  
  private handleStorageChange(event: StorageEvent): void {
    if (event.key?.startsWith('luminar_state_sync_')) {
      try {
        const message: StateSyncMessage = JSON.parse(event.newValue || '{}');
        
        if (message.targetApp === this.config.appName) {
          this.log('Received storage message:', message);
          this.processMessage(message);
        }
      } catch (error) {
        console.error('Error handling storage change:', error);
      }
    }
  }
  
  private handleCustomEvent(event: CustomEvent): void {
    this.log('Received custom sync event:', event.detail);
    // Trigger a state sync
    this.syncWithOtherApps();
  }
  
  private processMessage(message: StateSyncMessage): void {
    const handler = this.messageHandlers.get(message.data.type);
    if (handler) {
      handler(message.data);
    } else {
      this.log('No handler for message type:', message.data.type);
    }
  }
  
  private performPeriodicSync(): void {
    // Sync cross-app state
    this.syncWithOtherApps();
    
    // Update last activity
    const crossAppStore = useCrossAppStore.getState();
    crossAppStore.updateLastActivity();
  }
  
  private syncWithOtherApps(): void {
    const crossAppStore = useCrossAppStore.getState();
    const authStore = useAuthStore.getState();
    
    // Prepare sync data
    const syncData = {
      theme: crossAppStore.theme,
      settings: crossAppStore.settings,
      notificationPreferences: crossAppStore.notificationPreferences,
      user: authStore.user,
      isAuthenticated: authStore.isAuthenticated,
      lastActivity: crossAppStore.lastActivity,
    };
    
    // Broadcast to other apps
    this.broadcastState('global-sync', syncData);
  }
  
  public broadcastState(type: string, data: any, targetApp?: string): void {
    const message: StateSyncMessage = {
      type: 'state-sync',
      sourceApp: this.config.appName,
      targetApp: targetApp || 'all',
      data: { type, ...data },
      timestamp: Date.now(),
    };
    
    // Send via BroadcastChannel
    if (this.broadcastChannel) {
      this.broadcastChannel.postMessage(message);
    }
    
    // Send via storage (fallback)
    if (this.config.enableStorage) {
      const storageKey = `luminar_state_sync_${Date.now()}`;
      localStorage.setItem(storageKey, JSON.stringify(message));
      
      // Clean up after a short delay
      setTimeout(() => {
        localStorage.removeItem(storageKey);
      }, 1000);
    }
    
    this.log('Broadcasted state:', message);
  }
  
  public onMessage(type: string, handler: (data: any) => void): void {
    this.messageHandlers.set(type, handler);
  }
  
  public offMessage(type: string): void {
    this.messageHandlers.delete(type);
  }
  
  public destroy(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    if (this.broadcastChannel) {
      this.broadcastChannel.close();
    }
    
    window.removeEventListener('storage', this.handleStorageChange);
    window.removeEventListener('cross-app-sync', this.handleCustomEvent.bind(this) as EventListener);
    
    this.messageHandlers.clear();
    
    this.log('State sync manager destroyed');
  }
  
  private log(message: string, ...args: any[]): void {
    if (this.config.debugMode) {
      console.log(`[StateSync:${this.config.appName}] ${message}`, ...args);
    }
  }
}

// React hook for state sync
export const useStateSync = (config: StateSyncConfig) => {
  const manager = React.useMemo(() => new StateSyncManager(config), [config.appName]);
  
  React.useEffect(() => {
    return () => {
      manager.destroy();
    };
  }, [manager]);
  
  return {
    broadcastState: manager.broadcastState.bind(manager),
    onMessage: manager.onMessage.bind(manager),
    offMessage: manager.offMessage.bind(manager),
  };
};

// Utility functions
export const syncUserAuthentication = (appName: string): void => {
  const manager = new StateSyncManager({ appName });
  
  // Listen for auth changes
  useAuthStore.subscribe((state) => {
    const authState = { user: state.user, isAuthenticated: state.isAuthenticated };
    manager.broadcastState('auth-changed', authState);
  });
  
  // Handle auth sync messages
  manager.onMessage('auth-changed', (data) => {
    const authStore = useAuthStore.getState();
    if (data.isAuthenticated && !authStore.isAuthenticated) {
      // User authenticated in another app
      authStore.updateUser(data.user);
    } else if (!data.isAuthenticated && authStore.isAuthenticated) {
      // User logged out in another app
      authStore.logout();
    }
  });
};

export const syncThemeSettings = (appName: string): void => {
  const manager = new StateSyncManager({ appName });
  
  // Listen for theme changes
  useCrossAppStore.subscribe(
    (state) => state.theme,
    (theme) => {
      manager.broadcastState('theme-changed', { theme });
    }
  );
  
  // Handle theme sync messages
  manager.onMessage('theme-changed', (data) => {
    const crossAppStore = useCrossAppStore.getState();
    crossAppStore.setTheme(data.theme);
  });
};

export const syncNotifications = (appName: string): void => {
  const manager = new StateSyncManager({ appName });
  
  // Listen for notification changes
  useCrossAppStore.subscribe(
    (state) => state.notifications,
    (notifications) => {
      manager.broadcastState('notifications-changed', { notifications });
    }
  );
  
  // Handle notification sync messages
  manager.onMessage('notifications-changed', (data) => {
    // Sync notifications between apps
    const crossAppStore = useCrossAppStore.getState();
    // Only sync if this app doesn't have newer notifications
    const localNotifications = crossAppStore.notifications;
    const remoteNotifications = data.notifications;
    
    if (remoteNotifications.length > localNotifications.length) {
      // There are new notifications in another app
      const newNotifications = remoteNotifications.filter(
        (remote: any) => !localNotifications.find(local => local.id === remote.id)
      );
      
      newNotifications.forEach((notification: any) => {
        crossAppStore.addNotification(notification);
      });
    }
  });
};

// Initialize sync for all stores
export const initializeStateSyncForApp = (appName: string): StateSyncManager => {
  const manager = new StateSyncManager({ 
    appName, 
    debugMode: import.meta.env.DEV 
  });
  
  // Set up all sync handlers
  syncUserAuthentication(appName);
  syncThemeSettings(appName);
  syncNotifications(appName);
  
  return manager;
};