import { motion, AnimatePresence } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, SelectHTMLAttributes, useState, useRef, useEffect } from "react"
import { ChevronDown } from "lucide-react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface SelectOption {
  value: string
  label: string
  disabled?: boolean
}

export interface LuminarSelectProps extends Omit<SelectHTMLAttributes<HTMLSelectElement>, 'size'> {
  label?: string
  options: SelectOption[]
  placeholder?: string
  glass?: boolean
  error?: string
}

const LuminarSelect = forwardRef<HTMLSelectElement, LuminarSelectProps>(
  ({ 
    label, 
    options,
    placeholder = "Select an option",
    glass = true,
    error,
    value,
    onChange,
    className,
    ...props 
  }, ref) => {
    const [open, setIsOpen] = useState(false)
    const [selectedValue, setSelectedValue] = useState(value || "")
    const dropdownRef = useRef<HTMLDivElement>(null)

    // Add validation to prevent the error
    if (!options || !Array.isArray(options)) {
      console.error('LuminarSelect: options prop is required and must be an array', { options });
      return null;
    }
    
    const selectedOption = options.find(opt => opt.value === selectedValue)

    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
          setIsOpen(false)
        }
      }

      document.addEventListener("mousedown", handleClickOutside)
      return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [])

    const handleSelect = (option: SelectOption) => {
      if (option.disabled) return
      
      setSelectedValue(option.value)
      setIsOpen(false)
      
      // Create a synthetic event for the hidden select element
      const selectElement = ref as React.MutableRefObject<HTMLSelectElement>
      if (selectElement?.current) {
        const event = new Event('change', { bubbles: true })
        selectElement.current.value = option.value
        selectElement.current.dispatchEvent(event)
      }
      
      // Call onChange if provided
      if (onChange) {
        const syntheticEvent = {
          target: { value: option.value },
          currentTarget: { value: option.value }
        } as React.ChangeEvent<HTMLSelectElement>
        onChange(syntheticEvent)
      }
    }

    return (
      <div className="w-full">
        {label && (
          <motion.label
            className="block text-sm font-medium mb-2"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {label}
          </motion.label>
        )}
        
        <div className="relative" ref={dropdownRef}>
          <motion.div
            className={cn(
              "relative rounded-lg cursor-pointer",
              glass && "backdrop-blur-md bg-white/10 dark:bg-gray-900/10",
              "border transition-all duration-300",
              open 
                ? "border-primary shadow-[0_0_0_2px_rgba(var(--primary),0.2)]" 
                : error 
                  ? "border-destructive" 
                  : "border-white/20 dark:border-gray-700/30",
              className
            )}
            onClick={() => setIsOpen(!open)}
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
          >
            <div className="flex items-center justify-between px-4 py-3">
              <span className={cn(!selectedOption && "text-muted-foreground")}>
                {selectedOption ? selectedOption.label : placeholder}
              </span>
              <motion.div
                animate={{ rotate: open ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <ChevronDown className="w-4 h-4 text-muted-foreground" />
              </motion.div>
            </div>
          </motion.div>

          <AnimatePresence>
            {open && (
              <motion.div
                className={cn(
                  "absolute z-50 w-full mt-2 rounded-lg overflow-hidden",
                  glass && "backdrop-blur-xl bg-white/90 dark:bg-gray-900/90",
                  "border border-white/20 dark:border-gray-700/30",
                  "shadow-xl"
                )}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                <div className="max-h-60 overflow-y-auto">
                  {options.map((option) => (
                    <motion.div
                      key={option.value}
                      className={cn(
                        "px-4 py-3 cursor-pointer transition-colors",
                        "hover:bg-white/10 dark:hover:bg-gray-700/20",
                        option.disabled && "opacity-50 cursor-not-allowed",
                        selectedValue === option.value && "bg-primary/20 text-primary"
                      )}
                      onClick={() => handleSelect(option)}
                      whileHover={{ x: 4 }}
                      transition={{ duration: 0.1 }}
                    >
                      {option.label}
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Hidden select element for form compatibility */}
          <select
            ref={ref}
            value={selectedValue}
            onChange={() => {}} // Handled by custom dropdown
            className="sr-only"
            {...props}
          >
            <option value="">{placeholder}</option>
            {options.map(option => (
              <option key={option.value} value={option.value} disabled={option.disabled}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        
        {error && (
          <motion.p
            className="mt-2 text-sm text-destructive"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {error}
          </motion.p>
        )}
      </div>
    )
  }
)
LuminarSelect.displayName = "LuminarSelect"

export { LuminarSelect }