import { motion, useSpring, useTransform } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes, useEffect, useState } from "react"
import { Minus, Plus } from "lucide-react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface LuminarCounterProps extends Omit<HTMLAttributes<HTMLDivElement>, 'onAnimationStart' | 'onAnimationEnd' | 'onAnimationIteration' | 'onDrag' | 'onDragEnd' | 'onDragStart'> {
  value: number
  onIncrement?: () => void
  onDecrement?: () => void
  min?: number
  max?: number
  step?: number
  showControls?: boolean
  animated?: boolean
  duration?: number
  decimals?: number
  prefix?: string
  suffix?: string
  separator?: string
  label?: string
  size?: ComponentSize | 'md' | 'lg'
  variant?: ComponentVariant | 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  glass?: boolean
}

const LuminarCounter = forwardRef<HTMLDivElement, LuminarCounterProps>(
  ({
    value,
    onIncrement,
    onDecrement,
    min = -Infinity,
    max = Infinity,
    step = 1,
    showControls = false,
    animated = false,
    duration = 1,
    decimals = 0,
    prefix = "",
    suffix = "",
    separator = ",",
    label,
    size = 'md',
    variant = 'default',
    glass = false,
    className,
    ...props
  }, ref) => {
    const [isClient, setIsClient] = useState(false)
    
    useEffect(() => {
      setIsClient(true)
    }, [])

    const springValue = useSpring(0, {
      duration: duration * 1000,
      bounce: 0.25,
    })

    const displayValue = useTransform(springValue, (latest) =>
      formatNumber(latest, decimals, separator)
    )

    useEffect(() => {
      if (isClient && animated) {
        springValue.set(value)
      }
    }, [springValue, value, isClient, animated])

    function formatNumber(num: number, dec: number, sep: string): string {
      const fixed = num.toFixed(dec)
      const [whole, decimal] = fixed.split(".")
      const formatted = whole.replace(/\B(?=(\d{3})+(?!\d))/g, sep)
      return decimal ? `${formatted}.${decimal}` : formatted
    }

    const handleIncrement = () => {
      if (onIncrement && value < max) {
        onIncrement()
      }
    }

    const handleDecrement = () => {
      if (onDecrement && value > min) {
        onDecrement()
      }
    }

    const getSizeClasses = () => {
      switch (size) {
        case 'sm': return 'text-sm p-2 gap-1'
        case 'lg': return 'text-lg p-6 gap-3'
        default: return 'text-base p-4 gap-2'
      }
    }

    const getVariantClasses = () => {
      switch (variant) {
        case 'primary': return 'border-blue-500 bg-blue-50 text-blue-900 dark:bg-blue-900/20 dark:text-blue-100'
        case 'secondary': return 'border-gray-500 bg-gray-50 text-gray-900 dark:bg-gray-900/20 dark:text-gray-100'
        case 'success': return 'border-green-500 bg-green-50 text-green-900 dark:bg-green-900/20 dark:text-green-100'
        case 'warning': return 'border-yellow-500 bg-yellow-50 text-yellow-900 dark:bg-yellow-900/20 dark:text-yellow-100'
        case 'error': return 'border-red-500 bg-red-50 text-red-900 dark:bg-red-900/20 dark:text-red-100'
        default: return 'border-gray-200 bg-white text-gray-900 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100'
      }
    }

    const getTextSize = () => {
      switch (size) {
        case 'sm': return 'text-lg'
        case 'lg': return 'text-3xl'
        default: return 'text-2xl'
      }
    }

    const counterClasses = cn(
      'flex items-center justify-center rounded-lg border-2 shadow-sm transition-all duration-200',
      'hover:border-blue-300 hover:shadow-md',
      'dark:hover:border-blue-600',
      getSizeClasses(),
      getVariantClasses(),
      glass && 'backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border-white/20 dark:border-gray-700/30'
    )

    if (!isClient) {
      return (
        <div ref={ref} className={cn('flex flex-col items-center', className)} {...props}>
          {label && (
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {label}
            </label>
          )}
          <div className={counterClasses}>
            {showControls && (
              <button
                onClick={handleDecrement}
                disabled={value <= min}
                className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors dark:bg-gray-700 dark:hover:bg-gray-600"
              >
                <Minus className="w-4 h-4" />
              </button>
            )}
            
            <span className={cn('font-mono font-bold', getTextSize())}>
              {prefix}{formatNumber(value, decimals, separator)}{suffix}
            </span>
            
            {showControls && (
              <button
                onClick={handleIncrement}
                disabled={value >= max}
                className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors dark:bg-gray-700 dark:hover:bg-gray-600"
              >
                <Plus className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      )
    }

    return (
      <motion.div
        ref={ref}
        className={cn('flex flex-col items-center', className)}
        initial={{ scale: 0.5, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5 }}
        {...props}
      >
        {label && (
          <motion.label
            className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {label}
          </motion.label>
        )}
        
        <div className={counterClasses}>
          {showControls && (
            <motion.button
              onClick={handleDecrement}
              disabled={value <= min}
              className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors dark:bg-gray-700 dark:hover:bg-gray-600"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Minus className="w-4 h-4" />
            </motion.button>
          )}
          
          <motion.span className={cn('font-mono font-bold', getTextSize())}>
            <span>{prefix}</span>
            {animated ? (
              <motion.span>{displayValue}</motion.span>
            ) : (
              <span>{formatNumber(value, decimals, separator)}</span>
            )}
            <span>{suffix}</span>
          </motion.span>
          
          {showControls && (
            <motion.button
              onClick={handleIncrement}
              disabled={value >= max}
              className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors dark:bg-gray-700 dark:hover:bg-gray-600"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Plus className="w-4 h-4" />
            </motion.button>
          )}
        </div>
      </motion.div>
    )
  }
)
LuminarCounter.displayName = "LuminarCounter"

export { LuminarCounter }