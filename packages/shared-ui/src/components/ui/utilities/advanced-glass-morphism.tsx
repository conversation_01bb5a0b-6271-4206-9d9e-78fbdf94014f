import React, { useState, useRef, useEffect } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

// Advanced Glass Morphism 3.0 with physics-based effects
export interface GlassMorphism3Props {
  children?: React.ReactNode;
  className?: string;
  variant?: 'refractive' | 'liquid' | 'holographic' | 'depth' | 'volumetric';
  intensity?: 'subtle' | 'md' | 'strong' | 'extreme';
  interactive?: boolean;
  animated?: boolean;
  physicsEnabled?: boolean;
  lightSource?: { x: number; y: number; intensity: number };
}

export const GlassMorphism3: React.FC<GlassMorphism3Props> = ({
  children,
  className = '',
  variant = 'refractive',
  intensity = 'md',
  interactive = true,
  animated = true,
  physicsEnabled = true,
  lightSource = { x: 0.3, y: 0.3, intensity: 0.8 }
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [hovered, setIsHovered] = useState(false);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  // Motion values for mouse tracking
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  // Spring physics for smooth interactions
  const springX = useSpring(mouseX, { stiffness: 150, damping: 20, mass: 0.1 });
  const springY = useSpring(mouseY, { stiffness: 150, damping: 20, mass: 0.1 });

  // Transform values for various effects
  const rotateX = useTransform(springY, [-0.5, 0.5], [5, -5]);
  const rotateY = useTransform(springX, [-0.5, 0.5], [-5, 5]);
  const translateZ = useTransform(springY, [-0.5, 0.5], [0, 10]);

  // Color shifting based on mouse position for holographic effect
  const hueShift = useTransform(springX, [-0.5, 0.5], [0, 60]);
  const saturation = useTransform(springY, [-0.5, 0.5], [1, 1.2]);

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        setDimensions({ width: clientWidth, height: clientHeight });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!interactive || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = (event.clientX - rect.left) / rect.width - 0.5;
    const y = (event.clientY - rect.top) / rect.height - 0.5;

    mouseX.set(x);
    mouseY.set(y);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    mouseX.set(0);
    mouseY.set(0);
  };

  const getVariantStyles = () => {
    const intensityMap = {
      subtle: { blur: 8, opacity: 0.1, border: 0.2 },
      medium: { blur: 16, opacity: 0.15, border: 0.3 },
      strong: { blur: 24, opacity: 0.2, border: 0.4 },
      extreme: { blur: 32, opacity: 0.3, border: 0.5 }
    };

    const { blur, opacity, border } = intensityMap[intensity];

    const variants = {
      refractive: {
        backdropFilter: `blur(${blur}px)`,
        background: `rgba(255, 255, 255, ${opacity})`,
        border: `1px solid rgba(255, 255, 255, ${border})`,
        boxShadow: `
          0 8px 32px rgba(31, 38, 135, 0.37),
          inset 0 1px 0 rgba(255, 255, 255, 0.5),
          inset 0 -1px 0 rgba(255, 255, 255, 0.2)
        `,
        position: 'relative' as const,
        overflow: 'hidden' as const
      },
      
      liquid: {
        backdropFilter: `blur(${blur}px)`,
        background: `
          linear-gradient(145deg, 
            rgba(255, 255, 255, ${opacity * 1.2}), 
            rgba(255, 255, 255, ${opacity * 0.8})
          )
        `,
        border: `1px solid rgba(255, 255, 255, ${border})`,
        borderRadius: '20px',
        boxShadow: `
          0 8px 32px rgba(31, 38, 135, 0.37),
          inset 0 2px 4px rgba(255, 255, 255, 0.3)
        `,
        position: 'relative' as const,
        overflow: 'hidden' as const
      },
      
      holographic: {
        backdropFilter: `blur(${blur}px) hue-rotate(${hovered ? 30 : 0}deg)`,
        background: `
          linear-gradient(145deg, 
            hsla(${hovered ? 240 : 200}, 70%, 80%, ${opacity}),
            hsla(${hovered ? 280 : 220}, 70%, 90%, ${opacity * 0.8}),
            hsla(${hovered ? 320 : 260}, 70%, 85%, ${opacity * 0.6})
          )
        `,
        border: `1px solid rgba(255, 255, 255, ${border})`,
        boxShadow: `
          0 8px 32px rgba(31, 38, 135, 0.37),
          0 0 20px rgba(138, 43, 226, ${hovered ? 0.4 : 0.1}),
          inset 0 1px 0 rgba(255, 255, 255, 0.4)
        `,
        position: 'relative' as const,
        overflow: 'hidden' as const
      },
      
      depth: {
        backdropFilter: `blur(${blur}px)`,
        background: `rgba(255, 255, 255, ${opacity})`,
        border: `1px solid rgba(255, 255, 255, ${border})`,
        boxShadow: `
          0 20px 40px rgba(31, 38, 135, 0.4),
          0 8px 16px rgba(31, 38, 135, 0.3),
          0 4px 8px rgba(31, 38, 135, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.5)
        `,
        transform: hovered ? 'translateZ(10px)' : 'translateZ(0px)',
        position: 'relative' as const,
        overflow: 'hidden' as const
      },
      
      volumetric: {
        backdropFilter: `blur(${blur}px)`,
        background: `
          radial-gradient(ellipse at ${lightSource.x * 100}% ${lightSource.y * 100}%, 
            rgba(255, 255, 255, ${opacity * lightSource.intensity}),
            rgba(255, 255, 255, ${opacity * 0.5}),
            rgba(255, 255, 255, ${opacity * 0.2})
          )
        `,
        border: `1px solid rgba(255, 255, 255, ${border})`,
        boxShadow: `
          0 8px 32px rgba(31, 38, 135, 0.37),
          inset 0 0 20px rgba(255, 255, 255, 0.2)
        `,
        position: 'relative' as const,
        overflow: 'hidden' as const
      }
    };

    return variants[variant];
  };

  const RefractionEffect = () => {
    if (variant !== 'refractive') return null;

    return (
      <div className="absolute inset-0 pointer-events-none">
        {/* Refraction overlay */}
        <div
          className="absolute inset-0"
          style={{
            background: `
              radial-gradient(circle at ${(mouseX.get() + 0.5) * 100}% ${(mouseY.get() + 0.5) * 100}%, 
                rgba(255, 255, 255, 0.3) 0%,
                rgba(255, 255, 255, 0.1) 30%,
                transparent 70%
              )
            `,
            mixBlendMode: 'overlay'
          }}
        />
        
        {/* Chromatic aberration effect */}
        <div
          className="absolute inset-0"
          style={{
            background: `
              radial-gradient(circle at ${(mouseX.get() + 0.5) * 100}% ${(mouseY.get() + 0.5) * 100}%, 
                rgba(255, 0, 100, 0.05) 0%,
                rgba(0, 255, 255, 0.05) 50%,
                transparent 100%
              )
            `,
            mixBlendMode: 'multiply'
          }}
        />
      </div>
    );
  };

  const LiquidEffect = () => {
    if (variant !== 'liquid') return null;

    return (
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute inset-0"
          animate={animated ? {
            background: [
              'radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.2) 0%, transparent 50%)',
              'radial-gradient(circle at 80% 50%, rgba(255, 255, 255, 0.2) 0%, transparent 50%)',
              'radial-gradient(circle at 50% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%)',
              'radial-gradient(circle at 50% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 50%)',
              'radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.2) 0%, transparent 50%)'
            ]
          } : {}}
          transition={{ duration: 8, repeat: Infinity, ease: 'linear' }}
          style={{ mixBlendMode: 'overlay' }}
        />
      </div>
    );
  };

  const HolographicEffect = () => {
    if (variant !== 'holographic') return null;

    return (
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute inset-0"
          style={{
            background: `
              linear-gradient(45deg,
                hsla(${240 + hueShift.get()}, 70%, 80%, 0.1),
                hsla(${280 + hueShift.get()}, 70%, 90%, 0.1),
                hsla(${320 + hueShift.get()}, 70%, 85%, 0.1)
              )
            `,
            mixBlendMode: 'color-dodge'
          }}
          animate={animated ? {
            background: [
              `linear-gradient(45deg, hsla(240, 70%, 80%, 0.1), hsla(280, 70%, 90%, 0.1), hsla(320, 70%, 85%, 0.1))`,
              `linear-gradient(135deg, hsla(260, 70%, 85%, 0.1), hsla(300, 70%, 80%, 0.1), hsla(340, 70%, 90%, 0.1))`,
              `linear-gradient(225deg, hsla(280, 70%, 90%, 0.1), hsla(320, 70%, 85%, 0.1), hsla(360, 70%, 80%, 0.1))`,
              `linear-gradient(315deg, hsla(300, 70%, 85%, 0.1), hsla(340, 70%, 90%, 0.1), hsla(20, 70%, 85%, 0.1))`,
              `linear-gradient(45deg, hsla(240, 70%, 80%, 0.1), hsla(280, 70%, 90%, 0.1), hsla(320, 70%, 85%, 0.1))`
            ]
          } : {}}
          transition={{ duration: 6, repeat: Infinity, ease: 'linear' }}
        />
        
        {/* RGB separation effect */}
        <div
          className="absolute inset-0"
          style={{
            background: `
              radial-gradient(circle at ${(mouseX.get() + 0.5) * 100}% ${(mouseY.get() + 0.5) * 100}%, 
                rgba(255, 0, 0, 0.05) 0%,
                rgba(0, 255, 0, 0.05) 33%,
                rgba(0, 0, 255, 0.05) 66%,
                transparent 100%
              )
            `,
            mixBlendMode: 'screen'
          }}
        />
      </div>
    );
  };

  const VolumetricEffect = () => {
    if (variant !== 'volumetric') return null;

    return (
      <div className="absolute inset-0 pointer-events-none">
        {/* Volumetric light rays */}
        <motion.div
          className="absolute inset-0"
          style={{
            background: `
              conic-gradient(from ${Date.now() / 50}deg at ${lightSource.x * 100}% ${lightSource.y * 100}%,
                rgba(255, 255, 255, 0.1) 0deg,
                transparent 30deg,
                rgba(255, 255, 255, 0.05) 60deg,
                transparent 90deg,
                rgba(255, 255, 255, 0.08) 120deg,
                transparent 150deg,
                rgba(255, 255, 255, 0.06) 180deg,
                transparent 210deg,
                rgba(255, 255, 255, 0.04) 240deg,
                transparent 270deg,
                rgba(255, 255, 255, 0.07) 300deg,
                transparent 330deg,
                rgba(255, 255, 255, 0.1) 360deg
              )
            `,
            mixBlendMode: 'soft-light'
          }}
          animate={animated ? {
            transform: ['rotate(0deg)', 'rotate(360deg)']
          } : {}}
          transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
        />
        
        {/* Atmospheric particles */}
        <div className="absolute inset-0">
          {Array.from({ length: 12 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full opacity-30"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`
              }}
              animate={animated ? {
                opacity: [0.1, 0.5, 0.1],
                scale: [0.5, 1.2, 0.5]
              } : {}}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2
              }}
            />
          ))}
        </div>
      </div>
    );
  };

  return (
    <motion.div
      ref={containerRef}
      className={cn('relative rounded-lg', className)}
      style={getVariantStyles()}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={handleMouseLeave}
      animate={physicsEnabled && hovered ? {
        rotateX: rotateX.get(),
        rotateY: rotateY.get(),
        scale: 1.02
      } : {
        rotateX: 0,
        rotateY: 0,
        scale: 1
      }}
      transition={{
        type: 'spring',
        stiffness: 150,
        damping: 20,
        mass: 0.1
      }}
    >
      <RefractionEffect />
      <LiquidEffect />
      <HolographicEffect />
      <VolumetricEffect />
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
      
      {/* Edge glow effect */}
      {hovered && (
        <motion.div
          className="absolute inset-0 rounded-lg pointer-events-none"
          style={{
            background: 'transparent',
            border: '1px solid rgba(255, 255, 255, 0.6)',
            boxShadow: '0 0 20px rgba(255, 255, 255, 0.2)'
          }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        />
      )}
    </motion.div>
  );
};