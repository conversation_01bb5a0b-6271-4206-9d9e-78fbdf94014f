import { motion, AnimatePresence } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes, useState, useMemo } from "react"
import { 
  ChevronRight, 
  ChevronLeft, 
  ChevronsRight, 
  ChevronsLeft,
  Search,
  Check,
  X
} from "lucide-react"
import { LuminarInput } from "../forms/input"
import { Button } from "../actions/button"
import { LuminarCheckbox } from "../forms/checkbox"
import { LuminarBadge } from "../display/badge"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface TransferItem {
  id: string
  label: string
  value: any
  disabled?: boolean
  description?: string
  meta?: any
}

export interface LuminarTransferListProps extends Omit<HTMLAttributes<HTMLDivElement>, 'onChange'> {
  items: TransferItem[]
  value?: string[]
  onChange?: (value: string[]) => void
  leftTitle?: string
  rightTitle?: string
  searchable?: boolean
  showSelectAll?: boolean
  glass?: boolean
  animated?: boolean
  height?: string | number
  emptyMessage?: string
  disabled?: boolean
  showCount?: boolean
}

const LuminarTransferList = forwardRef<HTMLDivElement, LuminarTransferListProps>(
  ({ 
    items,
    value = [],
    onChange,
    leftTitle = "Available",
    rightTitle = "Selected",
    searchable = true,
    showSelectAll = true,
    glass = true,
    animated = true,
    height = 400,
    emptyMessage = "No items",
    disabled = false,
    showCount = true,
    className,
    ...props 
  }, ref) => {
    const [selectedLeft, setSelectedLeft] = useState<Set<string>>(new Set())
    const [selectedRight, setSelectedRight] = useState<Set<string>>(new Set())
    const [searchLeft, setSearchLeft] = useState('')
    const [searchRight, setSearchRight] = useState('')

    // Separate items into left and right lists
    const { leftItems, rightItems } = useMemo(() => {
      const valueSet = new Set(value)
      const left = items.filter(item => !valueSet.has(item.id))
      const right = items.filter(item => valueSet.has(item.id))
      return { leftItems: left, rightItems: right }
    }, [items, value])

    // Filter items based on search
    const filteredLeft = useMemo(() => {
      if (!searchLeft) return leftItems
      const search = searchLeft.toLowerCase()
      return leftItems.filter(item => 
        item.label.toLowerCase().includes(search) ||
        item.description?.toLowerCase().includes(search)
      )
    }, [leftItems, searchLeft])

    const filteredRight = useMemo(() => {
      if (!searchRight) return rightItems
      const search = searchRight.toLowerCase()
      return rightItems.filter(item => 
        item.label.toLowerCase().includes(search) ||
        item.description?.toLowerCase().includes(search)
      )
    }, [rightItems, searchRight])

    // Get transferable items
    const transferableToRight = useMemo(() => {
      return Array.from(selectedLeft).filter(id => {
        const item = items.find(i => i.id === id)
        return item && !item.disabled && !value.includes(id)
      })
    }, [selectedLeft, items, value])

    const transferableToLeft = useMemo(() => {
      return Array.from(selectedRight).filter(id => {
        const item = items.find(i => i.id === id)
        return item && !item.disabled && value.includes(id)
      })
    }, [selectedRight, items, value])

    // Transfer functions
    const moveToRight = () => {
      if (transferableToRight.length > 0) {
        onChange?.([...value, ...transferableToRight])
        setSelectedLeft(new Set())
      }
    }

    const moveToLeft = () => {
      if (transferableToLeft.length > 0) {
        onChange?.(value.filter(id => !transferableToLeft.includes(id)))
        setSelectedRight(new Set())
      }
    }

    const moveAllToRight = () => {
      const allAvailable = leftItems
        .filter(item => !item.disabled)
        .map(item => item.id)
      onChange?.([...value, ...allAvailable])
      setSelectedLeft(new Set())
    }

    const moveAllToLeft = () => {
      onChange?.([])
      setSelectedRight(new Set())
    }

    // Selection functions
    const toggleSelectLeft = (id: string) => {
      const newSelected = new Set(selectedLeft)
      if (newSelected.has(id)) {
        newSelected.delete(id)
      } else {
        newSelected.add(id)
      }
      setSelectedLeft(newSelected)
    }

    const toggleSelectRight = (id: string) => {
      const newSelected = new Set(selectedRight)
      if (newSelected.has(id)) {
        newSelected.delete(id)
      } else {
        newSelected.add(id)
      }
      setSelectedRight(newSelected)
    }

    const selectAllLeft = (checked: boolean) => {
      if (checked) {
        const allIds = filteredLeft
          .filter(item => !item.disabled)
          .map(item => item.id)
        setSelectedLeft(new Set(allIds))
      } else {
        setSelectedLeft(new Set())
      }
    }

    const selectAllRight = (checked: boolean) => {
      if (checked) {
        const allIds = filteredRight
          .filter(item => !item.disabled)
          .map(item => item.id)
        setSelectedRight(new Set(allIds))
      } else {
        setSelectedRight(new Set())
      }
    }

    // Check if all items are selected
    const isAllLeftSelected = filteredLeft.length > 0 && 
      filteredLeft.filter(item => !item.disabled).every(item => selectedLeft.has(item.id))
    
    const isAllRightSelected = filteredRight.length > 0 && 
      filteredRight.filter(item => !item.disabled).every(item => selectedRight.has(item.id))

    const renderList = (
      items: TransferItem[],
      selected: Set<string>,
      onToggle: (id: string) => void,
      onSelectAll: (checked: boolean) => void,
      isAllSelected: boolean,
      search: string,
      onSearchChange: (value: string) => void,
      title: string
    ) => (
      <div className={cn(
        "flex-1 flex flex-col rounded-xl overflow-hidden",
        glass && "backdrop-blur-sm bg-white/5 border border-white/10"
      )}>
        {/* Header */}
        <div className="p-4 border-b border-white/10">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium">
              {title}
              {showCount && (
                <LuminarBadge variant="info" className="ml-2">
                  {items.length}
                </LuminarBadge>
              )}
            </h3>
            {showSelectAll && items.some(item => !item.disabled) && (
              <LuminarCheckbox
                checked={isAllSelected}
                onChange={(e) => onSelectAll(e.target.checked)}
                disabled={disabled}
                glass={glass}
              />
            )}
          </div>
          {searchable && (
            <LuminarInput
              placeholder="Search..."
              value={search}
              onChange={(e) => onSearchChange(e.target.value)}
              icon={Search}
              size="sm"
              glass={glass}
              disabled={disabled}
            />
          )}
        </div>

        {/* List */}
        <div 
          className="flex-1 overflow-y-auto p-2"
          style={{ maxHeight: typeof height === 'number' ? `${height}px` : height }}
        >
          <AnimatePresence mode="popLayout">
            {items.length === 0 ? (
              <div className="flex items-center justify-center h-full text-gray-500">
                {emptyMessage}
              </div>
            ) : (
              items.map((item, index) => (
                <motion.div
                  key={item.id}
                  layout
                  initial={animated ? { opacity: 0, x: -20 } : undefined}
                  animate={animated ? { opacity: 1, x: 0 } : undefined}
                  exit={animated ? { opacity: 0, x: 20 } : undefined}
                  transition={{ delay: index * 0.05 }}
                  className={cn(
                    "flex items-center p-3 rounded-lg mb-2 cursor-pointer transition-colors",
                    "hover:bg-white/5",
                    selected.has(item.id) && "bg-white/10",
                    item.disabled && "opacity-50 cursor-not-allowed"
                  )}
                  onClick={() => !item.disabled && !disabled && onToggle(item.id)}
                >
                  <LuminarCheckbox
                    checked={selected.has(item.id)}
                    onChange={(e) => {
                      e.stopPropagation();
                      onToggle(item.id);
                    }}
                    disabled={item.disabled || disabled}
                    onClick={(e) => e.stopPropagation()}
                    glass={glass}
                    className="mr-3"
                  />
                  <div className="flex-1">
                    <div className="font-medium">{item.label}</div>
                    {item.description && (
                      <div className="text-sm text-gray-400">{item.description}</div>
                    )}
                  </div>
                </motion.div>
              ))
            )}
          </AnimatePresence>
        </div>
      </div>
    )

    return (
      <div ref={ref} className={cn("flex gap-4", className)} {...props}>
        {/* Left List */}
        {renderList(
          filteredLeft,
          selectedLeft,
          toggleSelectLeft,
          selectAllLeft,
          isAllLeftSelected,
          searchLeft,
          setSearchLeft,
          leftTitle
        )}

        {/* Transfer Controls */}
        <div className="flex flex-col justify-center gap-2">
          <Button
            variant={glass ? "glass" : "ghost"}
            size="sm"
            onClick={moveAllToRight}
            disabled={disabled || leftItems.filter(item => !item.disabled).length === 0}
            aria-label="Move all to right"
          >
            <ChevronsRight className="w-4 h-4" />
          </Button>
          <Button
            variant={glass ? "glass-primary" : "default"}
            size="sm"
            onClick={moveToRight}
            disabled={disabled || transferableToRight.length === 0}
            aria-label="Move selected to right"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
          <Button
            variant={glass ? "glass-primary" : "default"}
            size="sm"
            onClick={moveToLeft}
            disabled={disabled || transferableToLeft.length === 0}
            aria-label="Move selected to left"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <Button
            variant={glass ? "glass" : "ghost"}
            size="sm"
            onClick={moveAllToLeft}
            disabled={disabled || rightItems.filter(item => !item.disabled).length === 0}
            aria-label="Move all to left"
          >
            <ChevronsLeft className="w-4 h-4" />
          </Button>
        </div>

        {/* Right List */}
        {renderList(
          filteredRight,
          selectedRight,
          toggleSelectRight,
          selectAllRight,
          isAllRightSelected,
          searchRight,
          setSearchRight,
          rightTitle
        )}
      </div>
    )
  }
)
LuminarTransferList.displayName = "LuminarTransferList"

export { LuminarTransferList }