import React, { useState, use<PERSON><PERSON>back, useEffect } from "react";
import { cn } from "@/lib/utils";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/display/card";
import { <PERSON><PERSON> } from "@/components/ui/actions/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/navigation/tabs";
import { ScrollArea } from "@/components/ui/layout/scroll-area";
import { Separator } from "@/components/ui/layout/divider";
import { Badge } from "@/components/ui/display/badge";
import { Label } from "@/components/ui/forms/label";
import { Slider } from "@/components/ui/forms/slider";
import { Switch } from "@/components/ui/forms/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/forms/select";
import AdvancedColorPicker from "@/components/ui/forms/advanced-color-picker";
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';
  Palette, 
  Moon, 
  Sun, 
  Download, 
  Upload, 
  Sparkles, 
  RotateCcw, 
  Eye,
  Settings,
  Contrast,
  Type,
  Layout,
  Brush

  themeManager, 
  Theme, 
  ThemeColors, 
  themePresets, 
  getContrastRatio,
  isHighContrast,
  type ColorFormat 

interface ThemeControlPanelProps {
  className?: string;
  onThemeChange?: (theme: Theme, mode: "light" | "dark") => void;
  showPreview?: boolean;
  compact?: boolean;
}

interface ColorSection {
  title: string;
  expanded?: boolean;
  colors: Array<{
    key: keyof ThemeColors;
    label: string;
    description?: string;
  }>;
}

const COLOR_SECTIONS: ColorSection[] = [
  {
    title: "Primary Colors",
    expanded: true,
    colors: [
      { key: "primary", label: "Primary", description: "Main brand color" },
      { key: "primary-foreground", label: "Primary Foreground", description: "Text on primary background" },
    ],
  },
  {
    title: "Secondary Colors",
    expanded: true,
    colors: [
      { key: "secondary", label: "Secondary", description: "Secondary brand color" },
      { key: "secondary-foreground", label: "Secondary Foreground", description: "Text on secondary background" },
    ],
  },
  {
    title: "Base Colors",
    colors: [
      { key: "background", label: "Background", description: "Main background color" },
      { key: "foreground", label: "Foreground", description: "Main text color" },
    ],
  },
  {
    title: "Card Colors",
    colors: [
      { key: "card", label: "Card Background", description: "Card container background" },
      { key: "card-foreground", label: "Card Foreground", description: "Text on card background" },
    ],
  },
  {
    title: "Interactive Colors",
    colors: [
      { key: "accent", label: "Accent", description: "Accent color for highlights" },
      { key: "accent-foreground", label: "Accent Foreground", description: "Text on accent background" },
      { key: "muted", label: "Muted", description: "Muted background color" },
      { key: "muted-foreground", label: "Muted Foreground", description: "Muted text color" },
    ],
  },
  {
    title: "Functional Colors",
    colors: [
      { key: "destructive", label: "Destructive", description: "Error and danger color" },
      { key: "destructive-foreground", label: "Destructive Foreground", description: "Text on destructive background" },
      { key: "border", label: "Border", description: "Border color" },
      { key: "input", label: "Input", description: "Input field border" },
      { key: "ring", label: "Ring", description: "Focus ring color" },
    ],
  },
  {
    title: "Chart Colors",
    colors: [
      { key: "chart-1", label: "Chart 1", description: "First chart color" },
      { key: "chart-2", label: "Chart 2", description: "Second chart color" },
      { key: "chart-3", label: "Chart 3", description: "Third chart color" },
      { key: "chart-4", label: "Chart 4", description: "Fourth chart color" },
      { key: "chart-5", label: "Chart 5", description: "Fifth chart color" },
    ],
  },
  {
    title: "Sidebar Colors",
    colors: [
      { key: "sidebar", label: "Sidebar Background" },
      { key: "sidebar-foreground", label: "Sidebar Foreground" },
      { key: "sidebar-primary", label: "Sidebar Primary" },
      { key: "sidebar-primary-foreground", label: "Sidebar Primary Foreground" },
      { key: "sidebar-accent", label: "Sidebar Accent" },
      { key: "sidebar-accent-foreground", label: "Sidebar Accent Foreground" },
      { key: "sidebar-border", label: "Sidebar Border" },
      { key: "sidebar-ring", label: "Sidebar Ring" },
    ],
  },
];

const FONT_OPTIONS = {
  sans: [
    { value: 'Inter, ui-sans-serif, system-ui, sans-serif', label: 'Inter' },
    { value: 'Geist, ui-sans-serif, system-ui, sans-serif', label: 'Geist' },
    { value: 'ui-sans-serif, system-ui, sans-serif', label: 'System UI' },
    { value: '"Helvetica Neue", Helvetica, Arial, sans-serif', label: 'Helvetica' },
    { value: 'Roboto, ui-sans-serif, system-ui, sans-serif', label: 'Roboto' },
  ],
  serif: [
    { value: 'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif', label: 'System Serif' },
    { value: '"Playfair Display", Georgia, serif', label: 'Playfair Display' },
    { value: 'Merriweather, Georgia, serif', label: 'Merriweather' },
  ],
  mono: [
    { value: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, monospace', label: 'System Mono' },
    { value: '"JetBrains Mono", ui-monospace, monospace', label: 'JetBrains Mono' },
    { value: '"Fira Code", ui-monospace, monospace', label: 'Fira Code' },
    { value: 'Geist Mono, ui-monospace, monospace', label: 'Geist Mono' },
  ],
};

export const ThemeControlPanel: React.FC<ThemeControlPanelProps> = ({
  className,
  onThemeChange,
  showPreview = true,
  compact = false,
}) => {
  const [currentTheme, setCurrentTheme] = useState<Theme>(themeManager.getCurrentTheme());
  const [currentMode, setCurrentMode] = useState<"light" | "dark">(themeManager.getCurrentMode());
  const [activeTab, setActiveTab] = useState("colors");
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(COLOR_SECTIONS.filter(s => s.expanded).map(s => s.title))
  );

  useEffect(() => {
    const unsubscribe = themeManager.subscribe((theme, mode) => {
      setCurrentTheme(theme);
      setCurrentMode(mode);
      onThemeChange?.(theme, mode);
    });

    return unsubscribe;
  }, [onThemeChange]);

  const handleColorChange = useCallback((colorKey: keyof ThemeColors, value: string) => {
    themeManager.updateColors({ [colorKey]: value }, currentMode);
  }, [currentMode]);

  const toggleMode = useCallback(() => {
    themeManager.toggleMode();
  }, []);

  const applyPreset = useCallback((presetName: string) => {
    themeManager.applyPreset(presetName);
  }, []);

  const resetToDefault = useCallback(() => {
    // Implementation would reset to default theme
    console.log("Reset to default theme");
  }, []);

  const exportTheme = useCallback(() => {
    const themeData = themeManager.exportTheme();
    const blob = new Blob([themeData], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${currentTheme.name.toLowerCase().replace(/\s+/g, "-")}-theme.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [currentTheme.name]);

  const importTheme = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (themeManager.importTheme(content)) {
        console.log("Theme imported successfully");
      } else {
        console.error("Failed to import theme");
      }
    };
    reader.readAsText(file);
  }, []);

  const toggleSection = useCallback((sectionTitle: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionTitle)) {
        newSet.delete(sectionTitle);
      } else {
        newSet.add(sectionTitle);
      }
      return newSet;
    });
  }, []);

  const currentStyles = currentTheme.styles[currentMode];

  return (
    <Card className={cn("w-full max-w-2xl", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Theme Editor
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleMode}
              className="h-8 w-8 p-0"
            >
              {currentMode === "light" ? (
                <Moon className="h-4 w-4" />
              ) : (
                <Sun className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={resetToDefault}
              className="h-8 w-8 p-0"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={exportTheme}
              className="h-8 w-8 p-0"
            >
              <Download className="h-4 w-4" />
            </Button>
            <div className="relative">
              <input
                type="file"
                accept=".json"
                onChange={importTheme}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              />
              <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                <Upload className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary">{currentMode}</Badge>
          <Badge variant="outline">{currentTheme.name}</Badge>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="colors" className="flex items-center gap-1">
              <Brush className="h-3 w-3" />
              Colors
            </TabsTrigger>
            <TabsTrigger value="typography" className="flex items-center gap-1">
              <Type className="h-3 w-3" />
              Type
            </TabsTrigger>
            <TabsTrigger value="layout" className="flex items-center gap-1">
              <Layout className="h-3 w-3" />
              Layout
            </TabsTrigger>
            <TabsTrigger value="presets" className="flex items-center gap-1">
              <Sparkles className="h-3 w-3" />
              Presets
            </TabsTrigger>
          </TabsList>

          <TabsContent value="colors" className="space-y-4">
            <ScrollArea className="h-[600px] pr-4">
              <div className="space-y-6">
                {COLOR_SECTIONS.map((section) => {
                  const isExpanded = expandedSections.has(section.title);
                  return (
                    <div key={section.title} className="space-y-3">
                      <Button
                        variant="ghost"
                        className="w-full justify-between p-0 h-auto"
                        onClick={() => toggleSection(section.title)}
                      >
                        <h3 className="text-sm font-medium">{section.title}</h3>
                        <Badge variant="secondary" className="text-xs">
                          {section.colors.length}
                        </Badge>
                      </Button>
                      
                      {isExpanded && (
                        <div className="space-y-3 pl-4 border-l-2 border-border">
                          {section.colors.map((colorConfig) => {
                            const contrastTarget = colorConfig.key.includes('foreground') 
                              ? colorConfig.key.replace('-foreground', '') as keyof ThemeColors
                              : `${colorConfig.key}-foreground` as keyof ThemeColors;
                            
                            const hasContrastTarget = currentStyles[contrastTarget];
                            const contrastRatio = hasContrastTarget 
                              ? getContrastRatio(currentStyles[colorConfig.key], currentStyles[contrastTarget])
                              : null;

                            return (
                              <div key={colorConfig.key} className="space-y-2">
                                <AdvancedColorPicker
                                  color={currentStyles[colorConfig.key]}
                                  onChange={(value) => handleColorChange(colorConfig.key, value)}
                                  label={colorConfig.label}
                                  name={colorConfig.key}
                                  showTailwindColors
                                  showPresets
                                />
                                
                                {colorConfig.description && (
                                  <p className="text-xs text-muted-foreground">
                                    {colorConfig.description}
                                  </p>
                                )}
                                
                                {contrastRatio && (
                                  <div className="flex items-center gap-2 text-xs">
                                    <Contrast className="h-3 w-3" />
                                    <span>Contrast: {contrastRatio.toFixed(2)}:1</span>
                                    <Badge 
                                      variant={contrastRatio >= 4.5 ? "default" : "destructive"}
                                      className="text-xs"
                                    >
                                      {contrastRatio >= 4.5 ? "AA" : "Fail"}
                                    </Badge>
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="typography" className="space-y-4">
            <div className="space-y-6">
              <div>
                <Label className="text-sm font-medium">Sans-serif Font</Label>
                <Select
                  value={currentStyles["font-sans"]}
                  onValueChange={(value) => themeManager.updateColors({"font-sans": value}, currentMode)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {FONT_OPTIONS.sans.map((font) => (
                      <SelectItem key={font.value} value={font.value}>
                        {font.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-sm font-medium">Serif Font</Label>
                <Select
                  value={currentStyles["font-serif"]}
                  onValueChange={(value) => themeManager.updateColors({"font-serif": value}, currentMode)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {FONT_OPTIONS.serif.map((font) => (
                      <SelectItem key={font.value} value={font.value}>
                        {font.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-sm font-medium">Monospace Font</Label>
                <Select
                  value={currentStyles["font-mono"]}
                  onValueChange={(value) => themeManager.updateColors({"font-mono": value}, currentMode)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {FONT_OPTIONS.mono.map((font) => (
                      <SelectItem key={font.value} value={font.value}>
                        {font.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-sm font-medium">Letter Spacing</Label>
                <Slider
                  value={[parseFloat(currentStyles["letter-spacing"]?.replace("em", "") || "0")]}
                  onValueChange={([value]) => 
                    themeManager.updateColors({"letter-spacing": `${value}em`}, currentMode)
                  }
                  min={-0.1}
                  max={0.1}
                  step={0.01}
                  className="mt-2"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>-0.1em</span>
                  <span>0.1em</span>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="layout" className="space-y-4">
            <div className="space-y-6">
              <div>
                <Label className="text-sm font-medium">Border Radius</Label>
                <Slider
                  value={[parseFloat(currentStyles.radius?.replace("rem", "") || "0.5")]}
                  onValueChange={([value]) => 
                    themeManager.updateColors({radius: `${value}rem`}, currentMode)
                  }
                  min={0}
                  max={2}
                  step={0.1}
                  className="mt-2"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>0rem</span>
                  <span>2rem</span>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Spacing</Label>
                <Slider
                  value={[parseFloat(currentStyles.spacing?.replace("rem", "") || "0.25")]}
                  onValueChange={([value]) => 
                    themeManager.updateColors({spacing: `${value}rem`}, currentMode)
                  }
                  min={0.1}
                  max={1}
                  step={0.05}
                  className="mt-2"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>0.1rem</span>
                  <span>1rem</span>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="presets" className="space-y-4">
            <div className="grid grid-cols-2 gap-3">
              {Object.entries(themePresets).map(([presetName, preset]) => (
                <Button
                  key={presetName}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-start gap-2"
                  onClick={() => applyPreset(presetName)}
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="w-4 h-4 rounded-full border"
                      style={{ backgroundColor: preset.primary }}
                    />
                    <span className="capitalize font-medium">{presetName}</span>
                  </div>
                  <div className="flex gap-1">
                    {[preset.primary, preset.accent, "#64748b"].map((color, i) => (
                      <div
                        key={i}
                        className="w-3 h-3 rounded-sm"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </Button>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ThemeControlPanel;