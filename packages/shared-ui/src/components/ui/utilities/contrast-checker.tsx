import React, { useState, useMemo } from "react";
import { cn } from "@/lib/utils";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/display/card";
import { <PERSON><PERSON> } from "@/components/ui/actions/button";
import { Badge } from "@/components/ui/display/badge";
import { ScrollArea } from "@/components/ui/layout/scroll-area";
import { Separator } from "@/components/ui/layout/divider";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/navigation/tabs";
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
  Contrast, 
  Check, 
  AlertTriangle, 
  Info, 
  ExternalLink,
  Moon,
  Sun,
  Eye,
  EyeOff

import { getContrastRatio, themeManager, type ThemeColors } from "@/lib/theme-manager";
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

interface ContrastCheckerProps {
  className?: string;
  compact?: boolean;
}

interface ColorPair {
  id: string;
  foregroundKey: keyof ThemeColors;
  backgroundKey: keyof ThemeColors;
  label: string;
  category: "content" | "interactive" | "functional";
  description?: string;
}

const COLOR_PAIRS: ColorPair[] = [
  // Content pairs
  {
    id: "base",
    foregroundKey: "foreground",
    backgroundKey: "background",
    label: "Base Text",
    category: "content",
    description: "Main body text on background"
  },
  {
    id: "card",
    foregroundKey: "card-foreground",
    backgroundKey: "card",
    label: "Card Text",
    category: "content",
    description: "Text on card backgrounds"
  },
  {
    id: "popover",
    foregroundKey: "popover-foreground",
    backgroundKey: "popover",
    label: "Popover Text",
    category: "content",
    description: "Text in popovers and tooltips"
  },
  {
    id: "muted",
    foregroundKey: "muted-foreground",
    backgroundKey: "muted",
    label: "Muted Text",
    category: "content",
    description: "Secondary and helper text"
  },

  // Interactive pairs
  {
    id: "primary",
    foregroundKey: "primary-foreground",
    backgroundKey: "primary",
    label: "Primary Button",
    category: "interactive",
    description: "Text on primary buttons"
  },
  {
    id: "secondary",
    foregroundKey: "secondary-foreground",
    backgroundKey: "secondary",
    label: "Secondary Button",
    category: "interactive",
    description: "Text on secondary buttons"
  },
  {
    id: "accent",
    foregroundKey: "accent-foreground",
    backgroundKey: "accent",
    label: "Accent Elements",
    category: "interactive",
    description: "Text on accent backgrounds"
  },

  // Functional pairs
  {
    id: "destructive",
    foregroundKey: "destructive-foreground",
    backgroundKey: "destructive",
    label: "Error States",
    category: "functional",
    description: "Text on error/danger backgrounds"
  },
  {
    id: "sidebar",
    foregroundKey: "sidebar-foreground",
    backgroundKey: "sidebar",
    label: "Sidebar",
    category: "functional",
    description: "Main sidebar text"
  },
  {
    id: "sidebar-primary",
    foregroundKey: "sidebar-primary-foreground",
    backgroundKey: "sidebar-primary",
    label: "Sidebar Primary",
    category: "functional",
    description: "Active sidebar items"
  },
  {
    id: "sidebar-accent",
    foregroundKey: "sidebar-accent-foreground",
    backgroundKey: "sidebar-accent",
    label: "Sidebar Accent",
    category: "functional",
    description: "Sidebar accent elements"
  },
];

const WCAG_STANDARDS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3.0,
  AAA_NORMAL: 7.0,
  AAA_LARGE: 4.5,
};

const getWCAGLevel = (ratio: number, isLargeText: boolean = false): {
  level: "AAA" | "AA" | "Fail";
  color: "default" | "secondary" | "destructive";
} => {
  const normalThreshold = isLargeText ? WCAG_STANDARDS.AA_LARGE : WCAG_STANDARDS.AA_NORMAL;
  const aaaThreshold = isLargeText ? WCAG_STANDARDS.AAA_LARGE : WCAG_STANDARDS.AAA_NORMAL;

  if (ratio >= aaaThreshold) {
    return { level: "AAA", color: "default" };
  } else if (ratio >= normalThreshold) {
    return { level: "AA", color: "secondary" };
  } else {
    return { level: "Fail", color: "destructive" };
  }
};

const ContrastPreview: React.FC<{
  foreground: string;
  background: string;
  label: string;
  ratio: number;
}> = ({ foreground, background, label, ratio }) => {
  const normalText = getWCAGLevel(ratio, false);
  const largeText = getWCAGLevel(ratio, true);

  return (
    <div
      className="rounded-lg border p-4 transition-all hover:shadow-md"
      style={{ backgroundColor: background }}
    >
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium" style={{ color: foreground }}>
            {label}
          </h3>
          <Badge variant="outline" className="text-xs">
            {ratio.toFixed(2)}:1
          </Badge>
        </div>
        
        <div className="space-y-2">
          <p className="text-sm" style={{ color: foreground }}>
            Regular text sample for accessibility testing
          </p>
          <p className="text-lg font-semibold" style={{ color: foreground }}>
            Large text sample
          </p>
        </div>

        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <Badge variant={normalText.color} className="text-xs">
              {normalText.level}
            </Badge>
            <span className="text-xs" style={{ color: foreground }}>
              Normal
            </span>
          </div>
          <div className="flex items-center gap-1">
            <Badge variant={largeText.color} className="text-xs">
              {largeText.level}
            </Badge>
            <span className="text-xs" style={{ color: foreground }}>
              Large
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export const ContrastChecker: React.FC<ContrastCheckerProps> = ({
  className,
  compact = false,
}) => {
  const [currentTheme] = useState(themeManager.getCurrentTheme());
  const [currentMode, setCurrentMode] = useState<"light" | "dark">(themeManager.getCurrentMode());
  const [filter, setFilter] = useState<"all" | "issues">("all");
  const [showLargeTextOnly, setShowLargeTextOnly] = useState(false);

  const currentColors = currentTheme.styles[currentMode];

  const contrastResults = useMemo(() => {
    return COLOR_PAIRS.map(pair => {
      const foreground = currentColors[pair.foregroundKey];
      const background = currentColors[pair.backgroundKey];
      const ratio = getContrastRatio(foreground, background);
      
      return {
        ...pair,
        foreground,
        background,
        ratio,
        normalText: getWCAGLevel(ratio, false),
        largeText: getWCAGLevel(ratio, true),
      };
    });
  }, [currentColors]);

  const filteredResults = useMemo(() => {
    let results = contrastResults;
    
    if (filter === "issues") {
      results = results.filter(result => 
        result.normalText.level === "Fail" || result.largeText.level === "Fail"
      );
    }
    
    return results;
  }, [contrastResults, filter]);

  const categoryStats = useMemo(() => {
    const stats = { content: 0, interactive: 0, functional: 0 };
    const issues = { content: 0, interactive: 0, functional: 0 };

    contrastResults.forEach(result => {
      stats[result.category]++;
      if (result.normalText.level === "Fail") {
        issues[result.category]++;
      }
    });

    return { stats, issues };
  }, [contrastResults]);

  const totalIssues = contrastResults.filter(
    result => result.normalText.level === "Fail"
  ).length;

  if (compact) {
    return (
      <Dialog>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className={cn("gap-2", className)}>
            <Contrast className="h-4 w-4" />
            Contrast
            {totalIssues > 0 && (
              <Badge variant="destructive" className="ml-1 text-xs">
                {totalIssues}
              </Badge>
            )}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Accessibility Contrast Checker</DialogTitle>
            <DialogDescription>
              Review color contrast ratios to ensure WCAG 2.1 compliance
            </DialogDescription>
          </DialogHeader>
          <ContrastCheckerContent
            contrastResults={filteredResults}
            filter={filter}
            setFilter={setFilter}
            currentMode={currentMode}
            setCurrentMode={setCurrentMode}
            showLargeTextOnly={showLargeTextOnly}
            setShowLargeTextOnly={setShowLargeTextOnly}
            totalIssues={totalIssues}
            categoryStats={categoryStats}
          />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Contrast className="h-5 w-5" />
            Contrast Checker
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentMode(currentMode === "light" ? "dark" : "light")}
            >
              {currentMode === "light" ? (
                <Sun className="h-4 w-4" />
              ) : (
                <Moon className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowLargeTextOnly(!showLargeTextOnly)}
            >
              {showLargeTextOnly ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ContrastCheckerContent
          contrastResults={filteredResults}
          filter={filter}
          setFilter={setFilter}
          currentMode={currentMode}
          setCurrentMode={setCurrentMode}
          showLargeTextOnly={showLargeTextOnly}
          setShowLargeTextOnly={setShowLargeTextOnly}
          totalIssues={totalIssues}
          categoryStats={categoryStats}
        />
      </CardContent>
    </Card>
  );
};

const ContrastCheckerContent: React.FC<{
  contrastResults: any[];
  filter: "all" | "issues";
  setFilter: (filter: "all" | "issues") => void;
  currentMode: "light" | "dark";
  setCurrentMode: (mode: "light" | "dark") => void;
  showLargeTextOnly: boolean;
  setShowLargeTextOnly: (show: boolean) => void;
  totalIssues: number;
  categoryStats: any;
}> = ({
  contrastResults,
  filter,
  setFilter,
  totalIssues,
  categoryStats,
}) => {
  const categories = ["content", "interactive", "functional"] as const;

  return (
    <div className="space-y-4">
      {/* Info Banner */}
      <div className="flex items-start gap-3 p-4 rounded-lg bg-muted/50 border">
        <Info className="h-5 w-5 text-muted-foreground shrink-0 mt-0.5" />
        <div className="space-y-1">
          <p className="text-sm font-medium">WCAG 2.1 Guidelines</p>
          <p className="text-sm text-muted-foreground">
            AA standard requires 4.5:1 for normal text, 3:1 for large text. 
            AAA standard requires 7:1 for normal text, 4.5:1 for large text.
          </p>
          <Button variant="link" size="sm" className="h-auto p-0 text-xs">
            <ExternalLink className="h-3 w-3 mr-1" />
            Learn more about WCAG
          </Button>
        </div>
      </div>

      {/* Filter Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant={filter === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => setFilter("all")}
          >
            All ({contrastResults.length})
          </Button>
          <Button
            variant={filter === "issues" ? "default" : "outline"}
            size="sm"
            onClick={() => setFilter("issues")}
            className="gap-1"
          >
            <AlertTriangle className="h-3 w-3" />
            Issues ({totalIssues})
          </Button>
        </div>

        <div className="flex items-center gap-2">
          {categories.map(category => (
            <Badge key={category} variant="secondary" className="text-xs">
              {category}: {categoryStats.stats[category] - categoryStats.issues[category]}/
              {categoryStats.stats[category]}
            </Badge>
          ))}
        </div>
      </div>

      {/* Results */}
      <ScrollArea className="h-[500px]">
        <Tabs defaultValue="content" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="interactive">Interactive</TabsTrigger>
            <TabsTrigger value="functional">Functional</TabsTrigger>
          </TabsList>

          {categories.map(category => (
            <TabsContent key={category} value={category} className="space-y-4">
              <div className="grid gap-4">
                {contrastResults
                  .filter(result => result.category === category)
                  .map(result => (
                    <ContrastPreview
                      key={result.id}
                      foreground={result.foreground}
                      background={result.background}
                      label={result.label}
                      ratio={result.ratio}
                    />
                  ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </ScrollArea>
    </div>
  );
};

export default ContrastChecker;