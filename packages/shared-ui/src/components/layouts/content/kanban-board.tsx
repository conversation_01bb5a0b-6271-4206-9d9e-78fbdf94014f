import { ReactNode, useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  MoreHorizontal,
  Calendar,
  User,
  MessageSquare,
  Paperclip,
  Flag,
  Edit3,
  Trash2,
  Eye,
  Archive,
  Copy,
  Filter,
  Search,
  Settings,
  Download,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  X
} from "lucide-react";

import { LuminarCard } from '../../ui/display';
import { Button } from '../../ui/actions';
import { LuminarInput } from '../../ui/forms';
import { LuminarBadge, LuminarAvatar } from '../../ui/display';
import { ThemeToggle } from '../../ui/utilities';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface KanbanBoardProps {
  className?: string;
  columns?: KanbanColumn[];
  onColumnAdd?: (title: string) => void;
  onColumnUpdate?: (columnId: string, updates: Partial<KanbanColumn>) => void;
  onColumnDelete?: (columnId: string) => void;
  onTaskAdd?: (columnId: string, task: Partial<KanbanTask>) => void;
  onTaskUpdate?: (taskId: string, updates: Partial<KanbanTask>) => void;
  onTaskMove?: (taskId: string, fromColumnId: string, toColumnId: string, position: number) => void;
  onTaskDelete?: (taskId: string) => void;
  boardTitle?: string;
  boardDescription?: string;
  showHeader?: boolean;
  showFilters?: boolean;
  members?: BoardMember[];
  loading?: boolean;
}

export interface KanbanColumn {
  id: string;
  title: string;
  description?: string;
  color?: string;
  limit?: number;
  tasks: KanbanTask[];
  isCollapsed?: boolean;
}

export interface KanbanTask {
  id: string;
  title: string;
  description?: string;
  priority: 'low' | 'md' | 'high' | 'urgent';
  status: 'todo' | 'in-progress' | 'review' | 'done';
  assignees: TaskAssignee[];
  labels: TaskLabel[];
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  attachments?: number;
  comments?: number;
  checklist?: ChecklistItem[];
  subtasks?: KanbanTask[];
  estimatedHours?: number;
  actualHours?: number;
}

export interface TaskAssignee {
  id: string;
  name: string;
  avatar?: string;
  email: string;
}

export interface TaskLabel {
  id: string;
  name: string;
  color: string;
}

export interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
}

export interface BoardMember {
  id: string;
  name: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  isOnline?: boolean;
}

const defaultColumns: KanbanColumn[] = [
  {
    id: 'todo',
    title: 'To Do',
    color: 'gray',
    tasks: [
      {
        id: '1',
        title: 'Design user authentication flow',
        description: 'Create wireframes and mockups for the login and signup process',
        priority: 'high',
        status: 'todo',
        assignees: [
          { id: '1', name: 'Alice Johnson', email: '<EMAIL>' }
        ],
        labels: [
          { id: '1', name: 'Design', color: 'purple' },
          { id: '2', name: 'UX', color: 'blue' }
        ],
        dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        createdAt: new Date(),
        updatedAt: new Date(),
        attachments: 2,
        comments: 3,
        estimatedHours: 8
      }
    ]
  },
  {
    id: 'in-progress',
    title: 'In Progress',
    color: 'blue',
    limit: 3,
    tasks: [
      {
        id: '2',
        title: 'Implement API endpoints',
        priority: 'md',
        status: 'in-progress',
        assignees: [
          { id: '2', name: 'Bob Smith', email: '<EMAIL>' }
        ],
        labels: [
          { id: '3', name: 'Backend', color: 'green' }
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
        comments: 1,
        estimatedHours: 12,
        actualHours: 6
      }
    ]
  },
  {
    id: 'review',
    title: 'Review',
    color: 'yellow',
    tasks: []
  },
  {
    id: 'done',
    title: 'Done',
    color: 'green',
    tasks: [
      {
        id: '3',
        title: 'Set up project repository',
        priority: 'low',
        status: 'done',
        assignees: [
          { id: '3', name: 'Charlie Brown', email: '<EMAIL>' }
        ],
        labels: [
          { id: '4', name: 'DevOps', color: 'orange' }
        ],
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        estimatedHours: 2,
        actualHours: 1.5
      }
    ]
  }
];

export function KanbanBoard({
  className,
  columns = defaultColumns,
  onColumnAdd,
  onColumnUpdate,
  onColumnDelete,
  onTaskAdd,
  onTaskUpdate,
  onTaskMove,
  onTaskDelete,
  boardTitle = "Project Board",
  boardDescription = "Track your project progress",
  showHeader = true,
  showFilters = true,
  members = [],
  loading = false,
  ...props
}: KanbanBoardProps) {
  const [newColumnTitle, setNewColumnTitle] = useState("");
  const [showAddColumn, setShowAddColumn] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPriority, setSelectedPriority] = useState<string | null>(null);
  const [selectedAssignee, setSelectedAssignee] = useState<string | null>(null);
  const [draggedTask, setDraggedTask] = useState<string | null>(null);
  const [draggedOver, setDraggedOver] = useState<string | null>(null);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100 dark:bg-red-900';
      case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900';
      case 'md': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900';
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent': return AlertCircle;
      case 'high': return Flag;
      case 'md': return Clock;
      case 'low': return CheckCircle;
      default: return Clock;
    }
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const diffTime = date.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays === -1) return 'Yesterday';
    if (diffDays < 0) return `${Math.abs(diffDays)} days ago`;
    return `${diffDays} days`;
  };

  const isOverdue = (date: Date) => {
    return date.getTime() < new Date().getTime();
  };

  const handleAddColumn = () => {
    if (newColumnTitle.trim()) {
      onColumnAdd?.(newColumnTitle);
      setNewColumnTitle("");
      setShowAddColumn(false);
    }
  };

  const filteredColumns = columns.map(column => ({
    ...column,
    tasks: column.tasks.filter(task => {
      const matchesSearch = !searchQuery || 
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesPriority = !selectedPriority || task.priority === selectedPriority;
      
      const matchesAssignee = !selectedAssignee || 
        task.assignees.some(assignee => assignee.id === selectedAssignee);

      return matchesSearch && matchesPriority && matchesAssignee;
    })
  }));

  return (
    <div className={cn(
      "min-h-screen bg-gray-50 dark:bg-gray-900 p-6",
      className
    )} {...props}>
      
      {/* Header */}
      {showHeader && (
        <header className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {boardTitle}
              </h1>
              {boardDescription && (
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  {boardDescription}
                </p>
              )}
            </div>
            
            <div className="flex items-center gap-4">
              {/* Board Members */}
              {members.length > 0 && (
                <div className="flex items-center gap-2">
                  <div className="flex -space-x-2">
                    {members.slice(0, 4).map((member) => (
                      <LuminarAvatar
                        key={member.id}
                        src={member.avatar}
                        alt={member.name}
                        size="sm"
                        fallback={member.name.charAt(0)}
                        className="border-2 border-white dark:border-gray-900"
                      />
                    ))}
                    {members.length > 4 && (
                      <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 border-2 border-white dark:border-gray-900 flex items-center justify-center text-xs font-medium">
                        +{members.length - 4}
                      </div>
                    )}
                  </div>
                  <Button variant="outline" size="sm">
                    <Users className="w-4 h-4 mr-2" />
                  </Button>
                </div>
              )}
              
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4 mr-2" />
              </Button>
              
              <ThemeToggle />
            </div>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="flex items-center gap-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <LuminarInput
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                icon={Search}
                className="w-64"
                size="sm"
              />
              
              <select
                value={selectedPriority || ''}
                onChange={(e) => setSelectedPriority(e.target.value || null)}
                className="px-3 py-1 text-sm border border-gray-200 dark:border-gray-700 rounded bg-white dark:bg-gray-800"
              >
                <option value="">All Priorities</option>
                <option value="urgent">Urgent</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
              
              <select
                value={selectedAssignee || ''}
                onChange={(e) => setSelectedAssignee(e.target.value || null)}
                className="px-3 py-1 text-sm border border-gray-200 dark:border-gray-700 rounded bg-white dark:bg-gray-800"
              >
                <option value="">All Assignees</option>
                {members.map((member) => (
                  <option key={member.id} value={member.id}>
                    {member.name}
                  </option>
                ))}
              </select>
              
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                More Filters
              </Button>
              
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
              </Button>
            </div>
          )}
        </header>
      )}

      {/* Kanban Board */}
      <div className="flex gap-6 overflow-x-auto pb-6">
        {filteredColumns.map((column) => (
          <div
            key={column.id}
            className="flex-shrink-0 w-80"
            onDragOver={(e) => {
              e.preventDefault();
              setDraggedOver(column.id);
            }}
            onDragLeave={() => setDraggedOver(null)}
            onDrop={(e) => {
              e.preventDefault();
              if (draggedTask && draggedTask !== column.id) {
                // Handle task move
                const taskId = draggedTask;
                const sourceColumn = columns.find(col => 
                  col.tasks.some(task => task.id === taskId)
                );
                if (sourceColumn) {
                  onTaskMove?.(taskId, sourceColumn.id, column.id, column.tasks.length);
                }
              }
              setDraggedOver(null);
              setDraggedTask(null);
            }}
          >
            <LuminarCard className={cn(
              "h-fit",
              draggedOver === column.id && "ring-2 ring-blue-500"
            )}>
              {/* Column Header */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <h2 className="font-semibold text-gray-900 dark:text-white">
                      {column.title}
                    </h2>
                    <LuminarBadge variant="default" size="sm">
                      {column.tasks.length}
                      {column.limit && `/${column.limit}`}
                    </LuminarBadge>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onTaskAdd?.(column.id, {})}
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                {column.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {column.description}
                  </p>
                )}
              </div>

              {/* Tasks */}
              <div className="p-4 space-y-3 min-h-[200px] max-h-[600px] overflow-y-auto">
                <AnimatePresence>
                  {column.tasks.map((task) => {
                    const PriorityIcon = getPriorityIcon(task.priority);
                    
                    return (
                      <motion.div
                        key={task.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        draggable
                        onDragStart={() => setDraggedTask(task.id)}
                        onDragEnd={() => setDraggedTask(null)}
                        className={cn(
                          "cursor-move",
                          draggedTask === task.id && "opacity-50"
                        )}
                      >
                        <LuminarCard className="p-4 hover:shadow-md transition-shadow">
                          {/* Task Header */}
                          <div className="flex items-start justify-between mb-3">
                            <h3 className="font-medium text-gray-900 dark:text-white leading-tight">
                              {task.title}
                            </h3>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="w-3 h-3" />
                            </Button>
                          </div>

                          {/* Task Description */}
                          {task.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                              {task.description}
                            </p>
                          )}

                          {/* Labels */}
                          {task.labels.length > 0 && (
                            <div className="flex flex-wrap gap-1 mb-3">
                              {task.labels.map((label) => (
                                <LuminarBadge
                                  key={label.id}
                                  variant="default"
                                  size="sm"
                                  style={{ backgroundColor: `${label.color}20`, borderColor: label.color }}
                                >
                                  {label.name}
                                </LuminarBadge>
                              ))}
                            </div>
                          )}

                          {/* Task Meta */}
                          <div className="space-y-2">
                            {/* Priority & Due Date */}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-1">
                                <PriorityIcon className={cn("w-3 h-3", getPriorityColor(task.priority))} />
                                <span className={cn("text-xs font-medium px-1 rounded", getPriorityColor(task.priority))}>
                                  {task.priority}
                                </span>
                              </div>
                              
                              {task.dueDate && (
                                <div className={cn(
                                  "flex items-center gap-1 text-xs",
                                  isOverdue(task.dueDate) ? "text-red-600" : "text-gray-500"
                                )}>
                                  <Calendar className="w-3 h-3" />
                                  {formatDate(task.dueDate)}
                                </div>
                              )}
                            </div>

                            {/* Progress Bar for Checklist */}
                            {task.checklist && task.checklist.length > 0 && (
                              <div className="space-y-1">
                                <div className="flex items-center justify-between text-xs text-gray-500">
                                  <span>Checklist</span>
                                  <span>
                                    {task.checklist.filter(item => item.completed).length}/{task.checklist.length}
                                  </span>
                                </div>
                                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                                  <div 
                                    className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                                    style={{ 
                                      width: `${(task.checklist.filter(item => item.completed).length / task.checklist.length) * 100}%` 
                                    }}
                                  />
                                </div>
                              </div>
                            )}

                            {/* Task Footer */}
                            <div className="flex items-center justify-between">
                              {/* Assignees */}
                              <div className="flex -space-x-1">
                                {task.assignees.slice(0, 3).map((assignee) => (
                                  <LuminarAvatar
                                    key={assignee.id}
                                    src={assignee.avatar}
                                    alt={assignee.name}
                                    size="sm"
                                    fallback={assignee.name.charAt(0)}
                                    className="border border-white dark:border-gray-800"
                                  />
                                ))}
                                {task.assignees.length > 3 && (
                                  <div className="w-5 h-5 rounded-full bg-gray-200 dark:bg-gray-700 border border-white dark:border-gray-800 flex items-center justify-center text-xs font-medium">
                                    +{task.assignees.length - 3}
                                  </div>
                                )}
                              </div>

                              {/* Task Stats */}
                              <div className="flex items-center gap-2">
                                {task.comments && task.comments > 0 && (
                                  <div className="flex items-center gap-1 text-xs text-gray-500">
                                    <MessageSquare className="w-3 h-3" />
                                    {task.comments}
                                  </div>
                                )}
                                {task.attachments && task.attachments > 0 && (
                                  <div className="flex items-center gap-1 text-xs text-gray-500">
                                    <Paperclip className="w-3 h-3" />
                                    {task.attachments}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </LuminarCard>
                      </motion.div>
                    );
                  })}
                </AnimatePresence>

                {/* Add Task Button */}
                <Button
                  variant="ghost"
                  className="w-full justify-start text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  onClick={() => onTaskAdd?.(column.id, {})}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add a task
                </Button>
              </div>
            </LuminarCard>
          </div>
        ))}

        {/* Add Column */}
        <div className="flex-shrink-0 w-80">
          {showAddColumn ? (
            <LuminarCard className="p-4">
              <div className="space-y-3">
                <LuminarInput
                  value={newColumnTitle}
                  onChange={(e) => setNewColumnTitle(e.target.value)}
                  placeholder="Enter column title..."
                  onKeyPress={(e) => e.key === 'Enter' && handleAddColumn()}
                  autoFocus
                />
                <div className="flex gap-2">
                  <Button size="sm" onClick={handleAddColumn}>
                    Add Column
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setShowAddColumn(false);
                      setNewColumnTitle("");
                    }}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </LuminarCard>
          ) : (
            <Button
              variant="outline"
              className="w-full h-12 border-dashed"
              onClick={() => setShowAddColumn(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add another column
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}