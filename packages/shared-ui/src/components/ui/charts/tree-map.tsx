import React from 'react';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface TreeMapData {
  id: string;
  label: string;
  value: number;
  color?: string;
  children?: TreeMapData[];
}

export interface TreeMapProps {
  data: TreeMapData[];
  width?: number;
  height?: number;
  colorScheme?: 'blue' | 'green' | 'red' | 'purple' | 'orange' | 'mixed';
  showLabels?: boolean;
  showValues?: boolean;
  onNodeClick?: (node: TreeMapData) => void;
  className?: string;
}

interface TreeMapNode extends TreeMapData {
  x: number;
  y: number;
  width: number;
  height: number;
  depth: number;
}

export function LuminarTreeMap({
  data,
  width = 600,
  height = 400,
  colorScheme = 'mixed',
  showLabels = true,
  showValues = true,
  onNodeClick,
  className = ''
}: TreeMapProps) {
  const glassClasses = getAdaptiveGlassClasses('card', 'md', 'neutral');

  // Color schemes
  const colorSchemes = {
    blue: ['#dbeafe', '#bfdbfe', '#93c5fd', '#60a5fa', '#3b82f6', '#2563eb'],
    green: ['#d1fae5', '#a7f3d0', '#6ee7b7', '#34d399', '#10b981', '#059669'],
    red: ['#fee2e2', '#fecaca', '#fca5a5', '#f87171', '#ef4444', '#dc2626'],
    purple: ['#f3e8ff', '#e9d5ff', '#d8b4fe', '#c084fc', '#a855f7', '#9333ea'],
    orange: ['#fed7aa', '#fdba74', '#fb923c', '#f97316', '#ea580c', '#c2410c'],
    mixed: ['#dbeafe', '#d1fae5', '#fee2e2', '#f3e8ff', '#fed7aa', '#fde68a']
  };

  // Simple treemap layout algorithm
  const layoutNodes = (nodes: TreeMapData[], x: number, y: number, width: number, height: number, depth: number = 0): TreeMapNode[] => {
    const result: TreeMapNode[] = [];
    const totalValue = nodes.reduce((sum, node) => sum + node.value, 0);
    
    let currentX = x;
    let currentY = y;
    
    nodes.forEach((node, index) => {
      const ratio = node.value / totalValue;
      const nodeWidth = width * ratio;
      const nodeHeight = height;
      
      const layoutNode: TreeMapNode = {
        ...node,
        x: currentX,
        y: currentY,
        width: nodeWidth,
        height: nodeHeight,
        depth
      };
      
      result.push(layoutNode);
      
      if (node.children && node.children.length > 0) {
        const childNodes = layoutNodes(
          node.children,
          currentX + 2,
          currentY + 2,
          nodeWidth - 4,
          nodeHeight - 4,
          depth + 1
        );
        result.push(...childNodes);
      }
      
      currentX += nodeWidth;
    });
    
    return result;
  };

  const nodes = layoutNodes(data, 0, 0, width, height);

  // Get color for node
  const getNodeColor = (node: TreeMapNode) => {
    if (node.color) return node.color;
    
    const colors = colorSchemes[colorScheme];
    if (colorScheme === 'mixed') {
      return colors[node.depth % colors.length];
    }
    
    const colorIndex = Math.min(node.depth, colors.length - 1);
    return colors[colorIndex];
  };

  return (
    <div className={`${glassClasses} p-4 rounded-lg border ${className}`}>
      <div className="overflow-auto">
        <svg width={width} height={height}>
          {nodes.map((node, index) => (
            <g key={`${node.id}-${index}`}>
              <rect
                x={node.x}
                y={node.y}
                width={node.width}
                height={node.height}
                fill={getNodeColor(node)}
                stroke="var(--border)"
                strokeWidth="1"
                className="cursor-pointer hover:stroke-foreground transition-colors"
                onClick={() => onNodeClick?.(node)}
              />
              
              {showLabels && node.width > 50 && node.height > 20 && (
                <text
                  x={node.x + 4}
                  y={node.y + 16}
                  fontSize="12"
                  fill="currentColor"
                  className="text-foreground font-medium pointer-events-none"
                >
                  {node.label}
                </text>
              )}
              
              {showValues && node.width > 30 && node.height > 35 && (
                <text
                  x={node.x + 4}
                  y={node.y + (showLabels ? 32 : 16)}
                  fontSize="10"
                  fill="currentColor"
                  className="text-muted-foreground pointer-events-none"
                >
                  {node.value}
                </text>
              )}
            </g>
          ))}
        </svg>
      </div>
      
      <div className="mt-4 flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Total: {data.reduce((sum, node) => sum + node.value, 0)}
        </div>
        <div className="text-sm text-muted-foreground">
          {nodes.length} nodes
        </div>
      </div>
    </div>
  );
}

// Sample data generator
export function generateSampleTreeMapData(): TreeMapData[] {
  return [
    {
      id: 'frontend',
      label: 'Frontend',
      value: 450,
      children: [
        { id: 'react', label: 'React', value: 200 },
        { id: 'vue', label: 'Vue', value: 150 },
        { id: 'angular', label: 'Angular', value: 100 }
      ]
    },
    {
      id: 'backend',
      label: 'Backend',
      value: 350,
      children: [
        { id: 'node', label: 'Node.js', value: 180 },
        { id: 'python', label: 'Python', value: 120 },
        { id: 'java', label: 'Java', value: 50 }
      ]
    },
    {
      id: 'database',
      label: 'Database',
      value: 200,
      children: [
        { id: 'mysql', label: 'MySQL', value: 80 },
        { id: 'postgres', label: 'PostgreSQL', value: 70 },
        { id: 'mongodb', label: 'MongoDB', value: 50 }
      ]
    }
  ];
}