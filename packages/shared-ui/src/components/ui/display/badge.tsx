import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes } from "react"
import { X } from "lucide-react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface LuminarBadgeProps extends HTMLAttributes<HTMLDivElement> {
  variant?: ComponentVariant | "secondary" | "success" | "warning" | "error" | "info"
  size?: ComponentSize | "md" | "lg"
  glass?: boolean
  removable?: boolean
  onRemove?: () => void
  pulse?: boolean
}

const LuminarBadge = forwardRef<HTMLDivElement, LuminarBadgeProps>(
  ({ 
    variant = defaultComponentProps.variant,
    size = defaultComponentProps.size,
    glass = true,
    removable = false,
    onRemove,
    pulse = false,
    className,
    children,
    ...props 
  }, ref) => {
    const sizeClasses = {
      xs: "px-1.5 py-0.5 text-xs",
      sm: "px-2 py-0.5 text-xs",
      md: "px-2.5 py-1 text-sm",
      lg: "px-3 py-1.5 text-base"
    }

    const variantClasses = {
      default: glass 
        ? "bg-white/20 dark:bg-gray-900/20 text-foreground" 
        : "bg-secondary text-secondary-foreground",
      secondary: glass
        ? "bg-gray-500/20 text-gray-600 dark:text-gray-400 border-gray-500/30"
        : "bg-gray-500 text-white",
      success: glass
        ? "bg-green-500/20 text-green-600 dark:text-green-400 border-green-500/30"
        : "bg-green-500 text-white",
      warning: glass
        ? "bg-yellow-500/20 text-yellow-600 dark:text-yellow-400 border-yellow-500/30"
        : "bg-yellow-500 text-white",
      error: glass
        ? "bg-red-500/20 text-red-600 dark:text-red-400 border-red-500/30"
        : "bg-red-500 text-white",
      info: glass
        ? "bg-blue-500/20 text-blue-600 dark:text-blue-400 border-blue-500/30"
        : "bg-blue-500 text-white"
    }

    return (
      <motion.div
        ref={ref}
        className={cn(
          "inline-flex items-center gap-1 rounded-full font-medium",
          glass && "backdrop-blur-md border border-white/20 dark:border-gray-700/30",
          sizeClasses[size],
          variantClasses[variant],
          className
        )}
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0, opacity: 0 }}
        whileHover={{ scale: 1.05 }}
        transition={{ type: "spring", stiffness: 500, damping: 30 }}
      >
        {pulse && (
          <motion.span
            className={cn(
              "absolute -inset-1 rounded-full",
              variant === "default" && "bg-gray-400",
              variant === "secondary" && "bg-gray-500",
              variant === "success" && "bg-green-500",
              variant === "warning" && "bg-yellow-500",
              variant === "error" && "bg-red-500",
              variant === "info" && "bg-blue-500"
            )}
            animate={{
              scale: [1, 1.5, 1.5],
              opacity: [0.5, 0, 0]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatDelay: 1
            }}
          />
        )}
        
        <span className="relative z-10">{children}</span>
        
        {removable && (
          <motion.button
            className="relative z-10 ml-1 -mr-1 p-0.5 rounded-full hover:bg-black/10 dark:hover:bg-white/10"
            onClick={(e) => {
              e.stopPropagation()
              onRemove?.()
            }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <X className={cn(
              size === "xs" && "w-2 h-2",
              size === "sm" && "w-2.5 h-2.5",
              size === "md" && "w-3 h-3",
              size === "lg" && "w-3.5 h-3.5"
            )} />
          </motion.button>
        )}
      </motion.div>
    )
  }
)
LuminarBadge.displayName = "LuminarBadge"

export { LuminarBadge }