/**
 * Component Props Integration Tests
 * 
 * Tests to verify that the standardized props system works correctly
 * and provides proper TypeScript support.
 */

import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Import components with their types
import { Button, type ButtonProps } from '../components/ui/actions/button';
import { LuminarCard, type LuminarCardProps } from '../components/ui/display/card';
import { LuminarInput, type LuminarInputProps } from '../components/ui/forms/input';

// Import types for testing
import type {
  ComponentSize,
  ComponentVariant,
  GlassIntensity,
  AnimationPreset,
  StandardComponentProps,
  StandardFormComponentProps
} from '../types/component-props';
import { defaultComponentProps, defaultFormProps } from '../types/component-props';

describe('Component Props Standardization', () => {
  describe('Button Component', () => {
    it('should render with default props', () => {
      render(<Button>Test Button</Button>);
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent('Test Button');
    });

    it('should handle all size variants', () => {
      const sizes: ComponentSize[] = ['xs', 'sm', 'md', 'lg', 'xl'];
      
      sizes.forEach(size => {
        const { rerender } = render(<Button size={size}>Size {size}</Button>);
        const button = screen.getByRole('button');
        expect(button).toBeInTheDocument();
        rerender(<></>);
      });
    });

    it('should handle all variant types', () => {
      const variants: ComponentVariant[] = [
        'default', 'primary', 'secondary', 'outline', 'ghost', 
        'glass', 'gradient', 'destructive', 'success', 'warning', 'info'
      ];
      
      variants.forEach(variant => {
        const { rerender } = render(<Button variant={variant}>Variant {variant}</Button>);
        const button = screen.getByRole('button');
        expect(button).toBeInTheDocument();
        rerender(<></>);
      });
    });

    it('should handle loading state', () => {
      render(<Button loading={true} loadingText="Loading...">Test</Button>);
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-busy', 'true');
      expect(button).toBeDisabled();
    });

    it('should handle disabled state', () => {
      render(<Button disabled={true}>Test</Button>);
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });

    it('should handle click events', async () => {
      const handleClick = vi.fn();
      render(<Button onClick={handleClick}>Test</Button>);
      
      const button = screen.getByRole('button');
      await userEvent.click(button);
      
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('should handle accessibility props', () => {
      render(
        <Button
          aria-label="Test button"
          aria-describedby="test-description"
          data-testid="test-button"
        >
          Test
        </Button>
      );
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Test button');
      expect(button).toHaveAttribute('aria-describedby', 'test-description');
      expect(button).toHaveAttribute('data-testid', 'test-button');
    });

    it('should handle glass props', () => {
      render(
        <Button glass={true} glassIntensity="medium">
          Glass Button
        </Button>
      );
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });
  });

  describe('Card Component', () => {
    it('should render with default props', () => {
      render(<LuminarCard>Test Card</LuminarCard>);
      const card = screen.getByText('Test Card');
      expect(card).toBeInTheDocument();
    });

    it('should handle clickable cards', async () => {
      const handleClick = vi.fn();
      render(
        <LuminarCard clickable={true} onClick={handleClick}>
          Clickable Card
        </LuminarCard>
      );
      
      const card = screen.getByText('Clickable Card').closest('div');
      await userEvent.click(card!);
      
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('should handle elevation', () => {
      const elevations: Array<0 | 1 | 2 | 3 | 4 | 5> = [0, 1, 2, 3, 4, 5];
      
      elevations.forEach(elevation => {
        const { rerender } = render(
          <LuminarCard elevation={elevation}>
            Elevation {elevation}
          </LuminarCard>
        );
        const card = screen.getByText(`Elevation ${elevation}`);
        expect(card).toBeInTheDocument();
        rerender(<></>);
      });
    });

    it('should handle glass props', () => {
      render(
        <LuminarCard glass={true} glassIntensity="strong">
          Glass Card
        </LuminarCard>
      );
      
      const card = screen.getByText('Glass Card');
      expect(card).toBeInTheDocument();
    });

    it('should handle layout props', () => {
      render(
        <LuminarCard
          padding="lg"
          centered={true}
          maxWidth="400px"
        >
          Layout Card
        </LuminarCard>
      );
      
      const card = screen.getByText('Layout Card');
      expect(card).toBeInTheDocument();
    });
  });

  describe('Input Component', () => {
    it('should render with default props', () => {
      render(<LuminarInput placeholder="Test input" />);
      const input = screen.getByPlaceholderText('Test input');
      expect(input).toBeInTheDocument();
    });

    it('should handle form props', () => {
      const handleChange = vi.fn();
      render(
        <LuminarInput
          name="test"
          required={true}
          onChange={handleChange}
          placeholder="Test input"
        />
      );
      
      const input = screen.getByPlaceholderText('Test input');
      expect(input).toHaveAttribute('name', 'test');
      expect(input).toHaveAttribute('required');
    });

    it('should handle label and helper text', () => {
      render(
        <LuminarInput
          label="Test Label"
          helperText="Helper text"
          placeholder="Test input"
        />
      );
      
      expect(screen.getByText('Test Label')).toBeInTheDocument();
      expect(screen.getByText('Helper text')).toBeInTheDocument();
    });

    it('should handle error state', () => {
      render(
        <LuminarInput
          error="Test error"
          showError={true}
          placeholder="Test input"
        />
      );
      
      expect(screen.getByText('Test error')).toBeInTheDocument();
    });

    it('should handle disabled state', () => {
      render(<LuminarInput disabled={true} placeholder="Test input" />);
      const input = screen.getByPlaceholderText('Test input');
      expect(input).toBeDisabled();
    });

    it('should handle different input types', () => {
      const types: Array<'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search'> = [
        'text', 'email', 'password', 'number', 'tel', 'url', 'search'
      ];
      
      types.forEach(type => {
        const { rerender } = render(
          <LuminarInput type={type} placeholder={`${type} input`} />
        );
        const input = screen.getByPlaceholderText(`${type} input`);
        expect(input).toHaveAttribute('type', type);
        rerender(<></>);
      });
    });
  });

  describe('Type Safety', () => {
    it('should enforce correct prop types', () => {
      // These should compile without errors
      const buttonProps: ButtonProps = {
        variant: 'primary',
        size: 'lg',
        loading: false,
        disabled: false,
        onClick: () => {},
        'aria-label': 'Test button'
      };

      const cardProps: LuminarCardProps = {
        elevation: 3,
        clickable: true,
        glass: true,
        glassIntensity: 'medium',
        padding: 'lg'
      };

      const inputProps: LuminarInputProps = {
        type: 'email',
        required: true,
        error: 'Invalid email',
        showError: true,
        placeholder: 'Enter email'
      };

      // Test that the props are correctly typed
      expect(buttonProps.variant).toBe('primary');
      expect(cardProps.elevation).toBe(3);
      expect(inputProps.type).toBe('email');
    });

    it('should have correct default values', () => {
      expect(defaultComponentProps.variant).toBe('default');
      expect(defaultComponentProps.size).toBe('md');
      expect(defaultComponentProps.glass).toBe(false);
      expect(defaultComponentProps.animation).toBe('fadeIn');
      expect(defaultComponentProps.disableAnimation).toBe(false);
      expect(defaultComponentProps.interactive).toBe(true);
      expect(defaultComponentProps.hoverable).toBe(true);

      expect(defaultFormProps.required).toBe(false);
      expect(defaultFormProps.showError).toBe(true);
    });
  });

  describe('Animation Props', () => {
    it('should handle animation presets', () => {
      const animations: AnimationPreset[] = [
        'fadeIn', 'slideUp', 'slideDown', 'slideLeft', 'slideRight',
        'scale', 'rotate', 'bounce', 'pulse', 'wiggle', 'none'
      ];

      animations.forEach(animation => {
        const { rerender } = render(
          <Button animation={animation}>
            Animation {animation}
          </Button>
        );
        const button = screen.getByRole('button');
        expect(button).toBeInTheDocument();
        rerender(<></>);
      });
    });

    it('should handle animation timing', () => {
      render(
        <Button
          animation="bounce"
          animationDuration={0.5}
          animationDelay={0.1}
        >
          Timed Animation
        </Button>
      );
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('should handle disabled animations', () => {
      render(
        <Button disableAnimation={true}>
          No Animation
        </Button>
      );
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });
  });

  describe('Glass Props', () => {
    it('should handle glass intensities', () => {
      const intensities: GlassIntensity[] = [
        'subtle', 'light', 'medium', 'strong', 'intense'
      ];

      intensities.forEach(intensity => {
        const { rerender } = render(
          <Button glass={true} glassIntensity={intensity}>
            Glass {intensity}
          </Button>
        );
        const button = screen.getByRole('button');
        expect(button).toBeInTheDocument();
        rerender(<></>);
      });
    });

    it('should handle custom glass config', () => {
      render(
        <LuminarCard
          glass={true}
          glassConfig={{
            blur: 20,
            opacity: 0.1,
            saturation: 1.2,
            brightness: 1.1
          }}
        >
          Custom Glass
        </LuminarCard>
      );
      
      const card = screen.getByText('Custom Glass');
      expect(card).toBeInTheDocument();
    });
  });
});

// TypeScript compilation tests
describe('TypeScript Integration', () => {
  it('should provide correct types for IntelliSense', () => {
    // This test ensures TypeScript compilation works correctly
    
    // Test StandardComponentProps usage
    const standardProps: StandardComponentProps = {
      variant: 'primary',
      size: 'md',
      glass: true,
      glassIntensity: 'medium',
      animation: 'fadeIn',
      disableAnimation: false,
      interactive: true,
      hoverable: true,
      className: 'custom-class',
      'data-testid': 'test-component'
    };

    // Test StandardFormComponentProps usage
    const formProps: StandardFormComponentProps<string> = {
      ...standardProps,
      value: 'test value',
      onChange: (value: string) => console.log(value),
      required: true,
      error: 'Test error',
      showError: true
    };

    expect(standardProps.variant).toBe('primary');
    expect(formProps.required).toBe(true);
  });

  it('should enforce type safety in prop validation', () => {
    // Import validation functions to test
    const { isValidComponentSize, isValidComponentVariant } = require('../lib/prop-validation');
    
    // Test size validation
    expect(isValidComponentSize('md')).toBe(true);
    expect(isValidComponentSize('invalid')).toBe(false);
    
    // Test variant validation
    expect(isValidComponentVariant('primary')).toBe(true);
    expect(isValidComponentVariant('invalid')).toBe(false);
  });

  it('should provide utility functions for prop processing', () => {
    // Import utility functions to test
    const { getVariantClasses, getSizeClasses } = require('../lib/component-utilities');
    
    // Test class generation
    expect(getVariantClasses('primary')).toContain('bg-blue-600');
    expect(getSizeClasses('md')).toContain('h-10');
  });
});