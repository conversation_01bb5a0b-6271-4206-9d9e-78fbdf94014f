import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes } from "react"

export interface LuminarProgressRingProps extends HTMLAttributes<HTMLDivElement> {
  value: number
  max?: number
  size?: number | "xs" | "sm" | "md" | "lg" | "xl"
  strokeWidth?: number
  thickness?: number // Alias for strokeWidth
  showValue?: boolean
  showLabel?: boolean
  label?: string
  color?: string
  backgroundColor?: string
  glass?: boolean
  animated?: boolean
  gradient?: { from: string; to: string } // For gradient support
}

const LuminarProgressRing = forwardRef<HTMLDivElement, LuminarProgressRingProps>(
  ({ 
    value,
    max = 100,
    size = 120,
    strokeWidth = 8,
    thickness,
    showValue = true,
    showLabel = true,
    label,
    color = "rgb(var(--primary))",
    backgroundColor = "rgba(255, 255, 255, 0.1)",
    glass = true,
    animated = true,
    gradient,
    className,
    ...props 
  }, ref) => {
    // Convert size to number if it's a string
    const sizeMap = {
      xs: 40,
      sm: 60,
      md: 80,
      lg: 120,
      xl: 160
    }
    const numericSize = typeof size === 'string' ? sizeMap[size] || 120 : size
    
    // Use thickness if provided, otherwise use strokeWidth
    const effectiveStrokeWidth = thickness || strokeWidth
    
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
    const radius = (numericSize - effectiveStrokeWidth) / 2
    const circumference = 2 * Math.PI * radius
    const strokeDasharray = circumference
    const strokeDashoffset = circumference - (percentage / 100) * circumference
    
    return (
      <div
        ref={ref}
        className={cn(
          "relative inline-flex items-center justify-center",
          glass && "rounded-full backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30 p-4",
          className
        )}
        style={{ width: numericSize + (glass ? 32 : 0), height: numericSize + (glass ? 32 : 0) }}
        {...props}
      >
        <svg
          width={numericSize}
          height={numericSize}
          className="transform -rotate-90"
          style={{ filter: glass ? "drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))" : undefined }}
        >
          <defs>
            <linearGradient id="progress-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor={color} stopOpacity={0.8} />
              <stop offset="100%" stopColor={color} stopOpacity={1} />
            </linearGradient>
            <filter id="glow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>
          
          {/* Background circle */}
          <circle
            cx={numericSize / 2}
            cy={numericSize / 2}
            r={radius}
            fill="none"
            stroke={backgroundColor}
            strokeWidth={effectiveStrokeWidth}
            strokeLinecap="round"
          />
          
          {/* Progress circle */}
          <motion.circle
            cx={numericSize / 2}
            cy={numericSize / 2}
            r={radius}
            fill="none"
            stroke="url(#progress-gradient)"
            strokeWidth={effectiveStrokeWidth}
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            filter="url(#glow)"
            initial={animated ? { strokeDashoffset: circumference } : { strokeDashoffset }}
            animate={{ strokeDashoffset }}
            transition={{
              duration: animated ? 1.5 : 0,
              ease: "easeInOut"
            }}
          />
          
          {/* Inner glow effect */}
          <motion.circle
            cx={numericSize / 2}
            cy={numericSize / 2}
            r={radius - effectiveStrokeWidth / 2}
            fill="none"
            stroke={color}
            strokeWidth={1}
            strokeOpacity={0.3}
            strokeDasharray={strokeDasharray}
            initial={animated ? { strokeDashoffset: circumference } : { strokeDashoffset }}
            animate={{ strokeDashoffset }}
            transition={{
              duration: animated ? 1.5 : 0,
              ease: "easeInOut",
              delay: 0.2
            }}
          />
        </svg>
        
        {/* Center content */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          {showValue && (
            <motion.div
              className="text-center"
              initial={animated ? { opacity: 0, scale: 0.5 } : {}}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5, type: "spring", stiffness: 400, damping: 30 }}
            >
              <span className="text-2xl font-bold">{Math.round(percentage)}%</span>
              {showLabel && label && (
                <div className="text-xs text-muted-foreground mt-1">{label}</div>
              )}
            </motion.div>
          )}
        </div>
        
        {/* Pulse animation for completion */}
        {percentage >= 100 && (
          <motion.div
            className="absolute inset-0 rounded-full"
            style={{ 
              background: `radial-gradient(circle, ${color}20 0%, transparent 70%)`,
            }}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 0, 0.5]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        )}
      </div>
    )
  }
)
LuminarProgressRing.displayName = "LuminarProgressRing"

export { LuminarProgressRing }