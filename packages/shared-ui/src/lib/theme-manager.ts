/**
 * Advanced Theme Management System
 * Inspired by tweakcn's professional theme editing capabilities
 */

// Color format types
export type ColorFormat = 'hex' | 'rgb' | 'hsl' | 'oklch';

export interface RGB {
  r: number;
  g: number;
  b: number;
}

export interface HSL {
  h: number;
  s: number;
  l: number;
}

export interface OKLCH {
  l: number;
  c: number;
  h: number;
}

// Theme color definitions
export interface ThemeColors {
  background: string;
  foreground: string;
  card: string;
  cardForeground: string;
  popover: string;
  popoverForeground: string;
  primary: string;
  primaryForeground: string;
  secondary: string;
  secondaryForeground: string;
  muted: string;
  mutedForeground: string;
  accent: string;
  accentForeground: string;
  destructive: string;
  destructiveForeground: string;
  border: string;
  input: string;
  ring: string;
  chart1: string;
  chart2: string;
  chart3: string;
  chart4: string;
  chart5: string;
}

export interface ThemeConfig {
  name: string;
  colors: ThemeColors;
  radius: number;
  fontFamily: string;
  letterSpacing: number;
  spacing: number;
}

export interface ThemePreset {
  id: string;
  name: string;
  description: string;
  theme: ThemeConfig;
  category: 'light' | 'dark' | 'custom';
}

// Color conversion utilities
export class ColorConverter {
  static hexToRgb(hex: string): RGB | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  static rgbToHex(r: number, g: number, b: number): string {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  }

  static rgbToHsl(r: number, g: number, b: number): HSL {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0, s = 0, l = (max + min) / 2;

    if (max === min) {
      h = s = 0; // achromatic
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }

      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100)
    };
  }

  static hslToRgb(h: number, s: number, l: number): RGB {
    h /= 360;
    s /= 100;
    l /= 100;

    const hue2rgb = (p: number, q: number, t: number): number => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1/6) return p + (q - p) * 6 * t;
      if (t < 1/2) return q;
      if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
      return p;
    };

    let r, g, b;

    if (s === 0) {
      r = g = b = l; // achromatic
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  }

  static getContrastRatio(color1: string, color2: string): number {
    const getLuminance = (color: string): number => {
      const rgb = this.hexToRgb(color);
      if (!rgb) return 0;

      const rsRGB = rgb.r / 255;
      const gsRGB = rgb.g / 255;
      const bsRGB = rgb.b / 255;

      const r = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
      const g = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
      const b = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);

      return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    };

    const lum1 = getLuminance(color1);
    const lum2 = getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);

    return (brightest + 0.05) / (darkest + 0.05);
  }

  static formatColor(color: string, format: ColorFormat): string {
    const rgb = this.hexToRgb(color);
    if (!rgb) return color;

    switch (format) {
      case 'hex':
        return color;
      case 'rgb':
        return `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`;
      case 'hsl':
        const hsl = this.rgbToHsl(rgb.r, rgb.g, rgb.b);
        return `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`;
      case 'oklch':
        // Simplified OKLCH conversion (would need more complex conversion in production)
        const oklch = this.rgbToOklch(rgb.r, rgb.g, rgb.b);
        return `oklch(${oklch.l.toFixed(4)} ${oklch.c.toFixed(4)} ${oklch.h.toFixed(1)})`;
      default:
        return color;
    }
  }

  private static rgbToOklch(r: number, g: number, b: number): OKLCH {
    // Simplified conversion - in production, use a proper color space conversion library
    const hsl = this.rgbToHsl(r, g, b);
    return {
      l: hsl.l / 100,
      c: hsl.s / 100,
      h: hsl.h
    };
  }
}

// Theme management class
export class ThemeManager {
  private currentTheme: ThemeConfig;
  private presets: ThemePreset[] = [];

  constructor(initialTheme?: ThemeConfig) {
    this.currentTheme = initialTheme || this.getDefaultTheme();
    this.loadPresets();
  }

  private getDefaultTheme(): ThemeConfig {
    return {
      name: 'Default',
      colors: {
        background: '#ffffff',
        foreground: '#0a0a0a',
        card: '#ffffff',
        cardForeground: '#0a0a0a',
        popover: '#ffffff',
        popoverForeground: '#0a0a0a',
        primary: '#171717',
        primaryForeground: '#fafafa',
        secondary: '#f4f4f5',
        secondaryForeground: '#171717',
        muted: '#f4f4f5',
        mutedForeground: '#71717a',
        accent: '#f4f4f5',
        accentForeground: '#171717',
        destructive: '#ef4444',
        destructiveForeground: '#fafafa',
        border: '#e4e4e7',
        input: '#e4e4e7',
        ring: '#171717',
        chart1: '#e11d48',
        chart2: '#f59e0b',
        chart3: '#10b981',
        chart4: '#3b82f6',
        chart5: '#8b5cf6',
      },
      radius: 0.5,
      fontFamily: 'system-ui',
      letterSpacing: 0,
      spacing: 1,
    };
  }

  private loadPresets(): void {
    this.presets = [
      {
        id: 'default-light',
        name: 'Default Light',
        description: 'Clean and minimal light theme',
        category: 'light',
        theme: this.getDefaultTheme(),
      },
      {
        id: 'dark-slate',
        name: 'Dark Slate',
        description: 'Professional dark theme with slate colors',
        category: 'dark',
        theme: {
          ...this.getDefaultTheme(),
          name: 'Dark Slate',
          colors: {
            background: '#020817',
            foreground: '#f8fafc',
            card: '#020817',
            cardForeground: '#f8fafc',
            popover: '#020817',
            popoverForeground: '#f8fafc',
            primary: '#f8fafc',
            primaryForeground: '#0f172a',
            secondary: '#1e293b',
            secondaryForeground: '#f8fafc',
            muted: '#1e293b',
            mutedForeground: '#94a3b8',
            accent: '#1e293b',
            accentForeground: '#f8fafc',
            destructive: '#7f1d1d',
            destructiveForeground: '#f8fafc',
            border: '#1e293b',
            input: '#1e293b',
            ring: '#94a3b8',
            chart1: '#e11d48',
            chart2: '#f59e0b',
            chart3: '#10b981',
            chart4: '#3b82f6',
            chart5: '#8b5cf6',
          },
        },
      },
      {
        id: 'purple-gradient',
        name: 'Purple Gradient',
        description: 'Vibrant purple and pink gradient theme',
        category: 'custom',
        theme: {
          ...this.getDefaultTheme(),
          name: 'Purple Gradient',
          colors: {
            background: '#faf7ff',
            foreground: '#1a1625',
            card: '#ffffff',
            cardForeground: '#1a1625',
            popover: '#ffffff',
            popoverForeground: '#1a1625',
            primary: '#8b5cf6',
            primaryForeground: '#ffffff',
            secondary: '#f3f0ff',
            secondaryForeground: '#1a1625',
            muted: '#f3f0ff',
            mutedForeground: '#6b7280',
            accent: '#f3f0ff',
            accentForeground: '#1a1625',
            destructive: '#ef4444',
            destructiveForeground: '#ffffff',
            border: '#e5e7eb',
            input: '#e5e7eb',
            ring: '#8b5cf6',
            chart1: '#8b5cf6',
            chart2: '#ec4899',
            chart3: '#06b6d4',
            chart4: '#10b981',
            chart5: '#f59e0b',
          },
        },
      },
    ];
  }

  getCurrentTheme(): ThemeConfig {
    return { ...this.currentTheme };
  }

  setTheme(theme: ThemeConfig): void {
    this.currentTheme = { ...theme };
    this.applyTheme();
  }

  updateColor(colorKey: keyof ThemeColors, value: string): void {
    this.currentTheme.colors[colorKey] = value;
    this.applyTheme();
  }

  updateProperty(property: keyof Omit<ThemeConfig, 'colors'>, value: any): void {
    (this.currentTheme as any)[property] = value;
    this.applyTheme();
  }

  applyTheme(): void {
    const root = document.documentElement;
    
    // Apply CSS custom properties
    Object.entries(this.currentTheme.colors).forEach(([key, value]) => {
      const cssVar = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
      root.style.setProperty(cssVar, value);
    });

    // Apply other properties
    root.style.setProperty('--radius', `${this.currentTheme.radius}rem`);
    root.style.setProperty('--font-family', this.currentTheme.fontFamily);
    root.style.setProperty('--letter-spacing', `${this.currentTheme.letterSpacing}em`);
    root.style.setProperty('--spacing', `${this.currentTheme.spacing}rem`);
  }

  getPresets(): ThemePreset[] {
    return [...this.presets];
  }

  applyPreset(presetId: string): void {
    const preset = this.presets.find(p => p.id === presetId);
    if (preset) {
      this.setTheme(preset.theme);
    }
  }

  exportTheme(format: 'css' | 'json' | 'tailwind' = 'css'): string {
    switch (format) {
      case 'css':
        return this.exportAsCss();
      case 'json':
        return JSON.stringify(this.currentTheme, null, 2);
      case 'tailwind':
        return this.exportAsTailwind();
      default:
        return this.exportAsCss();
    }
  }

  private exportAsCss(): string {
    const cssVars = Object.entries(this.currentTheme.colors)
      .map(([key, value]) => {
        const cssVar = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
        return `  ${cssVar}: ${value};`;
      })
      .join('\n');

    return `:root {\n${cssVars}\n  --radius: ${this.currentTheme.radius}rem;\n  --font-family: ${this.currentTheme.fontFamily};\n  --letter-spacing: ${this.currentTheme.letterSpacing}em;\n  --spacing: ${this.currentTheme.spacing}rem;\n}`;
  }

  private exportAsTailwind(): string {
    const colors = Object.entries(this.currentTheme.colors).reduce((acc, [key, value]) => {
      acc[key] = value;
      return acc;
    }, {} as Record<string, string>);

    return `module.exports = {
  theme: {
    extend: {
      colors: ${JSON.stringify(colors, null, 6)},
      borderRadius: {
        DEFAULT: '${this.currentTheme.radius}rem',
      },
      fontFamily: {
        sans: ['${this.currentTheme.fontFamily}', 'sans-serif'],
      },
      letterSpacing: {
        DEFAULT: '${this.currentTheme.letterSpacing}em',
      },
      spacing: {
        DEFAULT: '${this.currentTheme.spacing}rem',
      },
    },
  },
}`;
  }

  importTheme(themeData: string, format: 'css' | 'json' = 'json'): boolean {
    try {
      if (format === 'json') {
        const parsed = JSON.parse(themeData);
        if (this.isValidThemeConfig(parsed)) {
          this.setTheme(parsed);
          return true;
        }
      }
      // TODO: Implement CSS parsing
      return false;
    } catch {
      return false;
    }
  }

  private isValidThemeConfig(config: any): config is ThemeConfig {
    return (
      config &&
      typeof config.name === 'string' &&
      config.colors &&
      typeof config.colors === 'object' &&
      typeof config.radius === 'number' &&
      typeof config.fontFamily === 'string'
    );
  }

  checkAccessibility(): Array<{
    pair: string;
    ratio: number;
    level: 'AA' | 'AAA' | 'FAIL';
    isLargeText?: boolean;
  }> {
    const results: Array<{
      pair: string;
      ratio: number;
      passes: boolean;
      isLargeText?: boolean;
    }> = [];
    const colors = this.currentTheme.colors;

    // Common color pairs to check
    const pairs = [
      { name: 'Primary/Primary Foreground', bg: colors.primary, fg: colors.primaryForeground },
      { name: 'Secondary/Secondary Foreground', bg: colors.secondary, fg: colors.secondaryForeground },
      { name: 'Background/Foreground', bg: colors.background, fg: colors.foreground },
      { name: 'Card/Card Foreground', bg: colors.card, fg: colors.cardForeground },
      { name: 'Muted/Muted Foreground', bg: colors.muted, fg: colors.mutedForeground },
    ];

    pairs.forEach(({ name, bg, fg }) => {
      const ratio = ColorConverter.getContrastRatio(bg, fg);
      let level: 'AA' | 'AAA' | 'FAIL' = 'FAIL';

      if (ratio >= 7) level = 'AAA';
      else if (ratio >= 4.5) level = 'AA';

      results.push({
        pair: name,
        ratio: Math.round(ratio * 100) / 100,
        level,
      });
    });

    return results;
  }
}

// Global theme manager instance
export const themeManager = new ThemeManager();