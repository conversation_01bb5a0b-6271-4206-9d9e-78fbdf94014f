import React from 'react';
import { getAdaptiveGlassClasses } from '../../../design-system';
import { LucideIcon } from 'lucide-react';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    label: string;
    direction: 'up' | 'down' | 'neutral';
  };
  className?: string;
}

export interface StatItem {
  label: string;
  value: string | number;
  icon?: LucideIcon | React.ComponentType<any>;
  trend?: {
    value: number;
    direction: 'up' | 'down' | 'neutral';
  };
  color?: 'primary' | 'success' | 'warning' | 'error';
  description?: string;
}

export interface StatsProps {
  stats: StatItem[];
  size?: ComponentSize | 'md' | 'lg';
  columns?: 1 | 2 | 3 | 4;
  glass?: boolean;
  loading?: boolean;
  className?: string;
}

export function LuminarStatCard({ 
  title, 
  value, 
  description, 
  icon, 
  trend,
  className = '' 
}: StatCardProps) {
  const glassClasses = getAdaptiveGlassClasses('card', 'md', 'neutral');

  const getTrendColor = (direction: 'up' | 'down' | 'neutral') => {
    switch (direction) {
      case 'up': return 'text-green-600 dark:text-green-400';
      case 'down': return 'text-red-600 dark:text-red-400';
      case 'neutral': return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getTrendIcon = (direction: 'up' | 'down' | 'neutral') => {
    switch (direction) {
      case 'up': return '↗';
      case 'down': return '↘';
      case 'neutral': return '→';
    }
  };

  return (
    <div className={`${glassClasses} p-6 rounded-lg border ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
        {icon && <div className="text-muted-foreground">{icon}</div>}
      </div>
      
      <div className="space-y-2">
        <div className="text-2xl font-bold text-foreground">{value}</div>
        
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
        
        {trend && (
          <div className={`flex items-center gap-1 text-sm ${getTrendColor(trend.direction)}`}>
            <span>{getTrendIcon(trend.direction)}</span>
            <span>{trend.value}%</span>
            <span className="text-muted-foreground">{trend.label}</span>
          </div>
        )}
      </div>
    </div>
  );
}

export interface StatsGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

export function LuminarStatsGrid({ 
  children, 
  columns = 3, 
  className = '' 
}: StatsGridProps) {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  return (
    <div className={`grid ${gridClasses[columns]} gap-6 ${className}`}>
      {children}
    </div>
  );
}

export function LuminarStats({
  stats,
  size = 'md',
  columns = 4,
  glass = false,
  loading = false,
  className = ''
}: StatsProps) {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  const sizeClasses = {
    xs: 'p-3',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  const getColorClasses = (color: StatItem['color']) => {
    switch (color) {
      case 'primary':
        return 'text-blue-600 dark:text-blue-400';
      case 'success':
        return 'text-green-600 dark:text-green-400';
      case 'warning':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'error':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getTrendColor = (direction: 'up' | 'down' | 'neutral') => {
    switch (direction) {
      case 'up': return 'text-green-600 dark:text-green-400';
      case 'down': return 'text-red-600 dark:text-red-400';
      case 'neutral': return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getTrendIcon = (direction: 'up' | 'down' | 'neutral') => {
    switch (direction) {
      case 'up': return '↗';
      case 'down': return '↘';
      case 'neutral': return '→';
    }
  };

  if (loading) {
    return (
      <div className={`grid ${gridClasses[columns]} gap-6 ${className}`}>
        {Array.from({ length: stats.length }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className={`${glass ? getAdaptiveGlassClasses('card', 'md', 'neutral') : 'bg-card'} ${sizeClasses[size]} rounded-lg border`}>
              <div className="flex items-center justify-between mb-4">
                <div className="h-4 bg-gray-300 rounded w-1/3"></div>
                <div className="h-5 w-5 bg-gray-300 rounded"></div>
              </div>
              <div className="space-y-2">
                <div className="h-8 bg-gray-300 rounded w-2/3"></div>
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={`grid ${gridClasses[columns]} gap-6 ${className}`}>
      {stats.map((stat, index) => {
        const Icon = stat.icon;
        const glassClasses = glass ? getAdaptiveGlassClasses('card', 'md', 'neutral') : 'bg-card';
        
        return (
          <div key={index} className={`${glassClasses} ${sizeClasses[size]} rounded-lg border`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-muted-foreground">{stat.label}</h3>
              {Icon && (
                <div className={`${getColorClasses(stat.color)}`}>
                  {React.isValidElement(Icon) ? Icon : <Icon className="h-5 w-5" />}
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <div className="text-2xl font-bold text-foreground">{stat.value}</div>
              
              {stat.description && (
                <p className="text-sm text-muted-foreground">{stat.description}</p>
              )}
              
              {stat.trend && (
                <div className={`flex items-center gap-1 text-sm ${getTrendColor(stat.trend.direction)}`}>
                  <span>{getTrendIcon(stat.trend.direction)}</span>
                  <span>{stat.trend.value}%</span>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}

// Compound component exports for backward compatibility
LuminarStats.Card = LuminarStatCard;
LuminarStats.Grid = LuminarStatsGrid;

// Default export
export default LuminarStats;