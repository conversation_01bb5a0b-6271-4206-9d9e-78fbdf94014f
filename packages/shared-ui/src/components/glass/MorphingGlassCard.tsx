/**
 * @file MorphingGlassCard.tsx
 * @description Advanced morphing glass card component demonstrating dynamic shape-shifting
 * glassmorphism effects with proper TypeScript integration and accessibility support.
 * 
 * This component showcases:
 * - Dynamic border-radius morphing on hover and interaction
 * - Smooth backdrop-filter transitions
 * - Integration with existing glass system (createGlassmorphism, microInteractions)
 * - Performance optimizations with GPU acceleration
 * - Accessibility compliance with reduced motion support
 */

import React, { useState, useCallback, useRef } from 'react';
import { motion, type Variants, type MotionProps } from 'framer-motion';
import { createGlassmorphism, type GlassConfig, type GlassProfile } from '../../lib/glassmorphism';
import { microInteractions, useReducedMotion } from '../../lib/micro-interactions';
import { type ColorTheme } from '../../design-system';
import { cn } from '../../lib/utils';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../types/component-props';
import { defaultComponentProps } from '../../types/component-props';

// --- TypeScript Interfaces ---

interface MorphingGlassCardProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onAnimationComplete'> {
  /** Glass profile intensity */
  glassProfile?: GlassProfile;
  /** Morphing animation intensity */
  morphingIntensity?: 'low' | 'medium' | 'high';
  /** Color theme integration */
  theme?: ColorTheme;
  /** Morphing trigger method */
  morphingTrigger?: 'hover' | 'click' | 'auto' | 'focus';
  /** Auto-morphing interval in milliseconds (when trigger is 'auto') */
  autoMorphInterval?: number;
  /** Enable interactive morphing sequence */
  enableSequence?: boolean;
  /** Callback when morphing animation completes */
  onMorphComplete?: (sequence: MorphingSequence) => void;
  /** Callback when morphing starts */
  onMorphStart?: (sequence: MorphingSequence) => void;
  /** Children content */
  children?: React.ReactNode;
}
type MorphingSequence = 'idle' | 'hover' | 'morphing' | 'focus' | 'active';

interface MorphingState {
  currentSequence: MorphingSequence;
  isAnimating: boolean;
  morphCount: number;
}

// --- Animation Variants ---

const createMorphingVariants = (intensity: 'low' | 'medium' | 'high'): Record<MorphingSequence, any> => {
  const intensityMultipliers = {
    low: { scale: 0.5, rotation: 0.3, radius: 0.6 },
    medium: { scale: 1, rotation: 1, radius: 1 },
    high: { scale: 1.5, rotation: 1.8, radius: 1.4 }
  };

  const multiplier = intensityMultipliers[intensity];

  return {
    idle: {
      borderRadius: '12px',
      scale: 1,
      rotateY: 0,
      rotateX: 0,
      backdropFilter: 'blur(12px) saturate(150%)',
      transition: { 
        duration: 0.4, 
        ease: [0.25, 0.46, 0.45, 0.94] // Custom cubic-bezier for smooth feel
      }
    },
    hover: {
      borderRadius: `${16 * multiplier.radius}px`,
      scale: 1 + (0.02 * multiplier.scale),
      y: -2 * multiplier.scale,
      rotateY: 2 * multiplier.rotation,
      backdropFilter: 'blur(16px) saturate(175%)',
      transition: { 
        duration: 0.3, 
        ease: 'easeOut'
      }
    },
    focus: {
      borderRadius: `${20 * multiplier.radius}px`,
      scale: 1 + (0.01 * multiplier.scale),
      backdropFilter: 'blur(14px) saturate(160%)',
      boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.3), 0 8px 32px rgba(0, 0, 0, 0.12)',
      transition: { 
        duration: 0.2, 
        ease: 'easeOut'
      }
    },
    active: {
      borderRadius: `${8 * multiplier.radius}px`,
      scale: 1 - (0.02 * multiplier.scale),
      backdropFilter: 'blur(10px) saturate(140%)',
      transition: { 
        duration: 0.1, 
        ease: 'easeInOut'
      }
    },
    morphing: {
      borderRadius: [
        '12px', 
        `${24 * multiplier.radius}px`, 
        `${48 * multiplier.radius}px`, 
        `${32 * multiplier.radius}px`, 
        '12px'
      ],
      scale: [
        1, 
        1 + (0.03 * multiplier.scale), 
        1 - (0.01 * multiplier.scale), 
        1 + (0.01 * multiplier.scale), 
        1
      ],
      rotateY: [0, 5 * multiplier.rotation, -3 * multiplier.rotation, 2 * multiplier.rotation, 0],
      rotateX: [0, 2 * multiplier.rotation, -1 * multiplier.rotation, 0.5 * multiplier.rotation, 0],
      backdropFilter: [
        'blur(12px) saturate(150%)',
        'blur(18px) saturate(180%)',
        'blur(24px) saturate(200%)',
        'blur(16px) saturate(170%)',
        'blur(12px) saturate(150%)'
      ],
      transition: { 
        duration: 2.5, 
        ease: 'easeInOut',
        times: [0, 0.25, 0.5, 0.75, 1]
      }
    }
  };
};

// --- Main Component ---

export const MorphingGlassCard: React.FC<MorphingGlassCardProps> = ({
  glassProfile = 'standard',
  morphingIntensity = 'md',
  theme = 'neutral',
  morphingTrigger = 'hover',
  autoMorphInterval = 5000,
  enableSequence = true,
  onMorphComplete,
  onMorphStart,
  children,
  className,
  onFocus,
  onBlur,
  onClick,
  onMouseEnter,
  onMouseLeave,
  ...props
}) => {
  // --- State Management ---
  const [morphingState, setMorphingState] = useState<MorphingState>({
    currentSequence: 'idle',
    isAnimating: false,
    morphCount: 0
  });

  const reducedMotion = useReducedMotion();
  const autoMorphTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  // --- Glass Configuration ---
  const glassConfig: GlassConfig = {
    profile: glassProfile,
    element: 'card',
    interactive: !reducedMotion,
    glow: true,
    frost: true,
    theme,
    textSafe: true
  };

  const glassClasses = createGlassmorphism(glassConfig);
  const morphingVariants = createMorphingVariants(morphingIntensity);

  // --- Animation Handlers ---
  const triggerMorphing = useCallback((newSequence: MorphingSequence) => {
    if (reducedMotion || morphingState.isAnimating) return;

    setMorphingState(prev => ({
      ...prev,
      currentSequence: newSequence,
      isAnimating: true,
      morphCount: newSequence === 'morphing' ? prev.morphCount + 1 : prev.morphCount
    }));

    onMorphStart?.(newSequence);

    // Clear any existing auto-morph timeout
    if (autoMorphTimeoutRef.current) {
      clearTimeout(autoMorphTimeoutRef.current);
    }
  }, [reducedMotion, morphingState.isAnimating, onMorphStart]);

  const handleAnimationComplete = useCallback(() => {
    setMorphingState(prev => ({
      ...prev,
      isAnimating: false
    }));

    onMorphComplete?.(morphingState.currentSequence);

    // Setup auto-morphing if enabled
    if (morphingTrigger === 'auto' && !reducedMotion) {
      autoMorphTimeoutRef.current = setTimeout(() => {
        triggerMorphing('morphing');
      }, autoMorphInterval);
    }
  }, [morphingState.currentSequence, morphingTrigger, autoMorphInterval, reducedMotion, onMorphComplete, triggerMorphing]);

  // --- Event Handlers ---
  const handleMouseEnter = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (morphingTrigger === 'hover') {
      triggerMorphing('hover');
    }
    onMouseEnter?.(e);
  }, [morphingTrigger, triggerMorphing, onMouseEnter]);

  const handleMouseLeave = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (morphingTrigger === 'hover') {
      triggerMorphing('idle');
    }
    onMouseLeave?.(e);
  }, [morphingTrigger, triggerMorphing, onMouseLeave]);

  const handleClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (morphingTrigger === 'click') {
      triggerMorphing(enableSequence ? 'morphing' : 'active');
    }
    onClick?.(e);
  }, [morphingTrigger, enableSequence, triggerMorphing, onClick]);

  const handleFocus = useCallback((e: React.FocusEvent<HTMLDivElement>) => {
    if (morphingTrigger === 'focus') {
      triggerMorphing('focus');
    }
    onFocus?.(e);
  }, [morphingTrigger, triggerMorphing, onFocus]);

  const handleBlur = useCallback((e: React.FocusEvent<HTMLDivElement>) => {
    if (morphingTrigger === 'focus') {
      triggerMorphing('idle');
    }
    onBlur?.(e);
  }, [morphingTrigger, triggerMorphing, onBlur]);

  // --- Effects ---
  React.useEffect(() => {
    // Initialize auto-morphing
    if (morphingTrigger === 'auto' && !reducedMotion) {
      autoMorphTimeoutRef.current = setTimeout(() => {
        triggerMorphing('morphing');
      }, autoMorphInterval);
    }

    // Cleanup on unmount
    return () => {
      if (autoMorphTimeoutRef.current) {
        clearTimeout(autoMorphTimeoutRef.current);
      }
    };
  }, [morphingTrigger, autoMorphInterval, reducedMotion, triggerMorphing]);

  // --- Accessibility ---
  const accessibilityProps = {
    role: 'button',
    tabIndex: morphingTrigger === 'focus' ? 0 : -1,
    'aria-label': `Morphing glass card${morphingState.morphCount > 0 ? ` (morphed ${morphingState.morphCount} times)` : ''}`,
    'aria-live': 'polite' as const,
    'aria-atomic': true,
    'data-morphing-state': morphingState.currentSequence,
    'data-morph-count': morphingState.morphCount
  };

  // --- Performance Optimizations ---
  const motionProps: MotionProps = {
    initial: 'idle',
    animate: morphingState.currentSequence,
    variants: morphingVariants,
    onAnimationComplete: handleAnimationComplete,
    style: {
      willChange: 'transform, border-radius, backdrop-filter',
      backfaceVisibility: 'hidden',
      perspective: 1000,
      transformStyle: 'preserve-3d'
    },
    ...(reducedMotion && {
      transition: { duration: 0 }
    })
  };

  // Separate HTML props from motion props to avoid conflicts
  // Remove props that conflict between HTML and Motion
  const {
    onDrag,
    onDragStart,
    onDragEnd,
    onAnimationStart,
    ...htmlProps
  } = props;

  // --- Render ---
  return (
    <motion.div
      ref={cardRef}
      className={cn(
        // Base glass styles
        glassClasses,
        // Layout and positioning
        'relative overflow-hidden',
        // Interactive states
        'cursor-pointer select-none',
        // Performance optimizations
        'transform-gpu will-change-transform',
        // Custom styles
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      onFocus={handleFocus}
      onBlur={handleBlur}
      {...accessibilityProps}
      {...motionProps}
      {...htmlProps}
    >
      {/* Glass overlay for enhanced depth */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        style={{
          background: `linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 50%,
            rgba(255, 255, 255, 0.02) 100%
          )`,
          borderRadius: 'inherit'
        }}
        variants={{
          idle: { opacity: 0.6 },
          hover: { opacity: 0.8 },
          focus: { opacity: 0.7 },
          active: { opacity: 0.5 },
          morphing: {
            opacity: [0.6, 0.9, 0.4, 0.7, 0.6],
            transition: { duration: 2.5, times: [0, 0.25, 0.5, 0.75, 1] }
          }
        }}
      />

      {/* Content container */}
      <div className="relative z-10 h-full w-full">
        {children}
      </div>

      {/* Morphing indicator (for debugging/development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-2 right-2 text-xs opacity-50 pointer-events-none">
          {morphingState.currentSequence}
          {morphingState.morphCount > 0 && ` (${morphingState.morphCount})`}
        </div>
      )}
    </motion.div>
  );
};

// --- Display Name ---
MorphingGlassCard.displayName = 'MorphingGlassCard';

// --- Default Export ---
export default MorphingGlassCard;

// --- Additional Exports ---
export type { MorphingGlassCardProps, MorphingSequence };
