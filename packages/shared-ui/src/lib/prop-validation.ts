/**
 * Prop Validation Utilities
 * 
 * Provides runtime validation and type checking utilities for standardized component props.
 * These utilities help ensure components receive valid props and provide helpful error messages.
 */

import * as React from 'react';
import type {
  ComponentSize,
  ComponentVariant,
  GlassIntensity,
  GlassDepth,
  AnimationPreset,
  LoadingState,
  ColorTheme,
  BaseComponentProps,
  VariantProps,
  FormComponentProps,
  StandardComponentProps,
  StandardFormComponentProps
} from '../types/component-props';

// =============================================================================
// Type Validators
// =============================================================================

/**
 * Validates if a value is a valid component size
 */
export const isValidComponentSize = (value: unknown): value is ComponentSize => {
  return typeof value === 'string' && ['xs', 'sm', 'md', 'lg', 'xl'].includes(value);
};

/**
 * Validates if a value is a valid component variant
 */
export const isValidComponentVariant = (value: unknown): value is ComponentVariant => {
  const validVariants = [
    'default', 'primary', 'secondary', 'outline', 'ghost', 'glass', 
    'gradient', 'destructive', 'success', 'warning', 'info'
  ];
  return typeof value === 'string' && validVariants.includes(value);
};

/**
 * Validates if a value is a valid glass intensity
 */
export const isValidGlassIntensity = (value: unknown): value is GlassIntensity => {
  return typeof value === 'string' && ['subtle', 'light', 'medium', 'strong', 'intense'].includes(value);
};

/**
 * Validates if a value is a valid glass depth
 */
export const isValidGlassDepth = (value: unknown): value is GlassDepth => {
  return typeof value === 'string' && ['surface', 'elevated', 'floating', 'modal'].includes(value);
};

/**
 * Validates if a value is a valid animation preset
 */
export const isValidAnimationPreset = (value: unknown): value is AnimationPreset => {
  const validPresets = [
    'fadeIn', 'slideUp', 'slideDown', 'slideLeft', 'slideRight',
    'scale', 'rotate', 'bounce', 'pulse', 'wiggle', 'none'
  ];
  return typeof value === 'string' && validPresets.includes(value);
};

/**
 * Validates if a value is a valid loading state
 */
export const isValidLoadingState = (value: unknown): value is LoadingState => {
  return typeof value === 'string' && ['idle', 'loading', 'success', 'error'].includes(value);
};

/**
 * Validates if a value is a valid color theme
 */
export const isValidColorTheme = (value: unknown): value is ColorTheme => {
  const validThemes = [
    'neutral', 'primary', 'secondary', 'accent', 
    'success', 'warning', 'error', 'info'
  ];
  return typeof value === 'string' && validThemes.includes(value);
};

// =============================================================================
// Prop Validation Functions
// =============================================================================

/**
 * Validates base component props
 */
export const validateBaseComponentProps = (props: Partial<BaseComponentProps>): boolean => {
  const errors: string[] = [];

  // Validate className
  if (props.className !== undefined && typeof props.className !== 'string') {
    errors.push('className must be a string');
  }

  // Validate id
  if (props.id !== undefined && typeof props.id !== 'string') {
    errors.push('id must be a string');
  }

  // Validate data-testid
  if (props['data-testid'] !== undefined && typeof props['data-testid'] !== 'string') {
    errors.push('data-testid must be a string');
  }

  // Validate aria-label
  if (props['aria-label'] !== undefined && typeof props['aria-label'] !== 'string') {
    errors.push('aria-label must be a string');
  }

  // Validate aria-describedby
  if (props['aria-describedby'] !== undefined && typeof props['aria-describedby'] !== 'string') {
    errors.push('aria-describedby must be a string');
  }

  if (errors.length > 0) {
    console.warn('BaseComponentProps validation errors:', errors);
    return false;
  }

  return true;
};

/**
 * Validates variant props
 */
export const validateVariantProps = (props: Partial<VariantProps>): boolean => {
  const errors: string[] = [];

  // Validate variant
  if (props.variant !== undefined && !isValidComponentVariant(props.variant)) {
    errors.push(`variant must be one of: default, primary, secondary, outline, ghost, glass, gradient, destructive, success, warning, info`);
  }

  // Validate size
  if (props.size !== undefined && !isValidComponentSize(props.size)) {
    errors.push(`size must be one of: xs, sm, md, lg, xl`);
  }

  if (errors.length > 0) {
    console.warn('VariantProps validation errors:', errors);
    return false;
  }

  return true;
};

/**
 * Validates form component props
 */
export const validateFormComponentProps = <T>(props: Partial<FormComponentProps<T>>): boolean => {
  const errors: string[] = [];

  // Validate name
  if (props.name !== undefined && typeof props.name !== 'string') {
    errors.push('name must be a string');
  }

  // Validate required
  if (props.required !== undefined && typeof props.required !== 'boolean') {
    errors.push('required must be a boolean');
  }

  // Validate placeholder
  if (props.placeholder !== undefined && typeof props.placeholder !== 'string') {
    errors.push('placeholder must be a string');
  }

  // Validate label
  if (props.label !== undefined && typeof props.label !== 'string') {
    errors.push('label must be a string');
  }

  // Validate helperText
  if (props.helperText !== undefined && typeof props.helperText !== 'string') {
    errors.push('helperText must be a string');
  }

  // Validate error
  if (props.error !== undefined && typeof props.error !== 'string') {
    errors.push('error must be a string');
  }

  // Validate showError
  if (props.showError !== undefined && typeof props.showError !== 'boolean') {
    errors.push('showError must be a boolean');
  }

  if (errors.length > 0) {
    console.warn('FormComponentProps validation errors:', errors);
    return false;
  }

  return true;
};

/**
 * Validates standard component props
 */
export const validateStandardComponentProps = (props: Partial<StandardComponentProps>): boolean => {
  return validateBaseComponentProps(props) && validateVariantProps(props);
};

/**
 * Validates standard form component props
 */
export const validateStandardFormComponentProps = <T>(props: Partial<StandardFormComponentProps<T>>): boolean => {
  return validateStandardComponentProps(props) && validateFormComponentProps(props);
};

// =============================================================================
// Prop Sanitization
// =============================================================================

/**
 * Sanitizes component props by removing invalid values and providing defaults
 */
export const sanitizeComponentProps = <T extends StandardComponentProps>(
  props: Partial<T>
): T => {
  const sanitized = { ...props } as T;

  // Sanitize variant
  if (sanitized.variant && !isValidComponentVariant(sanitized.variant)) {
    console.warn(`Invalid variant "${sanitized.variant}", falling back to "default"`);
    sanitized.variant = 'default';
  }

  // Sanitize size
  if (sanitized.size && !isValidComponentSize(sanitized.size)) {
    console.warn(`Invalid size "${sanitized.size}", falling back to "md"`);
    sanitized.size = 'md';
  }

  // Sanitize glass intensity
  if (sanitized.glassIntensity && !isValidGlassIntensity(sanitized.glassIntensity)) {
    console.warn(`Invalid glassIntensity "${sanitized.glassIntensity}", falling back to "medium"`);
    sanitized.glassIntensity = 'medium';
  }

  // Sanitize animation preset
  if (sanitized.animation && !isValidAnimationPreset(sanitized.animation)) {
    console.warn(`Invalid animation "${sanitized.animation}", falling back to "fadeIn"`);
    sanitized.animation = 'fadeIn';
  }

  return sanitized;
};

// =============================================================================
// Development Mode Helpers
// =============================================================================

/**
 * Enables prop validation in development mode
 */
export const enablePropValidation = import.meta.env.DEV;

/**
 * Validates props only in development mode
 */
export const validatePropsInDev = <T>(
  validator: (props: T) => boolean,
  props: T,
  componentName: string
): void => {
  if (enablePropValidation) {
    const isValid = validator(props);
    if (!isValid) {
      console.warn(`${componentName}: Invalid props detected. Check the console for details.`);
    }
  }
};

/**
 * Creates a higher-order component that validates props
 */
export const withPropValidation = <P extends StandardComponentProps>(
  Component: React.ComponentType<P>,
  validator: (props: P) => boolean
) => {
  const ValidatedComponent = (props: P) => {
    if (enablePropValidation) {
      validator(props);
    }
    return React.createElement(Component, props);
  };

  ValidatedComponent.displayName = `WithPropValidation(${Component.displayName || Component.name})`;
  return ValidatedComponent;
};

// =============================================================================
// Migration Helpers
// =============================================================================

/**
 * Migrates old prop format to new standardized format
 */
export const migrateProps = <T extends Record<string, any>>(
  oldProps: T,
  migrationMap: Record<string, string>
): Partial<StandardComponentProps> => {
  const newProps: Partial<StandardComponentProps> = {};

  Object.entries(oldProps).forEach(([key, value]) => {
    const newKey = migrationMap[key] || key;
    (newProps as any)[newKey] = value;
  });

  return newProps;
};

/**
 * Common migration mappings for legacy props
 */
export const commonMigrationMaps = {
  // Button legacy props
  button: {
    'color': 'variant',
    'isLoading': 'loading',
    'isDisabled': 'disabled',
    'leftIcon': 'icon',
    'rightIcon': 'icon',
    'iconPosition': 'iconPosition'
  },
  
  // Input legacy props
  input: {
    'isInvalid': 'error',
    'isRequired': 'required',
    'isDisabled': 'disabled',
    'helperText': 'helperText',
    'errorMessage': 'error'
  },
  
  // Card legacy props
  card: {
    'shadow': 'elevation',
    'isClickable': 'clickable',
    'borderRadius': 'variant'
  }
};

/**
 * Validates prop combinations and provides warnings
 */
export const validatePropCombinations = (props: Partial<StandardComponentProps>): void => {
  // Warn about conflicting glass and variant props
  if (props.glass && props.variant === 'glass') {
    console.warn('Both glass prop and glass variant are set. Consider using only one.');
  }

  // Warn about animation conflicts
  if (props.disableAnimation && props.animation && props.animation !== 'none') {
    console.warn('Animation is disabled but animation preset is set. Animation will be ignored.');
  }

  // Warn about interactive conflicts
  if (props.interactive === false && props.hoverable === true) {
    console.warn('Component is not interactive but hoverable is enabled. Hover effects may not work as expected.');
  }
};

// =============================================================================
// Type-safe prop merging
// =============================================================================

/**
 * Merges props with type safety and validation
 */
export const mergeProps = <T extends StandardComponentProps>(
  defaultProps: Partial<T>,
  userProps: Partial<T>
): T => {
  const merged = { ...defaultProps, ...userProps } as T;
  
  if (enablePropValidation) {
    validateStandardComponentProps(merged);
    validatePropCombinations(merged);
  }
  
  return sanitizeComponentProps(merged) as T;
};