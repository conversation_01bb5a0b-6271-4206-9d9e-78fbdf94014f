import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, InputHTMLAttributes, useState, FocusEventHandler, ChangeEventHandler } from "react"
import { LuminarIcon } from '../utilities/icon';
import { LucideIcon } from "lucide-react"
import {
  type GlassVariant,
  animationPresets,
  transitions,
  componentSizes
} from '../../../design-system'
import { createGlassStyles, focusGlassStyles } from '../../../lib/glass-utils'
import {
  type InputBaseProps,
  type ExtendedHTMLProps,
  defaultFormProps,
  sizeToPixels,
  sizeToSpacing
} from '../../../types/component-props';

/**
 * Input component props extending the standardized interface
 */
export interface LuminarInputProps extends 
  Omit<InputBaseProps, 'autoComplete'>,
  ExtendedHTMLProps<InputHTMLAttributes<HTMLInputElement>, {
    icon?: LucideIcon;
    iconPosition?: 'left' | 'right';
    iconSize?: number;
    iconAnimation?: string;
  }> {
  /**
   * Whether to show focus ring
   */
  showFocusRing?: boolean
  
  /**
   * Whether to enable performance monitoring
   */
  enablePerformanceMonitoring?: boolean
  
  /**
   * Legacy glass variant support (use variant prop instead)
   * @deprecated Use variant prop instead
   */
  variant?: GlassVariant
}

/**
 * Standardized Input Component
 * 
 * Implements the complete standardized props interface for consistent API
 * across all Luminar UI components.
 */
const LuminarInput = forwardRef<HTMLInputElement, LuminarInputProps>(
  ({
    // Standard component props
    className,
    id,
    style,
    'data-testid': dataTestId,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy,
    
    // Form component props
    value,
    defaultValue,
    onChange,
    onBlur,
    onFocus,
    name,
    required = defaultFormProps.required,
    placeholder,
    label,
    helperText,
    error,
    showError = defaultFormProps.showError,
    
    // Variant props
    variant = "input", // Legacy support
    size = defaultFormProps.size,
    
    // Glass props
    glass = defaultFormProps.glass,
    glassIntensity = defaultFormProps.glassIntensity,
    glassDepth,
    glassConfig,
    
    // Animation props
    animation = defaultFormProps.animation,
    disableAnimation = defaultFormProps.disableAnimation,
    animationDuration,
    animationDelay,
    motionProps,
    
    // Interactive props
    disabled = false,
    interactive = defaultFormProps.interactive,
    
    // Icon props
    icon: Icon,
    iconPosition = 'left',
    iconSize,
    iconAnimation,
    
    // Input specific props
    type = 'text',
    autoComplete,
    pattern,
    minLength,
    maxLength,
    
    // Additional props
    showFocusRing = true,
    enablePerformanceMonitoring = false,
    
    ...props
  }, ref) => {
    const [focused, setIsFocused] = useState(false)

    // Calculate icon size based on component size
    const calculatedIconSize = iconSize || (size ? sizeToPixels[size] * 0.6 : 24)
    
    // Use enhanced glass system
    const glassClasses = glass ? createGlassStyles({
      element: 'input',
      profile: glassIntensity === 'subtle' ? 'soft' : glassIntensity === 'strong' ? 'hard' : 'standard',
      interactive,
      frost: true,
      glow: false,
      ...glassConfig
    }) : ''
    
    const sizeClasses = size ? componentSizes[size] : componentSizes.md
    
    // Get animation configuration
    const animationProps = disableAnimation || animation === 'none' ? {} : {
      ...(animation ? animationPresets[animation] || animationPresets.fadeIn : animationPresets.fadeIn),
      transition: {
        ...transitions.default,
        duration: animationDuration || transitions.default.duration,
        delay: animationDelay || 0
      }
    }

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true)
      if (onFocus) {
        // Handle both patterns based on function length
        if (onFocus.length === 0) {
          (onFocus as () => void)()
        } else {
          (onFocus as FocusEventHandler<HTMLInputElement>)(e)
        }
      }
    }

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false)
      if (onBlur) {
        // Handle both patterns based on function length
        if (onBlur.length === 0) {
          (onBlur as () => void)()
        } else {
          (onBlur as FocusEventHandler<HTMLInputElement>)(e)
        }
      }
    }

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        // Handle both patterns based on function length
        if (onChange.length === 1 && typeof onChange === 'function') {
          // Check if it's expecting a string or an event
          try {
            (onChange as (value: string) => void)(e.target.value)
          } catch {
            (onChange as ChangeEventHandler<HTMLInputElement>)(e)
          }
        } else {
          (onChange as ChangeEventHandler<HTMLInputElement>)(e)
        }
      }
    }

    return (
      <div className="w-full">
        {label && (
          <motion.label
            className="block text-sm font-medium mb-2"
            {...animationProps}
            transition={{ duration: 0.15, ease: "easeInOut" as const }}
          >
            {label}
            {required && <span className="text-destructive ml-1">*</span>}
          </motion.label>
        )}
        
        <motion.div
          className={cn(
            "relative rounded-lg overflow-hidden transition-all duration-300",
            glass && glassClasses,
            focused && showFocusRing
              ? "border-primary shadow-[0_0_0_2px_rgba(var(--primary),0.2)]" 
              : error && showError
                ? "border-destructive" 
                : glass ? "" : "border-white/20 dark:border-gray-700/30",
            focusGlassStyles(focused),
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
          animate={{
            scale: focused ? 1.01 : 1,
          }}
          transition={{ duration: 0.15, ease: "easeInOut" as const }}
          style={style}
          {...motionProps}
        >
          {Icon && iconPosition === "left" && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2">
              <LuminarIcon
                icon={Icon}
                size={calculatedIconSize}
                animation={focused && iconAnimation ? iconAnimation : focused ? "pulse" : "none"}
                trigger="none"
                className="text-muted-foreground"
              />
            </div>
          )}
          
          <input
            ref={ref}
            id={id}
            name={name}
            type={type}
            value={value}
            defaultValue={defaultValue}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            autoComplete={autoComplete}
            pattern={pattern}
            minLength={minLength}
            maxLength={maxLength}
            className={cn(
              "w-full bg-transparent outline-none placeholder:text-muted-foreground",
              sizeClasses.padding,
              sizeClasses.text,
              Icon && iconPosition === "left" && "pl-12",
              Icon && iconPosition === "right" && "pr-12"
            )}
            data-testid={dataTestId}
            aria-label={ariaLabel}
            aria-describedby={ariaDescribedBy}
            {...props}
          />
          
          {Icon && iconPosition === "right" && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <LuminarIcon
                icon={Icon}
                size={calculatedIconSize}
                animation={focused && iconAnimation ? iconAnimation : focused ? "pulse" : "none"}
                trigger="none"
                className="text-muted-foreground"
              />
            </div>
          )}
        </motion.div>
        
        {/* Helper text */}
        {helperText && !error && (
          <motion.p
            className="mt-2 text-sm text-muted-foreground"
            {...animationProps}
            transition={{ duration: 0.15, ease: "easeInOut" as const }}
          >
            {helperText}
          </motion.p>
        )}
        
        {/* Error message */}
        {error && showError && (
          <motion.p
            className="mt-2 text-sm text-destructive"
            {...animationProps}
            transition={{ duration: 0.15, ease: "easeInOut" as const }}
          >
            {error}
          </motion.p>
        )}
      </div>
    )
  }
)
LuminarInput.displayName = "LuminarInput"

export { LuminarInput }