import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { usersApi } from '../../api';
import { queryKeys, cacheUtils } from '../client';
import { toast } from '../../toast';
import type {
  UserProfile,
  UserActivity,
  UserSession,
  UserRole,
  Department,
  UserStats,
  CreateUserDto,
  UpdateUserDto,
  UpdateUserPreferencesDto,
  UserChangePasswordDto,
  UserFilters,
  BulkUserAction,
  CreateRoleDto,
  UpdateRoleDto,
  CreateDepartmentDto,
  UpdateDepartmentDto,
} from '../../api';

// User Management Hooks
export function useUsers(filters?: UserFilters) {
  return useQuery({
    queryKey: queryKeys.users.list(filters),
    queryFn: () => usersApi.getUsers(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes for user lists
  });
}

export function useInfiniteUsers(filters?: UserFilters) {
  return useInfiniteQuery({
    queryKey: queryKeys.users.list(filters),
    queryFn: ({ pageParam = 1 }) => 
      usersApi.getUsers({ ...filters, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      const currentPage = allPages.length;
      const totalPages = lastPage.pagination?.totalPages || 1;
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    staleTime: 2 * 60 * 1000,
  });
}

export function useUserQuery(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.users.detail(id),
    queryFn: () => usersApi.getUser(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes for user details
  });
}

export function useCurrentUser() {
  return useQuery({
    queryKey: queryKeys.users.current(),
    queryFn: () => usersApi.getCurrentUser(),
    staleTime: 5 * 60 * 1000,
  });
}

export function useUserActivity(userId?: string, filters?: any) {
  return useQuery({
    queryKey: queryKeys.users.activity(userId),
    queryFn: () => usersApi.getUserActivity(userId, filters),
    staleTime: 1 * 60 * 1000, // 1 minute for activity
  });
}

export function useUserSessions() {
  return useQuery({
    queryKey: queryKeys.users.sessions(),
    queryFn: () => usersApi.getUserSessions(),
    staleTime: 1 * 60 * 1000,
  });
}

export function useUserNotifications() {
  return useQuery({
    queryKey: queryKeys.users.notifications(),
    queryFn: () => usersApi.getUserNotifications(),
    staleTime: 30 * 1000, // 30 seconds for notifications
  });
}

export function useUserStats(filters?: any) {
  return useQuery({
    queryKey: queryKeys.users.stats(filters),
    queryFn: () => usersApi.getUserStats(filters),
    staleTime: 10 * 60 * 1000, // 10 minutes for stats
  });
}

export function useUserGrowth(filters?: any) {
  return useQuery({
    queryKey: queryKeys.users.growth(filters),
    queryFn: () => usersApi.getUserGrowth(filters),
    staleTime: 10 * 60 * 1000,
  });
}

export function useSearchUsers(query: string, filters?: any) {
  return useQuery({
    queryKey: queryKeys.users.search(query, filters),
    queryFn: () => usersApi.searchUsers(query, filters),
    enabled: query.length > 0,
    staleTime: 2 * 60 * 1000,
  });
}

export function useUserSuggestions(context?: any) {
  return useQuery({
    queryKey: queryKeys.users.suggestions(context),
    queryFn: () => usersApi.getUserSuggestions(context),
    staleTime: 5 * 60 * 1000,
  });
}

// User Mutation Hooks
export function useCreateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateUserDto) => usersApi.createUser(data),
    onSuccess: (response) => {
      // Invalidate user lists
      cacheUtils.invalidateQueries(queryKeys.users.lists());
      
      // Add new user to cache
      queryClient.setQueryData(
        queryKeys.users.detail(response.data.id),
        response
      );
      
      toast.success('User created successfully');
    },
    onError: (error: any) => {
      toast.error(error.userMessage || 'Failed to create user');
    },
  });
}

export function useUpdateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserDto }) => 
      usersApi.updateUser(id, data),
    onSuccess: (response, variables) => {
      // Update user in cache
      queryClient.setQueryData(
        queryKeys.users.detail(variables.id),
        response
      );
      
      // Invalidate user lists
      cacheUtils.invalidateQueries(queryKeys.users.lists());
      
      toast.success('User updated successfully');
    },
  });
}

export function useUpdateCurrentUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: UpdateUserDto) => usersApi.updateCurrentUser(data),
    onSuccess: (response) => {
      // Update current user in cache
      queryClient.setQueryData(queryKeys.users.current(), response);
      
      // Update user detail in cache if it exists
      if (response.data.id) {
        queryClient.setQueryData(
          queryKeys.users.detail(response.data.id),
          response
        );
      }
      
      toast.success('Profile updated successfully');
    },
  });
}

export function useUpdateUserPreferences() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: UpdateUserPreferencesDto) => 
      usersApi.updateUserPreferences(data),
    onSuccess: (response) => {
      // Update current user in cache
      queryClient.setQueryData(queryKeys.users.current(), response);
      
      toast.success('Preferences updated successfully');
    },
  });
}

export function useDeleteUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => usersApi.deleteUser(id),
    onSuccess: (_, id) => {
      // Remove user from cache
      queryClient.removeQueries({ queryKey: queryKeys.users.detail(id) });
      
      // Invalidate user lists
      cacheUtils.invalidateQueries(queryKeys.users.lists());
      
      toast.success('User deleted successfully');
    },
  });
}

export function useActivateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => usersApi.activateUser(id),
    onSuccess: (response, id) => {
      // Update user in cache
      queryClient.setQueryData(queryKeys.users.detail(id), response);
      
      // Invalidate user lists
      cacheUtils.invalidateQueries(queryKeys.users.lists());
      
      toast.success('User activated successfully');
    },
  });
}

export function useDeactivateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => usersApi.deactivateUser(id),
    onSuccess: (response, id) => {
      // Update user in cache
      queryClient.setQueryData(queryKeys.users.detail(id), response);
      
      // Invalidate user lists
      cacheUtils.invalidateQueries(queryKeys.users.lists());
      
      toast.success('User deactivated successfully');
    },
  });
}

export function useBulkUpdateUsers() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: BulkUserAction) => usersApi.bulkUpdateUsers(data),
    onSuccess: (response) => {
      // Invalidate user lists
      cacheUtils.invalidateQueries(queryKeys.users.lists());
      
      toast.success(`${response.data.updated} users updated successfully`);
      
      if (response.data.failed > 0) {
        toast.error(`${response.data.failed} users failed to update`);
      }
    },
  });
}

export function useUploadAvatar() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (file: File) => usersApi.uploadAvatar(file),
    onSuccess: (response) => {
      // Invalidate current user to refetch with new avatar
      cacheUtils.invalidateQueries(queryKeys.users.current());
      
      toast.success('Avatar uploaded successfully');
    },
  });
}

export function useChangePassword() {
  return useMutation({
    mutationFn: (data: UserChangePasswordDto) => usersApi.changePassword(data),
    onSuccess: () => {
      toast.success('Password changed successfully');
    },
  });
}

export function useRevokeSession() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (sessionId: string) => usersApi.revokeSession(sessionId),
    onSuccess: () => {
      // Invalidate sessions
      cacheUtils.invalidateQueries(queryKeys.users.sessions());
      
      toast.success('Session revoked successfully');
    },
  });
}

export function useRevokeAllSessions() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => usersApi.revokeAllSessions(),
    onSuccess: () => {
      // Invalidate sessions
      cacheUtils.invalidateQueries(queryKeys.users.sessions());
      
      toast.success('All sessions revoked successfully');
    },
  });
}

export function useMarkNotificationAsRead() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (notificationId: string) => 
      usersApi.markNotificationAsRead(notificationId),
    onSuccess: () => {
      // Invalidate notifications
      cacheUtils.invalidateQueries(queryKeys.users.notifications());
    },
  });
}

export function useMarkAllNotificationsAsRead() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => usersApi.markAllNotificationsAsRead(),
    onSuccess: () => {
      // Invalidate notifications
      cacheUtils.invalidateQueries(queryKeys.users.notifications());
      
      toast.success('All notifications marked as read');
    },
  });
}

// Role Management Hooks
export function useRoles() {
  return useQuery({
    queryKey: queryKeys.roles.list(),
    queryFn: () => usersApi.getRoles(),
    staleTime: 10 * 60 * 1000, // 10 minutes for roles
  });
}

export function useRole(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.roles.detail(id),
    queryFn: () => usersApi.getRole(id),
    enabled: enabled && !!id,
    staleTime: 10 * 60 * 1000,
  });
}

export function usePermissions() {
  return useQuery({
    queryKey: queryKeys.roles.permissions(),
    queryFn: () => usersApi.getPermissions(),
    staleTime: 30 * 60 * 1000, // 30 minutes for permissions
  });
}

export function useCreateRole() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateRoleDto) => usersApi.createRole(data),
    onSuccess: (response) => {
      // Invalidate role lists
      cacheUtils.invalidateQueries(queryKeys.roles.lists());
      
      // Add new role to cache
      queryClient.setQueryData(
        queryKeys.roles.detail(response.data.id),
        response
      );
      
      toast.success('Role created successfully');
    },
  });
}

export function useUpdateRole() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateRoleDto }) => 
      usersApi.updateRole(id, data),
    onSuccess: (response, variables) => {
      // Update role in cache
      queryClient.setQueryData(
        queryKeys.roles.detail(variables.id),
        response
      );
      
      // Invalidate role lists
      cacheUtils.invalidateQueries(queryKeys.roles.lists());
      
      toast.success('Role updated successfully');
    },
  });
}

export function useDeleteRole() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => usersApi.deleteRole(id),
    onSuccess: (_, id) => {
      // Remove role from cache
      queryClient.removeQueries({ queryKey: queryKeys.roles.detail(id) });
      
      // Invalidate role lists
      cacheUtils.invalidateQueries(queryKeys.roles.lists());
      
      toast.success('Role deleted successfully');
    },
  });
}

// Department Management Hooks
export function useDepartments() {
  return useQuery({
    queryKey: queryKeys.departments.list(),
    queryFn: () => usersApi.getDepartments(),
    staleTime: 10 * 60 * 1000, // 10 minutes for departments
  });
}

export function useDepartment(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.departments.detail(id),
    queryFn: () => usersApi.getDepartment(id),
    enabled: enabled && !!id,
    staleTime: 10 * 60 * 1000,
  });
}

export function useDepartmentMembers(id: string, enabled: boolean = true) {
  return useQuery({
    queryKey: queryKeys.departments.members(id),
    queryFn: () => usersApi.getDepartmentMembers(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreateDepartment() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateDepartmentDto) => usersApi.createDepartment(data),
    onSuccess: (response) => {
      // Invalidate department lists
      cacheUtils.invalidateQueries(queryKeys.departments.lists());
      
      // Add new department to cache
      queryClient.setQueryData(
        queryKeys.departments.detail(response.data.id),
        response
      );
      
      toast.success('Department created successfully');
    },
  });
}

export function useUpdateDepartment() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateDepartmentDto }) => 
      usersApi.updateDepartment(id, data),
    onSuccess: (response, variables) => {
      // Update department in cache
      queryClient.setQueryData(
        queryKeys.departments.detail(variables.id),
        response
      );
      
      // Invalidate department lists
      cacheUtils.invalidateQueries(queryKeys.departments.lists());
      
      toast.success('Department updated successfully');
    },
  });
}

export function useDeleteDepartment() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => usersApi.deleteDepartment(id),
    onSuccess: (_, id) => {
      // Remove department from cache
      queryClient.removeQueries({ queryKey: queryKeys.departments.detail(id) });
      
      // Invalidate department lists
      cacheUtils.invalidateQueries(queryKeys.departments.lists());
      
      toast.success('Department deleted successfully');
    },
  });
}

// Utility hooks for optimistic updates
export function useOptimisticUserUpdate() {
  const queryClient = useQueryClient();
  
  return (userId: string, updateFn: (user: UserProfile) => UserProfile) => {
    const previousUser = queryClient.getQueryData<{ data: UserProfile }>(
      queryKeys.users.detail(userId)
    );
    
    if (previousUser) {
      queryClient.setQueryData(
        queryKeys.users.detail(userId),
        {
          ...previousUser,
          data: updateFn(previousUser.data),
        }
      );
    }
    
    return previousUser;
  };
}

export function useOptimisticNotificationUpdate() {
  const queryClient = useQueryClient();
  
  return (notificationId: string) => {
    const previousNotifications = queryClient.getQueryData<{ data: any[] }>(
      queryKeys.users.notifications()
    );
    
    if (previousNotifications) {
      queryClient.setQueryData(
        queryKeys.users.notifications(),
        {
          ...previousNotifications,
          data: previousNotifications.data.map(notification =>
            notification.id === notificationId
              ? { ...notification, isRead: true }
              : notification
          ),
        }
      );
    }
    
    return previousNotifications;
  };
}