"use client";

import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence, useAnimation } from "framer-motion";
import { LucideIcon } from "lucide-react";
import { cn } from '../../lib/utils';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../types/component-props';
import { defaultComponentProps } from '../../types/component-props';

export interface TimelineItem {
  id: number;
  title: string;
  date: string;
  content: string;
  category: string;
  icon: LucideIcon;
  relatedIds: number[];
  status: "completed" | "in-progress" | "pending";
  energy: number;
}

interface RadialOrbitalTimelineProps {
  timelineData: TimelineItem[];
  centerContent?: React.ReactNode;
  className?: string;
  enableConnections?: boolean;
  enableOrbitAnimation?: boolean;
  orbitDuration?: number;
}

const RadialOrbitalTimeline: React.FC<RadialOrbitalTimelineProps> = ({
  timelineData,
  centerContent,
  className,
  enableConnections = true,
  enableOrbitAnimation = true,
  orbitDuration = 30,
}) => {
  const [selectedItem, setSelectedItem] = useState<TimelineItem | null>(null);
  const [hoveredItem, setHoveredItem] = useState<number | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const controls = useAnimation();

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      const rect = containerRef.current.getBoundingClientRect();
      setMousePosition({
        x: e.clientX - rect.left - rect.width / 2,
        y: e.clientY - rect.top - rect.height / 2,
      });
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, []);

  useEffect(() => {
    if (enableOrbitAnimation) {
      controls.start({
        rotate: 360,
        transition: {
          duration: orbitDuration,
          repeat: Infinity,
          ease: "linear",
        },
      });
    }
  }, [enableOrbitAnimation, orbitDuration, controls]);

  const getOrbitRadius = (index: number, total: number) => {
    const baseRadius = 120;
    const radiusIncrement = 60;
    const layers = Math.ceil(total / 6);
    const layer = Math.floor(index / 6);
    return baseRadius + layer * radiusIncrement;
  };

  const getItemPosition = (index: number, total: number) => {
    const itemsPerLayer = 6;
    const layer = Math.floor(index / itemsPerLayer);
    const positionInLayer = index % itemsPerLayer;
    const itemsInThisLayer = Math.min(itemsPerLayer, total - layer * itemsPerLayer);
    const angle = (positionInLayer / itemsInThisLayer) * 2 * Math.PI - Math.PI / 2;
    const radius = getOrbitRadius(index, total);
    
    return {
      x: Math.cos(angle) * radius,
      y: Math.sin(angle) * radius,
    };
  };

  const getStatusColor = (status: TimelineItem["status"]) => {
    switch (status) {
      case "completed":
        return "from-green-400 to-green-600";
      case "in-progress":
        return "from-yellow-400 to-yellow-600";
      case "pending":
        return "from-gray-400 to-gray-600";
      default:
        return "from-blue-400 to-blue-600";
    }
  };

  const getEnergyPulse = (energy: number) => {
    const scale = 0.8 + (energy / 100) * 0.4;
    const duration = 3 - (energy / 100) * 1.5;
    return { scale, duration };
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative w-full h-[600px] flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900",
        className
      )}
    >
      {/* Background particles */}
      <div className="absolute inset-0">
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            initial={{
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
            }}
            animate={{
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
            }}
            transition={{
              duration: Math.random() * 20 + 10,
              repeat: Infinity,
              repeatType: "reverse",
            }}
          />
        ))}
      </div>

      {/* Orbital rings */}
      <div className="absolute inset-0 flex items-center justify-center">
        {[...Array(Math.ceil(timelineData.length / 6))].map((_, layer) => {
          const radius = getOrbitRadius(layer * 6, timelineData.length);
          return (
            <motion.div
              key={layer}
              className="absolute rounded-full border border-white/10"
              style={{
                width: radius * 2,
                height: radius * 2,
              }}
              animate={{
                scale: [1, 1.02, 1],
                opacity: [0.3, 0.5, 0.3],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                delay: layer * 0.5,
              }}
            />
          );
        })}
      </div>

      {/* Center hub */}
      <motion.div
        className="absolute z-20"
        animate={{
          scale: [1, 1.1, 1],
          rotate: [0, 180, 360],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "linear",
        }}
      >
        <div className="relative w-32 h-32 rounded-full bg-gradient-to-br from-purple-600 to-pink-600 p-[2px]">
          <div className="w-full h-full rounded-full bg-slate-900 flex items-center justify-center">
            {centerContent || (
              <div className="text-center">
                <div className="text-2xl font-bold text-white">Timeline</div>
                <div className="text-sm text-gray-400">Hub</div>
              </div>
            )}
          </div>
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-purple-600/30 to-pink-600/30 blur-xl animate-pulse" />
        </div>
      </motion.div>

      {/* Connections */}
      {enableConnections && (
        <svg className="absolute inset-0 w-full h-full pointer-events-none">
          <defs>
            <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="rgba(147, 51, 234, 0.5)" />
              <stop offset="100%" stopColor="rgba(236, 72, 153, 0.5)" />
            </linearGradient>
          </defs>
          {timelineData.map((item) => {
            const itemPos = getItemPosition(timelineData.indexOf(item), timelineData.length);
            return item.relatedIds.map((relatedId) => {
              const relatedItem = timelineData.find((i) => i.id === relatedId);
              if (!relatedItem) return null;
              const relatedPos = getItemPosition(
                timelineData.indexOf(relatedItem),
                timelineData.length
              );
              
              const isHighlighted = hoveredItem === item.id || hoveredItem === relatedId;
              
              return (
                <motion.line
                  key={`${item.id}-${relatedId}`}
                  x1={itemPos.x + window.innerWidth / 2}
                  y1={itemPos.y + window.innerHeight / 2}
                  x2={relatedPos.x + window.innerWidth / 2}
                  y2={relatedPos.y + window.innerHeight / 2}
                  stroke="url(#connectionGradient)"
                  strokeWidth={isHighlighted ? 2 : 1}
                  opacity={isHighlighted ? 0.8 : 0.3}
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 2, delay: 0.5 }}
                />
              );
            });
          })}
        </svg>
      )}

      {/* Timeline items */}
      <motion.div
        className="absolute inset-0"
        animate={controls}
      >
        {timelineData.map((item, index) => {
          const position = getItemPosition(index, timelineData.length);
          const { scale: energyScale, duration: energyDuration } = getEnergyPulse(item.energy);
          const Icon = item.icon;
          const hovered = hoveredItem === item.id;
          const isSelected = selectedItem?.id === item.id;
          
          return (
            <motion.div
              key={item.id}
              className="absolute"
              style={{
                left: "50%",
                top: "50%",
                x: position.x,
                y: position.y,
              }}
              animate={enableOrbitAnimation ? {
                rotate: -360,
              } : {}}
              transition={{
                duration: orbitDuration,
                repeat: Infinity,
                ease: "linear",
              }}
            >
              <motion.button
                className={cn(
                  "relative w-16 h-16 rounded-full p-[2px] cursor-pointer",
                  "bg-gradient-to-br",
                  getStatusColor(item.status)
                )}
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.95 }}
                onHoverStart={() => setHoveredItem(item.id)}
                onHoverEnd={() => setHoveredItem(null)}
                onClick={() => setSelectedItem(item)}
              >
                <div className="w-full h-full rounded-full bg-slate-900 flex items-center justify-center">
                  <Icon className="w-6 h-6 text-white" />
                </div>
                
                {/* Energy pulse */}
                <motion.div
                  className={cn(
                    "absolute inset-0 rounded-full",
                    "bg-gradient-to-br",
                    getStatusColor(item.status)
                  )}
                  animate={{
                    scale: [1, energyScale, 1],
                    opacity: [0.3, 0.1, 0.3],
                  }}
                  transition={{
                    duration: energyDuration,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                
                {/* Hover label */}
                <AnimatePresence>
                  {hovered && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      className="absolute top-full mt-2 left-1/2 transform -translate-x-1/2 whitespace-nowrap"
                    >
                      <div className="bg-slate-800/90 backdrop-blur-sm px-3 py-1 rounded-lg text-white text-sm">
                        {item.title}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.button>
            </motion.div>
          );
        })}
      </motion.div>

      {/* Selected item detail */}
      <AnimatePresence>
        {selectedItem && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30"
          >
            <div className="bg-slate-800/90 backdrop-blur-xl rounded-2xl p-6 shadow-2xl border border-white/10 min-w-[300px]">
              <div className="flex items-start gap-4">
                <div className={cn(
                  "w-12 h-12 rounded-lg flex items-center justify-center",
                  "bg-gradient-to-br",
                  getStatusColor(selectedItem.status)
                )}>
                  <selectedItem.icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-white">{selectedItem.title}</h3>
                  <p className="text-sm text-gray-400 mt-1">{selectedItem.date}</p>
                  <p className="text-sm text-gray-300 mt-2">{selectedItem.content}</p>
                  <div className="flex items-center gap-2 mt-3">
                    <span className="text-xs text-gray-500">Energy:</span>
                    <div className="flex-1 h-2 bg-gray-700 rounded-full overflow-hidden">
                      <motion.div
                        className="h-full bg-gradient-to-r from-purple-500 to-pink-500"
                        initial={{ width: 0 }}
                        animate={{ width: `${selectedItem.energy}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                    <span className="text-xs text-gray-400">{selectedItem.energy}%</span>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedItem(null)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  ×
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default RadialOrbitalTimeline;