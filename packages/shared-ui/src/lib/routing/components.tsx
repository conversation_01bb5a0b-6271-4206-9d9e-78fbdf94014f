/**
 * Enhanced routing components for TanStack Router
 * Features: Breadcrumbs, Code Splitting, Navigation, Analytics
 */

import React, { forwardRef, useCallback, useMemo, Suspense } from 'react'
import { Link, useLocation, useRouter } from '@tanstack/react-router'
import { NavigationOptions, BreadcrumbItem, BreadcrumbConfig } from './types'
import { useBreadcrumbs, useNavigation, useRouteMatch, useRoutePrefetch } from './hooks'
import { buildRoute, isRouteActive } from './utils'

/**
 * Enhanced Link component with prefetching and analytics
 */
export interface EnhancedLinkProps {
  to: string
  params?: Record<string, string>
  search?: Record<string, any>
  hash?: string
  replace?: boolean
  activeClass?: string
  inactiveClass?: string
  exactMatch?: boolean
  prefetch?: boolean
  children: React.ReactNode
  className?: string
  onClick?: (event: React.MouseEvent<HTMLAnchorElement>) => void
  onMouseEnter?: (event: React.MouseEvent<HTMLAnchorElement>) => void
  onFocus?: (event: React.FocusEvent<HTMLAnchorElement>) => void
  disabled?: boolean
  external?: boolean
  download?: boolean | string
  target?: string
  rel?: string
  'aria-label'?: string
  'aria-describedby'?: string
  role?: string
  tabIndex?: number
}

export const EnhancedLink = forwardRef<HTMLAnchorElement, EnhancedLinkProps>(
  ({
    to,
    params,
    search,
    hash,
    replace = false,
    activeClass = 'active',
    inactiveClass = '',
    exactMatch = false,
    prefetch = false,
    children,
    className,
    onClick,
    onMouseEnter,
    onFocus,
    disabled = false,
    external = false,
    download,
    target,
    rel,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedby,
    role,
    tabIndex,
    ...props
  }, ref) => {
    const location = useLocation()
    const { prefetch: prefetchRoute, prefetchOnHover } = useRoutePrefetch()
    
    const fullPath = useMemo(() => buildRoute(to, params, search, hash), [to, params, search, hash])
    const isActive = useMemo(() => isRouteActive(location.pathname, fullPath, exactMatch), [location.pathname, fullPath, exactMatch])
    
    const computedClassName = useMemo(() => {
      const classes = [className]
      
      if (isActive) {
        classes.push(activeClass)
      } else {
        classes.push(inactiveClass)
      }
      
      if (disabled) {
        classes.push('disabled')
      }
      
      return classes.filter(Boolean).join(' ')
    }, [className, isActive, activeClass, inactiveClass, disabled])
    
    const handleClick = useCallback((event: React.MouseEvent<HTMLAnchorElement>) => {
      if (disabled) {
        event.preventDefault()
        return
      }
      
      if (onClick) {
        onClick(event)
      }
      
      // Track click event
      console.log(`Link clicked: ${fullPath}`)
    }, [disabled, onClick, fullPath])
    
    const handleMouseEnter = useCallback((event: React.MouseEvent<HTMLAnchorElement>) => {
      if (prefetch && !disabled) {
        prefetchRoute(fullPath)
      }
      
      if (onMouseEnter) {
        onMouseEnter(event)
      }
    }, [prefetch, disabled, prefetchRoute, fullPath, onMouseEnter])
    
    const handleFocus = useCallback((event: React.FocusEvent<HTMLAnchorElement>) => {
      if (prefetch && !disabled) {
        prefetchRoute(fullPath)
      }
      
      if (onFocus) {
        onFocus(event)
      }
    }, [prefetch, disabled, prefetchRoute, fullPath, onFocus])
    
    // External link
    if (external) {
      return (
        <a
          ref={ref}
          href={fullPath}
          className={computedClassName}
          onClick={handleClick}
          onMouseEnter={handleMouseEnter}
          onFocus={handleFocus}
          target={target || '_blank'}
          rel={rel || 'noopener noreferrer'}
          download={download}
          aria-label={ariaLabel}
          aria-describedby={ariaDescribedby}
          role={role}
          tabIndex={disabled ? -1 : tabIndex}
          {...props}
        >
          {children}
        </a>
      )
    }
    
    // Internal link using TanStack Router
    return (
      <Link
        ref={ref}
        to={fullPath}
        replace={replace}
        className={computedClassName}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onFocus={handleFocus}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedby}
        role={role}
        tabIndex={disabled ? -1 : tabIndex}
        {...props}
      >
        {children}
      </Link>
    )
  }
)

EnhancedLink.displayName = 'EnhancedLink'

/**
 * Breadcrumb navigation component
 */
export interface BreadcrumbProps {
  items?: BreadcrumbItem[]
  config?: BreadcrumbConfig
  className?: string
  itemClassName?: string
  activeClassName?: string
  separatorClassName?: string
  onItemClick?: (item: BreadcrumbItem) => void
}

export function Breadcrumb({
  items,
  config = {},
  className = '',
  itemClassName = '',
  activeClassName = 'active',
  separatorClassName = '',
  onItemClick
}: BreadcrumbProps) {
  const defaultBreadcrumbs = useBreadcrumbs()
  const breadcrumbs = items || defaultBreadcrumbs
  
  const {
    separator = '/',
    maxItems = 5,
    showHome = true,
    homeTitle = 'Home',
    homePath = '/'
  } = config
  
  const displayBreadcrumbs = useMemo(() => {
    let filtered = breadcrumbs
    
    if (!showHome) {
      filtered = filtered.filter(item => item.path !== '/')
    }
    
    if (filtered.length > maxItems) {
      const start = filtered.slice(0, 1)
      const end = filtered.slice(-maxItems + 2)
      return [...start, { title: '...', path: '', isActive: false }, ...end]
    }
    
    return filtered
  }, [breadcrumbs, showHome, maxItems])
  
  const handleItemClick = useCallback((item: BreadcrumbItem) => {
    if (onItemClick) {
      onItemClick(item)
    }
  }, [onItemClick])
  
  return (
    <nav aria-label="Breadcrumb" className={`breadcrumb ${className}`}>
      <ol className="breadcrumb-list">
        {displayBreadcrumbs.map((item, index) => (
          <li key={index} className={`breadcrumb-item ${itemClassName}`}>
            {item.path && !item.isActive ? (
              <EnhancedLink
                to={item.path}
                className={`breadcrumb-link`}
                onClick={() => handleItemClick(item)}
                aria-current={item.isActive ? 'page' : undefined}
              >
                {item.title}
              </EnhancedLink>
            ) : (
              <span
                className={`breadcrumb-text ${item.isActive ? activeClassName : ''}`}
                aria-current={item.isActive ? 'page' : undefined}
              >
                {item.title}
              </span>
            )}
            
            {index < displayBreadcrumbs.length - 1 && (
              <span className={`breadcrumb-separator ${separatorClassName}`} aria-hidden="true">
                {separator}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

/**
 * Navigation menu component
 */
export interface NavItem {
  title: string
  path: string
  icon?: React.ReactNode
  badge?: string | number
  disabled?: boolean
  hidden?: boolean
  children?: NavItem[]
  external?: boolean
  permissions?: string[]
  roles?: string[]
}

export interface NavigationProps {
  items: NavItem[]
  orientation?: 'horizontal' | 'vertical'
  className?: string
  itemClassName?: string
  activeClassName?: string
  disabledClassName?: string
  onItemClick?: (item: NavItem) => void
  collapsible?: boolean
  defaultCollapsed?: boolean
  renderIcon?: (icon: React.ReactNode) => React.ReactNode
  renderBadge?: (badge: string | number) => React.ReactNode
}

export function Navigation({
  items,
  orientation = 'horizontal',
  className = '',
  itemClassName = '',
  activeClassName = 'active',
  disabledClassName = 'disabled',
  onItemClick,
  collapsible = false,
  defaultCollapsed = false,
  renderIcon,
  renderBadge
}: NavigationProps) {
  const location = useLocation()
  const [collapsed, setCollapsed] = React.useState(defaultCollapsed)
  
  const renderNavItem = useCallback((item: NavItem, depth: number = 0) => {
    if (item.hidden) return null
    
    const isActive = isRouteActive(location.pathname, item.path, false)
    const hasChildren = item.children && item.children.length > 0
    
    const itemClasses = [
      'nav-item',
      itemClassName,
      isActive ? activeClassName : '',
      item.disabled ? disabledClassName : '',
      hasChildren ? 'has-children' : ''
    ].filter(Boolean).join(' ')
    
    const handleClick = useCallback(() => {
      if (item.disabled) return
      
      if (onItemClick) {
        onItemClick(item)
      }
    }, [item])
    
    return (
      <li key={item.path} className={itemClasses} style={{ paddingLeft: `${depth * 16}px` }}>
        {item.external ? (
          <a
            href={item.path}
            className="nav-link"
            onClick={handleClick}
            target="_blank"
            rel="noopener noreferrer"
            aria-disabled={item.disabled}
          >
            {renderIcon && item.icon && renderIcon(item.icon)}
            <span>{item.title}</span>
            {renderBadge && item.badge && renderBadge(item.badge)}
          </a>
        ) : (
          <EnhancedLink
            to={item.path}
            className="nav-link"
            activeClass={activeClassName}
            onClick={handleClick}
            disabled={item.disabled}
            aria-disabled={item.disabled}
          >
            {renderIcon && item.icon && renderIcon(item.icon)}
            <span>{item.title}</span>
            {renderBadge && item.badge && renderBadge(item.badge)}
          </EnhancedLink>
        )}
        
        {hasChildren && (
          <ul className="nav-submenu">
            {item.children!.map(child => renderNavItem(child, depth + 1))}
          </ul>
        )}
      </li>
    )
  }, [location.pathname, itemClassName, activeClassName, disabledClassName, onItemClick, renderIcon, renderBadge])
  
  const toggleCollapsed = useCallback(() => {
    setCollapsed(!collapsed)
  }, [collapsed])
  
  return (
    <nav className={`navigation ${orientation} ${className} ${collapsed ? 'collapsed' : ''}`}>
      {collapsible && (
        <button
          type="button"
          className="nav-toggle"
          onClick={toggleCollapsed}
          aria-expanded={!collapsed}
          aria-label={collapsed ? 'Expand navigation' : 'Collapse navigation'}
        >
          <span className="nav-toggle-icon" />
        </button>
      )}
      
      <ul className="nav-list">
        {items.map(item => renderNavItem(item))}
      </ul>
    </nav>
  )
}

/**
 * Route loader component
 */
export interface RouteLoaderProps {
  fallback?: React.ReactNode
  error?: React.ReactNode
  className?: string
  children: React.ReactNode
}

export function RouteLoader({
  fallback,
  error,
  className = '',
  children
}: RouteLoaderProps) {
  const router = useRouter()
  const routerState = router.state
  
  // Check for router errors (property might not exist in current TanStack Router version)
  const routerError = (routerState as any).error;
  if (routerError) {
    return (
      <div className={`route-error ${className}`}>
        {error || (
          <div className="error-message">
            <h2>Something went wrong</h2>
            <p>{routerError.message || 'An unexpected error occurred'}</p>
          </div>
        )}
      </div>
    )
  }
  
  if (routerState.isLoading || routerState.isTransitioning) {
    return (
      <div className={`route-loading ${className}`}>
        {fallback || (
          <div className="loading-spinner">
            <div className="spinner" />
            <p>Loading...</p>
          </div>
        )}
      </div>
    )
  }
  
  return <>{children}</>
}

/**
 * Route guard component
 */
export interface RouteGuardProps {
  requireAuth?: boolean
  requireRole?: string | string[]
  requirePermission?: string | string[]
  fallback?: React.ReactNode
  redirectTo?: string
  loading?: React.ReactNode
  children: React.ReactNode
}

export function RouteGuard({
  requireAuth = false,
  requireRole,
  requirePermission,
  fallback,
  redirectTo = '/login',
  loading,
  children
}: RouteGuardProps) {
  const { navigate } = useNavigation()
  const [isLoading, setIsLoading] = React.useState(true)
  const [hasAccess, setHasAccess] = React.useState(false)
  
  // Mock user data - in real app, this would come from auth context
  const user = { id: 1, name: 'User', roles: ['user'], permissions: ['read'] }
  
  React.useEffect(() => {
    const checkAccess = async () => {
      setIsLoading(true)
      
      try {
        // Check authentication
        if (requireAuth && !user) {
          if (redirectTo) {
            navigate(redirectTo)
            return
          }
          setHasAccess(false)
          return
        }
        
        // Check roles
        if (requireRole && user) {
          const roles = Array.isArray(requireRole) ? requireRole : [requireRole]
          const hasRole = roles.some(role => user.roles.includes(role))
          if (!hasRole) {
            setHasAccess(false)
            return
          }
        }
        
        // Check permissions
        if (requirePermission && user) {
          const permissions = Array.isArray(requirePermission) ? requirePermission : [requirePermission]
          const hasPermission = permissions.some(permission => user.permissions.includes(permission))
          if (!hasPermission) {
            setHasAccess(false)
            return
          }
        }
        
        setHasAccess(true)
      } catch (error) {
        console.error('Route guard check failed:', error)
        setHasAccess(false)
      } finally {
        setIsLoading(false)
      }
    }
    
    checkAccess()
  }, [requireAuth, requireRole, requirePermission, user, navigate, redirectTo])
  
  if (isLoading) {
    return loading || <div className="route-guard-loading">Loading...</div>
  }
  
  if (!hasAccess) {
    return fallback || <div className="route-guard-denied">Access denied</div>
  }
  
  return <>{children}</>
}

/**
 * Route transition component
 */
export interface RouteTransitionProps {
  children: React.ReactNode
  className?: string
  duration?: number
  appear?: boolean
  enter?: boolean
  exit?: boolean
}

export function RouteTransition({
  children,
  className = '',
  duration = 300,
  appear = true,
  enter = true,
  exit = true
}: RouteTransitionProps) {
  const location = useLocation()
  const [isTransitioning, setIsTransitioning] = React.useState(false)
  
  React.useEffect(() => {
    if (enter || exit) {
      setIsTransitioning(true)
      const timer = setTimeout(() => {
        setIsTransitioning(false)
      }, duration)
      
      return () => clearTimeout(timer)
    }
  }, [location.pathname, duration, enter, exit])
  
  return (
    <div
      className={`route-transition ${className} ${isTransitioning ? 'transitioning' : ''}`}
      style={{
        '--transition-duration': `${duration}ms`
      } as React.CSSProperties}
    >
      {children}
    </div>
  )
}

/**
 * Route analytics component
 */
export interface RouteAnalyticsProps {
  trackPageViews?: boolean
  trackClicks?: boolean
  trackErrors?: boolean
  customEvents?: string[]
  children: React.ReactNode
}

export function RouteAnalytics({
  trackPageViews = true,
  trackClicks = false,
  trackErrors = true,
  customEvents = [],
  children
}: RouteAnalyticsProps) {
  const location = useLocation()
  const router = useRouter()
  
  React.useEffect(() => {
    if (trackPageViews) {
      console.log(`Page view tracked: ${location.pathname}`)
      // Track page view in analytics service
    }
  }, [location.pathname, trackPageViews])
  
  React.useEffect(() => {
    if (trackErrors) {
      // TODO: Update for current TanStack Router API - subscribe/unsubscribe pattern has changed
      console.log('Route error tracking is disabled - needs update for current TanStack Router API')
    }
  }, [router, trackErrors])
  
  React.useEffect(() => {
    if (trackClicks) {
      const handleClick = (event: MouseEvent) => {
        const target = event.target as HTMLElement
        if (target.tagName === 'A') {
          console.log(`Link click tracked: ${target.getAttribute('href')}`)
          // Track click in analytics service
        }
      }
      
      document.addEventListener('click', handleClick)
      return () => document.removeEventListener('click', handleClick)
    }
  }, [trackClicks])
  
  return <>{children}</>
}

/**
 * Smart Breadcrumb with route metadata integration
 */
export interface SmartBreadcrumbProps extends BreadcrumbProps {
  routeMetadata?: Record<string, any>
  showDescription?: boolean
  showBadges?: boolean
  maxDisplayItems?: number
  showIcons?: boolean
}

export function SmartBreadcrumb({
  routeMetadata = {},
  showDescription = false,
  showBadges = false,
  maxDisplayItems = 5,
  showIcons = false,
  ...props
}: SmartBreadcrumbProps) {
  const location = useLocation()
  const segments = location.pathname.split('/').filter(Boolean)
  
  const enhancedBreadcrumbs = useMemo(() => {
    return segments.map((segment, index) => {
      const path = '/' + segments.slice(0, index + 1).join('/')
      const metadata = routeMetadata[path] || {}
      
      return {
        title: metadata.title || segment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        path,
        isActive: index === segments.length - 1,
        description: metadata.description,
        badge: metadata.badge,
        icon: metadata.icon,
        metadata
      }
    })
  }, [segments, routeMetadata])
  
  const displayItems = useMemo(() => {
    if (enhancedBreadcrumbs.length <= maxDisplayItems) {
      return enhancedBreadcrumbs
    }
    
    return [
      enhancedBreadcrumbs[0],
      { title: '...', path: '', isActive: false },
      ...enhancedBreadcrumbs.slice(-(maxDisplayItems - 2))
    ]
  }, [enhancedBreadcrumbs, maxDisplayItems])
  
  const currentItem = enhancedBreadcrumbs[enhancedBreadcrumbs.length - 1]
  
  return (
    <div className="smart-breadcrumb-container">
      <Breadcrumb
        items={displayItems}
        {...props}
      />
      
      {showDescription && currentItem?.description && (
        <p className="text-sm text-gray-600 mt-2 ml-4">
          {currentItem.description}
        </p>
      )}
      
      {showBadges && (
        <div className="flex items-center space-x-2 mt-2 ml-4">
          {enhancedBreadcrumbs
            .filter(item => item.badge)
            .map((item, index) => (
              <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {item.badge}
              </span>
            ))}
        </div>
      )}
      
      {showIcons && (
        <div className="flex items-center space-x-2 mt-2 ml-4">
          {enhancedBreadcrumbs
            .filter(item => item.icon)
            .map((item, index) => (
              <span key={index} className="flex items-center space-x-1">
                {item.icon}
                <span className="text-xs text-gray-500">{item.title}</span>
              </span>
            ))}
        </div>
      )}
    </div>
  )
}

/**
 * Code Split Route component for lazy loading
 */
export interface CodeSplitRouteProps {
  loader: () => Promise<{ default: React.ComponentType<any> }>
  fallback?: React.ReactNode
  errorBoundary?: React.ComponentType<{ error: Error; reset: () => void }>
  preload?: boolean
  timeout?: number
  retryAttempts?: number
  onLoadStart?: () => void
  onLoadEnd?: () => void
  onError?: (error: Error) => void
  [key: string]: any
}

export function CodeSplitRoute({
  loader,
  fallback,
  errorBoundary: ErrorBoundary,
  preload = false,
  timeout = 10000,
  retryAttempts = 3,
  onLoadStart,
  onLoadEnd,
  onError,
  ...props
}: CodeSplitRouteProps) {
  const [LazyComponent, setLazyComponent] = React.useState<React.ComponentType<any> | null>(null)
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<Error | null>(null)
  const [retryCount, setRetryCount] = React.useState(0)
  
  const loadComponent = useCallback(async () => {
    if (LazyComponent) return
    
    setLoading(true)
    setError(null)
    
    if (onLoadStart) {
      onLoadStart()
    }
    
    try {
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Component load timeout')), timeout)
      })
      
      const componentPromise = loader()
      const result = await Promise.race([componentPromise, timeoutPromise])
      
      if (result && typeof result === 'object' && 'default' in result) {
        setLazyComponent(result.default as React.ComponentType<any>)
      } else {
        throw new Error('Invalid component module')
      }
    } catch (err) {
      const error = err as Error
      console.error('Code splitting error:', error)
      setError(error)
      
      if (onError) {
        onError(error)
      }
      
      // Retry logic
      if (retryCount < retryAttempts) {
        setTimeout(() => {
          setRetryCount(prev => prev + 1)
          loadComponent()
        }, 1000 * (retryCount + 1))
      }
    } finally {
      setLoading(false)
      if (onLoadEnd) {
        onLoadEnd()
      }
    }
  }, [loader, LazyComponent, timeout, retryCount, retryAttempts, onLoadStart, onLoadEnd, onError])
  
  React.useEffect(() => {
    if (preload) {
      loadComponent()
    }
  }, [preload, loadComponent])
  
  React.useEffect(() => {
    if (!LazyComponent && !loading && !error) {
      loadComponent()
    }
  }, [LazyComponent, loading, error, loadComponent])
  
  if (error) {
    if (ErrorBoundary) {
      return (
        <ErrorBoundary
          error={error}
          reset={() => {
            setError(null)
            setRetryCount(0)
            loadComponent()
          }}
        />
      )
    }
    
    return (
      <div className="flex flex-col items-center justify-center min-h-64 p-4">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load component</h3>
          <p className="text-sm text-gray-600 mb-4">{error.message}</p>
          <button
            onClick={() => {
              setError(null)
              setRetryCount(0)
              loadComponent()
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Retry ({retryAttempts - retryCount} attempts left)
          </button>
        </div>
      </div>
    )
  }
  
  if (loading || !LazyComponent) {
    return (
      <div className="route-loading">
        {fallback || (
          <div className="flex items-center justify-center min-h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-sm text-gray-600">Loading component...</span>
          </div>
        )}
      </div>
    )
  }
  
  return <LazyComponent {...props} />
}

/**
 * Enhanced Navigation Menu with nested support
 */
export interface EnhancedNavItem extends NavItem {
  description?: string
  shortcut?: string
  group?: string
  order?: number
  onClick?: (item: EnhancedNavItem) => void
}

export interface EnhancedNavigationProps extends Omit<NavigationProps, 'items'> {
  items: EnhancedNavItem[]
  showDescriptions?: boolean
  showShortcuts?: boolean
  groupItems?: boolean
  searchable?: boolean
  onSearch?: (query: string) => void
  filterFn?: (item: EnhancedNavItem, query: string) => boolean
}

export function EnhancedNavigation({
  items,
  showDescriptions = false,
  showShortcuts = false,
  groupItems = false,
  searchable = false,
  onSearch,
  filterFn,
  ...props
}: EnhancedNavigationProps) {
  const [searchQuery, setSearchQuery] = React.useState('')
  const [expandedGroups, setExpandedGroups] = React.useState<Set<string>>(new Set())
  
  const filteredItems = useMemo(() => {
    if (!searchQuery) return items
    
    const defaultFilter = (item: EnhancedNavItem, query: string) => {
      return item.title.toLowerCase().includes(query.toLowerCase()) ||
             (item.description && item.description.toLowerCase().includes(query.toLowerCase()))
    }
    
    const filter = filterFn || defaultFilter
    return items.filter(item => filter(item, searchQuery))
  }, [items, searchQuery, filterFn])
  
  const groupedItems = useMemo(() => {
    if (!groupItems) return { ungrouped: filteredItems }
    
    return filteredItems.reduce((acc, item) => {
      const group = item.group || 'ungrouped'
      if (!acc[group]) acc[group] = []
      acc[group].push(item)
      return acc
    }, {} as Record<string, EnhancedNavItem[]>)
  }, [filteredItems, groupItems])
  
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query)
    if (onSearch) {
      onSearch(query)
    }
  }, [onSearch])
  
  const toggleGroup = useCallback((group: string) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev)
      if (newSet.has(group)) {
        newSet.delete(group)
      } else {
        newSet.add(group)
      }
      return newSet
    })
  }, [])
  
  const renderNavItem = useCallback((item: EnhancedNavItem, depth: number = 0) => {
    const isActive = isRouteActive(useLocation().pathname, item.path, false)
    
    return (
      <div key={item.path} className="nav-item-container">
        <div className="flex items-center space-x-2 p-2 hover:bg-gray-100 rounded-md transition-colors">
          <EnhancedLink
            to={item.path}
            className={`flex-1 flex items-center space-x-2 ${isActive ? 'text-blue-600 font-medium' : 'text-gray-700'}`}
            onClick={() => item.onClick?.(item)}
            disabled={item.disabled}
          >
            {item.icon && <span className="nav-icon">{item.icon}</span>}
            <span className="nav-title">{item.title}</span>
            {item.badge && (
              <span className="ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                {item.badge}
              </span>
            )}
          </EnhancedLink>
          
          {showShortcuts && item.shortcut && (
            <span className="text-xs text-gray-400 bg-gray-100 px-1 py-0.5 rounded">
              {item.shortcut}
            </span>
          )}
        </div>
        
        {showDescriptions && item.description && (
          <p className="text-xs text-gray-500 mt-1 pl-8">
            {item.description}
          </p>
        )}
        
        {item.children && (
          <div className="ml-4 mt-1 border-l border-gray-200 pl-4">
            {item.children.map(child => renderNavItem(child as EnhancedNavItem, depth + 1))}
          </div>
        )}
      </div>
    )
  }, [showDescriptions, showShortcuts])
  
  return (
    <div className="enhanced-navigation">
      {searchable && (
        <div className="nav-search mb-4">
          <input
            type="text"
            placeholder="Search navigation..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      )}
      
      <div className="nav-content">
        {Object.entries(groupedItems).map(([group, items]) => (
          <div key={group} className="nav-group">
            {groupItems && group !== 'ungrouped' && (
              <div className="nav-group-header">
                <button
                  onClick={() => toggleGroup(group)}
                  className="flex items-center space-x-2 w-full text-left p-2 text-sm font-medium text-gray-900 hover:bg-gray-100 rounded-md"
                >
                  <span className={`transform transition-transform ${expandedGroups.has(group) ? 'rotate-90' : ''}`}>
                    ▶
                  </span>
                  <span>{group}</span>
                </button>
              </div>
            )}
            
            <div className={`nav-group-items ${groupItems && !expandedGroups.has(group) && group !== 'ungrouped' ? 'hidden' : ''}`}>
              {items.map(item => renderNavItem(item))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}