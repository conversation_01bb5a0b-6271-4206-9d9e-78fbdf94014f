import { motion, AnimatePresence } from "framer-motion";
import { forwardRef, useState, useMemo } from "react";
import {
  <PERSON>uo<PERSON>, 
  Co<PERSON>, 
  Trash2, 
  Download, 
  Edit3, 
  Check, 
  X,
  BookOpen,
  FileText,
  Settings,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Hash,
  Filter,
  Search
} from "lucide-react";
import { cn } from '../../../lib/utils';
import { LuminarButton } from "../actions/button-advanced";
import { LuminarCard } from "../display/card";
import { LuminarText } from "../display/text";
import { LuminarBadge } from "../display/badge";
import { LuminarTooltip } from "../feedback/tooltip";
import { LuminarInput } from "../forms/input";
import { LuminarSelect } from "../forms/select";
import { LuminarTextarea } from "../forms/textarea";
import { LuminarDropdown } from "../actions/dropdown";
import {
  getGlassClasses, 
  animationPresets, 
  type ComponentSize
} from "../../../design-system";
import { Citation, ResearchSource } from '../../../types/research';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface ResearchCitationsProps {
  citations: Citation[];
  sources: ResearchSource[];
  onCitationRemove: (citationId: string) => void;
  onCitationEdit: (citationId: string, updates: Partial<Citation>) => void;
  onCitationStyleChange: (style: Citation['style']) => void;
  onExportCitations: (format: 'text' | 'bibtex' | 'json') => void;
  citationStyle: Citation['style'];
  className?: string;
  size?: ComponentSize;
  showSearch?: boolean;
  showGrouping?: boolean;
  collapsible?: boolean;
  maxHeight?: string;
}

const ResearchCitations = forwardRef<HTMLDivElement, ResearchCitationsProps>(
  ({
    citations,
    sources,
    onCitationRemove,
    onCitationEdit,
    onCitationStyleChange,
    onExportCitations,
    citationStyle,
    className,
    size = "md",
    showSearch = true,
    showGrouping = true,
    collapsible = false,
    maxHeight = "400px",
    ...props
  }, ref) => {
    const [searchQuery, setSearchQuery] = useState("");
    const [filterBySource, setFilterBySource] = useState<string>('all');
    const [editingCitation, setEditingCitation] = useState<string | null>(null);
    const [editText, setEditText] = useState("");
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [copiedCitation, setCopiedCitation] = useState<string | null>(null);
    const [groupBy, setGroupBy] = useState<'source' | 'style' | 'none'>('source');

    // Filter citations
    const filteredCitations = useMemo(() => {
      let filtered = citations;

      // Search filter
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        filtered = filtered.filter(citation =>
          citation.text.toLowerCase().includes(query) ||
          citation.generatedText.toLowerCase().includes(query) ||
          sources.find(s => s.id === citation.sourceId)?.title.toLowerCase().includes(query)
        );
      }

      // Source filter
      if (filterBySource !== 'all') {
        filtered = filtered.filter(citation => citation.sourceId === filterBySource);
      }

      return filtered;
    }, [citations, searchQuery, filterBySource, sources]);

    // Group citations
    const groupedCitations = useMemo(() => {
      if (groupBy === 'none') {
        return { 'All Citations': filteredCitations };
      }

      const groups: Record<string, Citation[]> = {};

      filteredCitations.forEach(citation => {
        let groupKey: string;
        
        if (groupBy === 'source') {
          const source = sources.find(s => s.id === citation.sourceId);
          groupKey = source?.title || 'Unknown Source';
        } else if (groupBy === 'style') {
          groupKey = citation.style;
        } else {
          groupKey = 'All Citations';
        }

        if (!groups[groupKey]) {
          groups[groupKey] = [];
        }
        groups[groupKey].push(citation);
      });

      return groups;
    }, [filteredCitations, groupBy, sources]);

    // Start editing citation
    const startEditing = (citation: Citation) => {
      setEditingCitation(citation.id);
      setEditText(citation.generatedText);
    };

    // Save citation edit
    const saveEdit = (citationId: string) => {
      onCitationEdit(citationId, { generatedText: editText });
      setEditingCitation(null);
      setEditText("");
    };

    // Cancel edit
    const cancelEdit = () => {
      setEditingCitation(null);
      setEditText("");
    };

    // Copy citation
    const copyCitation = async (citation: Citation) => {
      try {
        await navigator.clipboard.writeText(citation.generatedText);
        setCopiedCitation(citation.id);
        setTimeout(() => setCopiedCitation(null), 2000);
      } catch (error) {
        console.error('Failed to copy citation:', error);
      }
    };

    // Export citations
    const handleExport = (format: 'text' | 'bibtex' | 'json') => {
      onExportCitations(format);
    };

    // Citation card component
    const CitationCard = ({ citation, index }: { citation: Citation; index: number }) => {
      const isEditing = editingCitation === citation.id;
      const source = sources.find(s => s.id === citation.sourceId);
      const isCopied = copiedCitation === citation.id;

      const glassClasses = getGlassClasses('card', {
        intensity: 'light',
        depth: 'surface',
        animated: true,
        interactive: true
      });

      return (
        <motion.div
          key={citation.id}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 20 }}
          transition={{ 
            type: "spring" as const,
            stiffness: 300,
            damping: 30,
            delay: index * 0.05 
          }}
          className={cn(
            "group relative",
            glassClasses,
            isEditing && "ring-2 ring-blue-500/50"
          )}
        >
          <div className="p-4">
            {/* Header */}
            <div className="flex items-start justify-between gap-3 mb-3">
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <div className="p-1.5 rounded-md bg-blue-500/20 text-blue-400">
                  <Quote className="w-4 h-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <LuminarText 
                    text={source?.title || 'Unknown Source'}
                    className="font-medium text-sm truncate"
                  />
                  <LuminarText 
                    text={`${citation.style} Style`}
                    className="text-xs text-gray-400"
                  />
                </div>
              </div>
              
              {/* Actions */}
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <LuminarTooltip content={isCopied ? "Copied!" : "Copy citation"}>
                  <LuminarButton
                    size="xs"
                    variant="ghost"
                    onClick={() => copyCitation(citation)}
                  >
                    {isCopied ? <Check className="w-3 h-3" /> : <Copy className="w-3 h-3" />}
                  </LuminarButton>
                </LuminarTooltip>

                <LuminarTooltip content="Edit citation">
                  <LuminarButton
                    size="xs"
                    variant="ghost"
                    onClick={() => startEditing(citation)}
                  >
                    <Edit3 className="w-3 h-3" />
                  </LuminarButton>
                </LuminarTooltip>

                <LuminarTooltip content="Remove citation">
                  <LuminarButton
                    size="xs"
                    variant="ghost"
                    onClick={() => onCitationRemove(citation.id)}
                    className="hover:text-red-400"
                  >
                    <Trash2 className="w-3 h-3" />
                  </LuminarButton>
                </LuminarTooltip>
              </div>
            </div>

            {/* Citation content */}
            <div className="mb-3">
              {isEditing ? (
                <div className="space-y-2">
                  <LuminarTextarea
                    value={editText}
                    onChange={(e) => setEditText(e.target.value)}
                    className="w-full min-h-[80px] text-sm"
                    placeholder="Edit citation text..."
                  />
                  <div className="flex justify-end gap-2">
                    <LuminarButton
                      size="xs"
                      variant="ghost"
                      onClick={cancelEdit}
                    >
                      <X className="w-3 h-3 mr-1" />
                      Cancel
                    </LuminarButton>
                    <LuminarButton
                      size="xs"
                      variant="default"
                      onClick={() => saveEdit(citation.id)}
                    >
                      <Check className="w-3 h-3 mr-1" />
                      Save
                    </LuminarButton>
                  </div>
                </div>
              ) : (
                <div className="p-3 bg-gray-500/10 rounded-lg">
                  <LuminarText 
                    text={citation.generatedText}
                    className="text-sm font-mono text-gray-300"
                  />
                </div>
              )}
            </div>

            {/* Original text preview */}
            {citation.text && (
              <div className="mb-3">
                <LuminarText 
                  text="Original text:"
                  className="text-xs text-gray-400 mb-1"
                />
                <div className="p-2 bg-gray-500/5 rounded border-l-2 border-gray-500/30">
                  <LuminarText 
                    text={`"${citation.text}"`}
                    className="text-xs text-gray-400 line-clamp-2"
                  />
                </div>
              </div>
            )}

            {/* Footer */}
            <div className="flex items-center justify-between gap-2 text-xs text-gray-500">
              <div className="flex items-center gap-2">
                <LuminarBadge variant="default" className="text-xs">
                  {citation.style}
                </LuminarBadge>
                
                {source?.url && (
                  <div className="flex items-center gap-1">
                    <ExternalLink className="w-3 h-3" />
                    <span className="truncate max-w-32">
                      {source.metadata?.domain || new URL(source.url).hostname}
                    </span>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-1">
                <Hash className="w-3 h-3" />
                <span>{citation.id.split('-').pop()}</span>
              </div>
            </div>
          </div>
        </motion.div>
      );
    };

    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col h-full",
          className
        )}
        {...props}
      >
        {/* Header */}
        <div className="p-4 border-b border-white/10">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <LuminarText 
                text="Citations"
                className="text-lg font-semibold"
              />
              <LuminarBadge variant="default">
                {citations.length}
              </LuminarBadge>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Export dropdown */}
              <LuminarDropdown
                trigger={
                  <LuminarButton size="sm" variant="outline">
                    <Download className="w-4 h-4 mr-1" />
                    Export
                  </LuminarButton>
                }
                items={[
                  {
                    id: 'export-text',
                    label: 'Plain Text',
                    icon: FileText,
                    onClick: () => handleExport('text')
                  },
                  {
                    id: 'export-bibtex',
                    label: 'BibTeX',
                    icon: BookOpen,
                    onClick: () => handleExport('bibtex')
                  },
                  {
                    id: 'export-json',
                    label: 'JSON',
                    icon: Hash,
                    onClick: () => handleExport('json')
                  }
                ]}
              />

              {collapsible && (
                <LuminarButton
                  size="sm"
                  variant="ghost"
                  onClick={() => setIsCollapsed(!isCollapsed)}
                >
                  {isCollapsed ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
                </LuminarButton>
              )}
            </div>
          </div>

          {/* Controls */}
          {!isCollapsed && (
            <div className="space-y-3">
              {/* Citation style selector */}
              <div className="flex items-center gap-2">
                <LuminarText 
                  text="Style:"
                  className="text-sm text-gray-400"
                />
                <LuminarSelect
                  value={citationStyle}
                  onChange={(e) => onCitationStyleChange(e.target.value as Citation['style'])}
                  options={[
                    { value: 'APA', label: 'APA' },
                    { value: 'MLA', label: 'MLA' },
                    { value: 'Chicago', label: 'Chicago' },
                    { value: 'Harvard', label: 'Harvard' },
                    { value: 'IEEE', label: 'IEEE' }
                  ]}
                />
              </div>

              {/* Search and filters */}
              <div className="flex items-center gap-2">
                {showSearch && (
                  <div className="relative flex-1">
                    <LuminarInput
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search citations..."
                      size="sm"
                      className="pl-8"
                    />
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  </div>
                )}

                <LuminarSelect
                  value={filterBySource}
                  onChange={(e) => setFilterBySource(e.target.value)}
                  options={[
                    { value: 'all', label: 'All sources' },
                    ...sources.map(source => ({
                      value: source.id,
                      label: source.title
                    }))
                  ]}
                />

                {showGrouping && (
                  <LuminarSelect
                    value={groupBy}
                    onChange={(e) => setGroupBy(e.target.value as 'source' | 'style' | 'none')}
                    options={[
                      { value: 'none', label: 'No grouping' },
                      { value: 'source', label: 'By source' },
                      { value: 'style', label: 'By style' }
                    ]}
                  />
                )}
              </div>
            </div>
          )}
        </div>

        {/* Citations List */}
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="flex-1 overflow-y-auto"
              style={{ maxHeight }}
            >
              <div className="p-4">
                <AnimatePresence mode="wait">
                  {Object.keys(groupedCitations).length > 0 ? (
                    <motion.div
                      key="citations"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="space-y-4"
                    >
                      {Object.entries(groupedCitations).map(([groupName, groupCitations]) => (
                        <div key={groupName} className="space-y-3">
                          {groupBy !== 'none' && (
                            <div className="flex items-center gap-2">
                              <LuminarText 
                                text={groupName}
                                className="text-sm font-medium text-gray-300"
                              />
                              <LuminarBadge variant="default" className="text-xs">
                                {groupCitations.length}
                              </LuminarBadge>
                            </div>
                          )}
                          
                          <div className="space-y-3">
                            {groupCitations.map((citation, index) => (
                              <CitationCard
                                key={citation.id}
                                citation={citation}
                                index={index}
                              />
                            ))}
                          </div>
                        </div>
                      ))}
                    </motion.div>
                  ) : citations.length === 0 ? (
                    <motion.div
                      key="empty"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="flex flex-col items-center justify-center py-12 text-center"
                    >
                      <Quote className="w-12 h-12 text-gray-400 mb-4" />
                      <LuminarText 
                        text="No citations yet"
                        className="text-lg font-medium text-gray-300 mb-2"
                      />
                      <LuminarText 
                        text="Citations will appear here when you add them from sources or conversations."
                        className="text-sm text-gray-500 max-w-md"
                      />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="no-match"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="flex flex-col items-center justify-center py-12 text-center"
                    >
                      <Search className="w-12 h-12 text-gray-400 mb-4" />
                      <LuminarText 
                        text="No citations match your filters"
                        className="text-lg font-medium text-gray-300 mb-2"
                      />
                      <LuminarText 
                        text="Try adjusting your search or filter settings."
                        className="text-sm text-gray-500 max-w-md"
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }
);
ResearchCitations.displayName = "ResearchCitations";

export { ResearchCitations };