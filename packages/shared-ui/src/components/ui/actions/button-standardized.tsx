/**
 * Example of Button component using standardized props interface
 * This demonstrates how to implement the new standardized component props system
 */

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { motion, AnimatePresence } from "framer-motion";
import { Loader2 } from "lucide-react";

import { cn } from '../../../lib/utils';
import { createGlassStyles } from '../../../lib/glass-utils';
import { animationPresets, transitions } from '../../../design-system';
import type {
  ButtonBaseProps, 
  ExtendedHTMLProps,
  AnimationPreset,
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { 
  sizeToPixels,
  sizeToSpacing,
  defaultComponentProps
} from '../../../types/component-props';

/**
 * Button props extending the standardized interface
 */
export interface StandardizedButtonProps extends 
  ButtonBaseProps,
  ExtendedHTMLProps<React.ButtonHTMLAttributes<HTMLButtonElement>> {
  /**
   * Whether to render as a child component (using Radix Slot)
   */
  asChild?: boolean;
  
  /**
   * Whether to show a shimmer effect
   */
  shimmer?: boolean;
  
  /**
   * Whether to show a glow effect
   */
  glow?: boolean;
  
  /**
   * Loading spinner variant
   */
  loadingSpinner?: 'default' | 'dots' | 'pulse';
  
  /**
   * Magnetic hover effect
   */
  magnetic?: boolean;
}

/**
 * Get button classes based on variant and size
 */
const getButtonClasses = (
  variant: ComponentVariant = 'default',
  size: ComponentSize = 'md',
  glass: boolean = false,
  loading: boolean = false,
  disabled: boolean = false
) => {
  const baseClasses = [
    "inline-flex items-center justify-center gap-2",
    "whitespace-nowrap rounded-md font-medium",
    "ring-offset-background transition-all duration-300",
    "focus-visible:outline-hidden focus-visible:ring-2",
    "focus-visible:ring-ring focus-visible:ring-offset-2",
    "relative overflow-hidden",
    "select-none"
  ];

  // Size classes
  const sizeClasses: Record<ComponentSize, string> = {
    xs: "h-7 px-2 py-1 text-xs",
    sm: "h-9 px-3 py-2 text-sm",
    md: "h-10 px-4 py-2 text-sm",
    lg: "h-11 px-6 py-3 text-base",
    xl: "h-12 px-8 py-4 text-lg"
  };

  // Variant classes
  const variantClasses: Record<ComponentVariant, string> = {
    default: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-md hover:shadow-lg",
    primary: "bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
    ghost: "hover:bg-accent hover:text-accent-foreground",
    glass: glass ? "" : "backdrop-blur-md bg-white/10 dark:bg-white/5 border border-white/20 dark:border-white/10",
    gradient: "bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700",
    destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-md hover:shadow-lg",
    success: "bg-green-600 text-white hover:bg-green-700 shadow-md hover:shadow-lg",
    warning: "bg-yellow-600 text-white hover:bg-yellow-700 shadow-md hover:shadow-lg",
    info: "bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg"
  };

  // State classes
  const stateClasses = [];
  if (disabled) {
    stateClasses.push("disabled:pointer-events-none disabled:opacity-50");
  }
  if (loading) {
    stateClasses.push("cursor-wait");
  }

  return cn(
    ...baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    ...stateClasses
  );
};

/**
 * Get animation configuration
 */
const getAnimationConfig = (
  animation: AnimationPreset = 'fadeIn',
  disableAnimation: boolean = false,
  duration?: number,
  delay?: number
) => {
  if (disableAnimation || animation === 'none') {
    return {};
  }

  const baseAnimation = animationPresets[animation] || animationPresets.fadeIn;
  
  return {
    ...baseAnimation,
    transition: {
      ...transitions.default,
      duration: duration || transitions.default.duration,
      delay: delay || 0
    }
  };
};

/**
 * Standardized Button Component
 * 
 * This button implements the standardized component props interface,
 * providing a consistent API across all Luminar UI components.
 */
const StandardizedButton = React.forwardRef<HTMLButtonElement, StandardizedButtonProps>(
  ({
    // Destructure standard props
    className,
    id,
    style,
    'data-testid': dataTestId,
    'aria-label': ariaLabel,
    'aria-describedby': ariaDescribedBy,
    
    // Variant props
    variant = defaultComponentProps.variant,
    size = defaultComponentProps.size,
    
    // Glass props
    glass = defaultComponentProps.glass,
    glassIntensity = defaultComponentProps.glassIntensity,
    glassDepth,
    glassConfig,
    
    // Animation props
    animation = defaultComponentProps.animation,
    disableAnimation = defaultComponentProps.disableAnimation,
    animationDuration,
    animationDelay,
    motionProps,
    
    // Interactive props
    disabled = false,
    loading = false,
    loadingText,
    interactive = defaultComponentProps.interactive,
    hoverable = defaultComponentProps.hoverable,
    
    // Icon props
    icon: Icon,
    iconPosition = 'left',
    iconSize,
    iconAnimation,
    
    // Button specific props
    type = 'button',
    onClick,
    fullWidth = false,
    asChild = false,
    shimmer = false,
    glow = false,
    loadingSpinner = 'default',
    magnetic = false,
    
    // Children and other props
    children,
    ...props
  }, ref) => {
    const [isHovered, setIsHovered] = React.useState(false);
    const [isPressed, setIsPressed] = React.useState(false);
    
    // Get classes
    const buttonClasses = getButtonClasses(variant, size, glass, loading, disabled);
    
    // Apply glass styles if enabled
    const glassStyles = glass ? createGlassStyles({
      element: 'button',
      profile: glassIntensity === 'subtle' ? 'soft' : 
               glassIntensity === 'intense' ? 'hard' : 'standard',
      interactive: interactive && !disabled && !loading,
      ...glassConfig
    }) : '';
    
    // Get animation configuration
    const animationConfig = getAnimationConfig(
      animation,
      disableAnimation,
      animationDuration,
      animationDelay
    );
    
    // Calculate icon size based on component size
    const calculatedIconSize = iconSize || sizeToPixels[size] * 0.5;
    
    // Loading spinner component
    const LoadingSpinner = () => (
      <motion.div
        className="absolute inset-0 flex items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        {loadingSpinner === 'default' ? (
          <Loader2 className="animate-spin" size={calculatedIconSize} />
        ) : loadingSpinner === 'dots' ? (
          <div className="flex gap-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-current rounded-full"
                animate={{ opacity: [0.3, 1, 0.3] }}
                transition={{ duration: 0.8, delay: i * 0.2, repeat: Infinity }}
              />
            ))}
          </div>
        ) : (
          <motion.div
            className="w-6 h-6 border-2 border-current border-t-transparent rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
        )}
      </motion.div>
    );
    
    // Hover and interaction handlers
    const handleMouseEnter = () => {
      if (hoverable && !disabled && !loading) {
        setIsHovered(true);
      }
    };
    
    const handleMouseLeave = () => {
      setIsHovered(false);
      setIsPressed(false);
    };
    
    const handleMouseDown = () => {
      if (interactive && !disabled && !loading) {
        setIsPressed(true);
      }
    };
    
    const handleMouseUp = () => {
      setIsPressed(false);
    };
    
    const handleClick = () => {
      if (!disabled && !loading && onClick) {
        onClick();
      }
    };
    
    // Component to render
    const Comp = asChild ? Slot : "button";
    
    // Motion wrapper for animations
    const MotionComp = motion(Comp);
    
    return (
      <MotionComp
        ref={ref}
        id={id}
        type={!asChild ? type : undefined}
        className={cn(
          buttonClasses,
          glass && glassStyles,
          fullWidth && "w-full",
          className
        )}
        style={style}
        disabled={disabled || loading}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        data-testid={dataTestId}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
        aria-busy={loading}
        {...animationConfig}
        {...motionProps}
        animate={{
          ...animationConfig.animate,
          scale: isPressed ? 0.98 : isHovered && hoverable ? 1.02 : 1,
          ...(magnetic && isHovered && {
            x: 0, // Would need mouse position for true magnetic effect
            y: 0
          })
        }}
        transition={{
          ...animationConfig.transition,
          scale: { duration: 0.1 }
        }}
        {...props}
      >
        {/* Loading overlay */}
        <AnimatePresence>
          {loading && <LoadingSpinner />}
        </AnimatePresence>
        
        {/* Button content */}
        <span className={cn(
          "relative z-10 flex items-center justify-center gap-2",
          loading && "opacity-0"
        )}>
          {/* Icon on the left */}
          {Icon && iconPosition === 'left' && (
            <Icon 
              size={calculatedIconSize}
              className={cn(
                "shrink-0",
                iconAnimation && !disableAnimation && `animate-${iconAnimation}`
              )}
            />
          )}
          
          {/* Children or loading text */}
          {loading && loadingText ? loadingText : children}
          
          {/* Icon on the right */}
          {Icon && iconPosition === 'right' && (
            <Icon 
              size={calculatedIconSize}
              className={cn(
                "shrink-0",
                iconAnimation && !disableAnimation && `animate-${iconAnimation}`
              )}
            />
          )}
        </span>
        
        {/* Shimmer effect */}
        {shimmer && !disabled && (
          <motion.div
            className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent"
            animate={{ x: ['0%', '200%'] }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          />
        )}
        
        {/* Glow effect */}
        {glow && !disabled && (
          <div className="absolute inset-0 rounded-md bg-current opacity-25 blur-xl -z-10" />
        )}
      </MotionComp>
    );
  }
);
StandardizedButton.displayName = "StandardizedButton";

export { StandardizedButton };

/**
 * Usage examples:
 * 
 * Basic usage:
 * <StandardizedButton>Click me</StandardizedButton>
 * 
 * With variant and size:
 * <StandardizedButton variant="primary" size="lg">Large Primary</StandardizedButton>
 * 
 * With icon:
 * <StandardizedButton icon={Save} iconPosition="right">Save Changes</StandardizedButton>
 * 
 * With loading state:
 * <StandardizedButton loading loadingText="Saving...">Save</StandardizedButton>
 * 
 * With glass effect:
 * <StandardizedButton glass glassIntensity="strong">Glass Button</StandardizedButton>
 * 
 * With animation:
 * <StandardizedButton animation="bounce" icon={Star}>Animated</StandardizedButton>
 * 
 * Full example:
 * <StandardizedButton
 *   variant="gradient"
 *   size="lg"
 *   icon={Rocket}
 *   loading={isLoading}
 *   onClick={handleLaunch}
 *   glass
 *   shimmer
 *   animation="scale"
 * >
 *   Launch Application
 * </StandardizedButton>
 */