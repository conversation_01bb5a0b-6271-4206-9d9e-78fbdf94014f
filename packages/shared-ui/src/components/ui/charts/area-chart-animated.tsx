import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { getAdaptiveGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface AreaChartData {
  label: string;
  value: number;
}

export interface AnimatedAreaChartProps {
  data: AreaChartData[];
  width?: number;
  height?: number;
  color?: string;
  gradientColor?: string;
  showGrid?: boolean;
  showLabels?: boolean;
  showValues?: boolean;
  animate?: boolean;
  animationDuration?: number;
  className?: string;
}

export function LuminarAnimatedAreaChart({
  data,
  width = 600,
  height = 300,
  color = '#3b82f6',
  gradientColor = '#3b82f640',
  showGrid = true,
  showLabels = true,
  showValues = false,
  animate = true,
  animationDuration = 1.5,
  className = ''
}: AnimatedAreaChartProps) {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const glassClasses = getAdaptiveGlassClasses('card', 'md', 'neutral');

  // Chart dimensions
  const padding = { top: 20, right: 20, bottom: 40, left: 50 };
  const chartWidth = width - padding.left - padding.right;
  const chartHeight = height - padding.top - padding.bottom;

  // Calculate scales
  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(0, ...data.map(d => d.value));
  const valueRange = maxValue - minValue;

  const xScale = (index: number) => (index / (data.length - 1)) * chartWidth;
  const yScale = (value: number) => chartHeight - ((value - minValue) / valueRange) * chartHeight;

  // Generate path
  const path = useMemo(() => {
    const points = data.map((d, i) => `${xScale(i)},${yScale(d.value)}`);
    return `M ${points.join(' L ')}`;
  }, [data]);

  // Generate area path
  const areaPath = useMemo(() => {
    const points = data.map((d, i) => `${xScale(i)},${yScale(d.value)}`);
    return `M ${xScale(0)},${yScale(0)} L ${points.join(' L ')} L ${xScale(data.length - 1)},${yScale(0)} Z`;
  }, [data]);

  // Grid lines
  const gridLines = useMemo(() => {
    const lines = [];
    const gridCount = 5;
    for (let i = 0; i <= gridCount; i++) {
      const y = (i / gridCount) * chartHeight;
      const value = maxValue - (i / gridCount) * valueRange;
      lines.push({ y, value });
    }
    return lines;
  }, [maxValue, valueRange, chartHeight]);

  return (
    <div className={`${glassClasses} p-4 rounded-lg border ${className}`}>
      <svg width={width} height={height}>
        <defs>
          <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={color} stopOpacity="0.6" />
            <stop offset="100%" stopColor={color} stopOpacity="0.1" />
          </linearGradient>
        </defs>

        <g transform={`translate(${padding.left},${padding.top})`}>
          {/* Grid lines */}
          {showGrid && (
            <g className="grid">
              {gridLines.map((line, i) => (
                <g key={i}>
                  <motion.line
                    x1={0}
                    y1={line.y}
                    x2={chartWidth}
                    y2={line.y}
                    stroke="var(--border)"
                    strokeWidth="1"
                    opacity="0.3"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 0.3 }}
                    transition={{ delay: i * 0.1 }}
                  />
                  <motion.text
                    x={-10}
                    y={line.y + 4}
                    textAnchor="end"
                    fontSize="10"
                    fill="currentColor"
                    className="text-muted-foreground"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: -10 }}
                    transition={{ delay: i * 0.1 }}
                  >
                    {line.value.toFixed(0)}
                  </motion.text>
                </g>
              ))}
            </g>
          )}

          {/* Area */}
          <motion.path
            d={areaPath}
            fill="url(#areaGradient)"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: animationDuration * 0.8 }}
          />

          {/* Line */}
          <motion.path
            d={path}
            fill="none"
            stroke={color}
            strokeWidth="3"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: animationDuration, ease: "easeInOut" }}
          />

          {/* Data points */}
          {data.map((d, i) => (
            <g key={i}>
              <motion.circle
                cx={xScale(i)}
                cy={yScale(d.value)}
                r={hoveredIndex === i ? 6 : 4}
                fill={color}
                stroke="var(--background)"
                strokeWidth="2"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ 
                  delay: (i / data.length) * animationDuration,
                  type: "spring",
                  stiffness: 300,
                  damping: 10
                }}
                onMouseEnter={() => setHoveredIndex(i)}
                onMouseLeave={() => setHoveredIndex(null)}
                className="cursor-pointer"
              />

              {/* Value tooltip */}
              <AnimatePresence>
                {(showValues || hoveredIndex === i) && (
                  <motion.g
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <rect
                      x={xScale(i) - 30}
                      y={yScale(d.value) - 30}
                      width="60"
                      height="24"
                      rx="4"
                      fill="var(--popover)"
                      stroke="var(--border)"
                      strokeWidth="1"
                    />
                    <text
                      x={xScale(i)}
                      y={yScale(d.value) - 12}
                      textAnchor="middle"
                      fontSize="12"
                      fill="currentColor"
                      className="text-popover-foreground font-medium"
                    >
                      {d.value}
                    </text>
                  </motion.g>
                )}
              </AnimatePresence>

              {/* X-axis labels */}
              {showLabels && (
                <motion.text
                  x={xScale(i)}
                  y={chartHeight + 20}
                  textAnchor="middle"
                  fontSize="10"
                  fill="currentColor"
                  className="text-muted-foreground"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: (i / data.length) * animationDuration }}
                >
                  {d.label}
                </motion.text>
              )}
            </g>
          ))}
        </g>
      </svg>
    </div>
  );
}