import React from "react"
import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes, createContext, useContext, useId, ReactNode } from "react"

import { 
  useForm, 
  FormProvider, 
  useFormContext, 
  FieldPath, 
  FieldValues, 
  UseFormReturn,
  ControllerProps,
  FieldError
} from "react-hook-form"
import { 
  type GlassVariant, 
  type GlassIntensity,
  type ColorTheme,
  getGlassClasses, 
  animationPresets, 
  transitions,
  componentSizes
} from '../../../design-system'
import { microInteractions } from '../../../lib/micro-interactions'

import { LuminarButton } from "../actions/button-advanced"
import { LuminarInput } from "../forms/input"
import { LuminarTextarea } from "../forms/textarea"
import { LuminarSelect } from "../forms/select"
import { LuminarCheckbox } from "../forms/checkbox"
import { LuminarRadioGroup } from "../forms/radio-group"
import { LuminarSwitch } from "../forms/switch"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

// Form context for field items
interface FormFieldContextValue {
  name: string
  error?: FieldError
  required?: boolean
  description?: string
}

const FormFieldContext = createContext<FormFieldContextValue | undefined>(undefined)

const useFormField = () => {
  const context = useContext(FormFieldContext)
  if (!context) {
    throw new Error("useFormField must be used within a FormField")
  }
  return context
}

// Root Form component
export interface LuminarFormProps extends HTMLAttributes<HTMLFormElement> {
  variant?: GlassVariant
  size?: ComponentSize
  glassIntensity?: GlassIntensity
  colorTheme?: ColorTheme
  animation?: keyof typeof animationPresets
  disableAnimation?: boolean
  spacing?: "tight" | "normal" | "relaxed"
}

const LuminarForm = forwardRef<HTMLFormElement, LuminarFormProps>(
  ({ 
    className,
    variant = "card",
    size = "md",
    glassIntensity = "medium",
    colorTheme = "neutral",
    animation = "fadeIn",
    disableAnimation = false,
    spacing = "normal",
    children,
    ...props 
  }, ref) => {
    // Generate glass classes
    const glassClasses = getGlassClasses(variant, {
      intensity: glassIntensity,
      depth: "surface",
      animated: !disableAnimation,
      interactive: true
    })

    const sizeClasses = componentSizes[size]
    const animationProps = disableAnimation ? {} : animationPresets[animation]

    const spacingClasses = {
      tight: "space-y-4",
      normal: "space-y-6",
      relaxed: "space-y-8"
    }

    return (
      <motion.form
        ref={ref}
        className={cn(
          "w-full",
          glassClasses,
          sizeClasses.padding,
          spacingClasses[spacing],
          className
        )}
        {...(animationProps as any)}
        transition={transitions.spring}
        {...props}
      >
        {children}
      </motion.form>
    )
  }
)

// Form Provider wrapper
export interface LuminarFormProviderProps<T extends FieldValues> {
  form: UseFormReturn<T>
  children: ReactNode
}

function LuminarFormProvider<T extends FieldValues>({ form, children }: LuminarFormProviderProps<T>) {
  return (
    <FormProvider {...form}>
      {children}
    </FormProvider>
  )
}

// Form Field component
export interface LuminarFormFieldProps<T extends FieldValues> extends HTMLAttributes<HTMLDivElement> {
  name: FieldPath<T>
  label?: string
  description?: string
  required?: boolean
  layout?: "vertical" | "horizontal"
  labelWidth?: string
}

function LuminarFormField<T extends FieldValues>({ 
  name,
  label,
  description,
  required = false,
  layout = "vertical",
  labelWidth = "auto",
  className,
  children,
  ...props 
}: LuminarFormFieldProps<T>) {
  const context = useFormContext<T>()
  const error = context?.formState?.errors?.[name] as FieldError | undefined
  const fieldId = useId()

  const contextValue: FormFieldContextValue = {
    name,
    error,
    required,
    description
  }

  return (
    <FormFieldContext.Provider value={contextValue}>
      <div 
        className={cn(
          "space-y-2",
          layout === "horizontal" && "flex items-start gap-4",
          className
        )}
        {...props}
      >
        {label && (
          <div 
            className={cn(
              layout === "horizontal" && "flex-shrink-0",
              layout === "horizontal" && labelWidth !== "auto" && `w-[${labelWidth}]`
            )}
          >
            <label 
              htmlFor={fieldId}
              className={cn(
                "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                error && "text-destructive"
              )}
            >
              {label}
              {required && <span className="text-destructive ml-1">*</span>}
            </label>
          </div>
        )}
        
        <div className={cn(layout === "horizontal" && "flex-1")}>
          {React.Children.map(children, child => {
            if (React.isValidElement(child)) {
              const childProps = child.props || {};
              return React.cloneElement(child as React.ReactElement<any>, {
                ...childProps,
                id: fieldId,
                "aria-describedby": description ? `${fieldId}-description` : undefined,
                "aria-invalid": error ? "true" : "false"
              } as any)
            }
            return child
          })}
          
          {description && (
            <p 
              id={`${fieldId}-description`}
              className="text-sm text-muted-foreground"
            >
              {description}
            </p>
          )}
          
          {error && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="text-sm text-destructive"
            >
              {error.message}
            </motion.p>
          )}
        </div>
      </div>
    </FormFieldContext.Provider>
  )
}

// Form Control component (for custom controls)
export interface LuminarFormControlProps extends HTMLAttributes<HTMLDivElement> {
  render: (field: { value: any; onChange: (value: any) => void; onBlur: () => void }) => ReactNode
}

function LuminarFormControl<T extends FieldValues>({ render, ...props }: LuminarFormControlProps) {
  const { name } = useFormField()
  const context = useFormContext<T>()
  const control = context?.control

  return (
    <div {...props}>
      {/* This would need proper Controller integration */}
      {/* For now, this is a placeholder structure */}
      {render({ 
        value: "", 
        onChange: () => {}, 
        onBlur: () => {} 
      })}
    </div>
  )
}

// Form Input component
export interface LuminarFormInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  variant?: ComponentVariant | "glass"
  size?: ComponentSize
  glassIntensity?: GlassIntensity
  colorTheme?: ColorTheme
}

const LuminarFormInput = forwardRef<HTMLInputElement, LuminarFormInputProps>(
  ({ 
    className,
    variant = "default",
    size = "md",
    glassIntensity = "light",
    colorTheme = "neutral",
    ...props 
  }, ref) => {
    const { name, error } = useFormField()
    const context = useFormContext()
    const register = context?.register

    const registerProps = register ? register(name) : null;
    const registerRef = registerProps?.ref;
    const registerRest = registerProps ? { ...registerProps, ref: undefined } : {};

    return (
      <LuminarInput
        ref={registerRef || ref}
        className={cn(
          error && "border-destructive focus:border-destructive",
          className
        )}
        variant={variant === "glass" ? "input" : "default"}
        size={size}
        glassIntensity={glassIntensity}
        {...registerRest}
        {...props}
      />
    )
  }
)

// Form Textarea component
export interface LuminarFormTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  variant?: ComponentVariant | "glass"
  size?: ComponentSize
  glassIntensity?: GlassIntensity
  colorTheme?: ColorTheme
}

const LuminarFormTextarea = forwardRef<HTMLTextAreaElement, LuminarFormTextareaProps>(
  ({ 
    className,
    variant = "default",
    size = "md",
    glassIntensity = "light",
    colorTheme = "neutral",
    ...props 
  }, ref) => {
    const { name, error } = useFormField()
    const context = useFormContext()
    const register = context?.register

    const registerProps = register ? register(name) : null;
    const registerRef = registerProps?.ref;
    const registerRest = registerProps ? { ...registerProps, ref: undefined } : {};

    return (
      <LuminarTextarea
        ref={registerRef || ref}
        className={cn(
          error && "border-destructive focus:border-destructive",
          className
        )}
        {...registerRest}
        {...props}
      />
    )
  }
)

// Form Select component
export interface LuminarFormSelectProps {
  placeholder?: string
  options: Array<{ value: string; label: string }>
  variant?: ComponentVariant | "glass"
  size?: ComponentSize
  glassIntensity?: GlassIntensity
  colorTheme?: ColorTheme
}

const LuminarFormSelect = forwardRef<HTMLSelectElement, LuminarFormSelectProps>(
  ({ 
    placeholder = "Select an option",
    options,
    variant = "default",
    size = "md",
    glassIntensity = "light",
    colorTheme = "neutral",
    ...props 
  }, ref) => {
    const { name, error } = useFormField()
    const context = useFormContext()
    const { register, setValue, watch } = context || {}
    const value = watch?.(name)

    return (
      <LuminarSelect
        ref={ref}
        placeholder={placeholder}
        value={value}
        onChange={(e) => setValue?.(name, e.target.value)}
        className={cn(
          error && "border-destructive focus:border-destructive"
        )}
        options={options}
        {...props}
      />
    )
  }
)

// Form Checkbox component
export interface LuminarFormCheckboxProps {
  label?: string
  size?: ComponentSize
  glassIntensity?: GlassIntensity
  colorTheme?: ColorTheme
}

const LuminarFormCheckbox = forwardRef<HTMLInputElement, LuminarFormCheckboxProps>(
  ({ 
    label,
    size = "md",
    glassIntensity = "light",
    colorTheme = "neutral",
    ...props 
  }, ref) => {
    const { name, error } = useFormField()
    const context = useFormContext()
    const { register, watch } = context || {}
    const isChecked = watch?.(name)

    return (
      <div className="flex items-center space-x-2">
        <LuminarCheckbox
          checked={isChecked}
          className={cn(
            error && "border-destructive"
          )}
          {...(register ? { ...register(name), ref: ref || register(name).ref } : { ref })}
          {...props}
        />
        {label && (
          <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {label}
          </label>
        )}
      </div>
    )
  }
)

// Form Switch component
export interface LuminarFormSwitchProps {
  label?: string
  size?: ComponentSize
  glassIntensity?: GlassIntensity
  colorTheme?: ColorTheme
}

const LuminarFormSwitch = forwardRef<HTMLInputElement, LuminarFormSwitchProps>(
  ({ 
    label,
    size = "md",
    glassIntensity = "light",
    colorTheme = "neutral",
    ...props 
  }, ref) => {
    const { name, error } = useFormField()
    const context = useFormContext()
    const { register, watch, setValue } = context || {}
    const isChecked = watch?.(name)

    return (
      <div className="flex items-center space-x-2">
        <LuminarSwitch
          ref={ref}
          checked={isChecked}
          onCheckedChange={(checked) => setValue?.(name, checked)}
          size={size === "xs" ? "sm" : size === "xl" ? "lg" : size}
          className={cn(
            error && "border-destructive"
          )}
          {...props}
        />
        {label && (
          <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {label}
          </label>
        )}
      </div>
    )
  }
)

// Form Submit Button component
export interface LuminarFormSubmitProps {
  children: ReactNode
  variant?: ComponentVariant | "destructive" | "outline" | "secondary" | "ghost" | "link" | "glass"
  size?: ComponentSize
  loading?: boolean
  disabled?: boolean
  className?: string
}

const LuminarFormSubmit = forwardRef<HTMLButtonElement, LuminarFormSubmitProps>(
  ({ 
    children,
    variant = "default",
    size = "md",
    loading = false,
    disabled = false,
    className,
    ...props 
  }, ref) => {
    const context = useFormContext()
  const formState = context?.formState
    const isSubmitting = formState?.isSubmitting || loading

    return (
      <LuminarButton
        ref={ref}
        type="submit"
        variant={variant}
        size={size}
        loading={isSubmitting}
        disabled={disabled || isSubmitting}
        className={className}
        {...props}
      >
        {children}
      </LuminarButton>
    )
  }
)

// Form Reset Button component
export interface LuminarFormResetProps {
  children: ReactNode
  variant?: ComponentVariant | "destructive" | "outline" | "secondary" | "ghost" | "link" | "glass"
  size?: ComponentSize
  disabled?: boolean
  className?: string
}

const LuminarFormReset = forwardRef<HTMLButtonElement, LuminarFormResetProps>(
  ({ 
    children,
    variant = "outline",
    size = "md",
    disabled = false,
    className,
    ...props 
  }, ref) => {
    const context = useFormContext()
  const reset = context?.reset

    return (
      <LuminarButton
        ref={ref}
        type="button"
        variant={variant}
        size={size}
        disabled={disabled}
        className={className}
        onClick={() => reset?.()}
        {...props}
      >
        {children}
      </LuminarButton>
    )
  }
)

// Form Actions component (for button groups)
export interface LuminarFormActionsProps extends HTMLAttributes<HTMLDivElement> {
  justify?: "start" | "center" | "end" | "between"
  spacing?: "tight" | "normal" | "relaxed"
}

const LuminarFormActions = forwardRef<HTMLDivElement, LuminarFormActionsProps>(
  ({ 
    className,
    justify = "end",
    spacing = "normal",
    children,
    ...props 
  }, ref) => {
    const justifyClasses = {
      start: "justify-start",
      center: "justify-center",
      end: "justify-end",
      between: "justify-between"
    }

    const spacingClasses = {
      tight: "gap-2",
      normal: "gap-4",
      relaxed: "gap-6"
    }

    return (
      <div
        ref={ref}
        className={cn(
          "flex items-center",
          justifyClasses[justify],
          spacingClasses[spacing],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

// Set display names
LuminarForm.displayName = "LuminarForm"
LuminarFormProvider.displayName = "LuminarFormProvider"
LuminarFormField.displayName = "LuminarFormField"
LuminarFormControl.displayName = "LuminarFormControl"
LuminarFormInput.displayName = "LuminarFormInput"
LuminarFormTextarea.displayName = "LuminarFormTextarea"
LuminarFormSelect.displayName = "LuminarFormSelect"
LuminarFormCheckbox.displayName = "LuminarFormCheckbox"
LuminarFormSwitch.displayName = "LuminarFormSwitch"
LuminarFormSubmit.displayName = "LuminarFormSubmit"
LuminarFormReset.displayName = "LuminarFormReset"
LuminarFormActions.displayName = "LuminarFormActions"

export {
  LuminarForm,
  LuminarFormProvider,
  LuminarFormField,
  LuminarFormControl,
  LuminarFormInput,
  LuminarFormTextarea,
  LuminarFormSelect,
  LuminarFormCheckbox,
  LuminarFormSwitch,
  LuminarFormSubmit,
  LuminarFormReset,
  LuminarFormActions,
  useFormField,
}

// Simplified API for quick usage (shadcn/ui style)
export const Form = LuminarForm
export const FormField = LuminarFormField
export const FormControl = LuminarFormControl
export const FormInput = LuminarFormInput
export const FormTextarea = LuminarFormTextarea
export const FormSelect = LuminarFormSelect
export const FormCheckbox = LuminarFormCheckbox
export const FormSwitch = LuminarFormSwitch
export const FormSubmit = LuminarFormSubmit
export const FormReset = LuminarFormReset
export const FormActions = LuminarFormActions

// Re-export from react-hook-form for convenience
export { useForm, type UseFormReturn, type FieldValues, FormProvider } from "react-hook-form"