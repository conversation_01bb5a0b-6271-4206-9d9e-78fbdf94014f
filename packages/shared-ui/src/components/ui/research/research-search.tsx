import { motion, AnimatePresence } from "framer-motion";
import { forwardRef, useEffect, useMemo, useState } from "react";
import {
  Search, 
  Filter, 
  Clock, 
  Globe, 
  BookOpen, 
  Database, 
  FileText,
  ExternalLink,
  Plus,
  Star,
  Download,
  Share2,
  MoreVertical
} from "lucide-react";
import { cn } from '../../../lib/utils';
import { LuminarButton } from "../actions/button-advanced";
import { LuminarInput } from "../forms/input";
import { LuminarCard } from "../display/card";
import { LuminarText } from "../display/text";
import { LuminarBadge } from "../display/badge";
import { LuminarTooltip } from "../feedback/tooltip";
import { LuminarSelect } from "../forms/select";
import { LuminarDropdown } from "../actions/dropdown";
import { LoadingSpinner } from "../display/loading-spinner";
import {
  getGlassClasses, 
  animationPresets, 
  type ComponentSize
} from "../../../design-system";
import { ResearchSource } from '../../../types/research';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface ResearchSearchProps {
  query: string;
  results: ResearchSource[];
  searchHistory: string[];
  isSearching: boolean;
  onQueryChange: (query: string) => void;
  onSearch: (query: string) => void;
  onAddSource: (source: ResearchSource) => void;
  onSelectSource: (source: ResearchSource) => void;
  onClearSearch: () => void;
  className?: string;
  size?: ComponentSize;
  disabled?: boolean;
  placeholder?: string;
  showFilters?: boolean;
  maxResults?: number;
}

const ResearchSearch = forwardRef<HTMLDivElement, ResearchSearchProps>(
  ({
    query,
    results,
    searchHistory,
    isSearching,
    onQueryChange,
    onSearch,
    onAddSource,
    onSelectSource,
    onClearSearch,
    className,
    size = "md",
    disabled = false,
    placeholder = "Search for research sources...",
    showFilters = true,
    maxResults = 20,
    ...props
  }, ref) => {
    const [activeFilters, setActiveFilters] = useState({
      type: 'all' as ResearchSource['type'] | 'all',
      dateRange: 'all' as 'all' | 'recent' | 'year' | 'custom',
      sortBy: 'relevance' as 'relevance' | 'date' | 'title'
    });
    const [showHistory, setShowHistory] = useState(false);

    // Filter and sort results
    const filteredResults = useMemo(() => {
      let filtered = results;

      // Filter by type
      if (activeFilters.type !== 'all') {
        filtered = filtered.filter(source => source.type === activeFilters.type);
      }

      // Filter by date range
      if (activeFilters.dateRange !== 'all') {
        const now = new Date();
        const cutoff = new Date();
        
        switch (activeFilters.dateRange) {
          case 'recent':
            cutoff.setDate(now.getDate() - 7);
            break;
          case 'year':
            cutoff.setFullYear(now.getFullYear() - 1);
            break;
        }
        
        filtered = filtered.filter(source => 
          source.metadata?.publishDate && source.metadata.publishDate >= cutoff
        );
      }

      // Sort results
      switch (activeFilters.sortBy) {
        case 'relevance':
          filtered.sort((a, b) => b.relevanceScore - a.relevanceScore);
          break;
        case 'date':
          filtered.sort((a, b) => {
            const dateA = a.metadata?.publishDate || new Date(0);
            const dateB = b.metadata?.publishDate || new Date(0);
            return dateB.getTime() - dateA.getTime();
          });
          break;
        case 'title':
          filtered.sort((a, b) => a.title.localeCompare(b.title));
          break;
      }

      return filtered.slice(0, maxResults);
    }, [results, activeFilters, maxResults]);

    // Handle search submission
    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (query.trim() && !disabled && !isSearching) {
        onSearch(query.trim());
        setShowHistory(false);
      }
    };

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Escape') {
        setShowHistory(false);
      }
    };

    // Get source type icon
    const getSourceTypeIcon = (type: ResearchSource['type']) => {
      switch (type) {
        case 'web':
          return Globe;
        case 'academic':
          return BookOpen;
        case 'database':
          return Database;
        case 'pdf':
        case 'doc':
          return FileText;
        default:
          return FileText;
      }
    };

    // Get source type color
    const getSourceTypeColor = (type: ResearchSource['type']) => {
      switch (type) {
        case 'web':
          return 'bg-blue-500/20 text-blue-400';
        case 'academic':
          return 'bg-purple-500/20 text-purple-400';
        case 'database':
          return 'bg-green-500/20 text-green-400';
        case 'pdf':
        case 'doc':
          return 'bg-orange-500/20 text-orange-400';
        default:
          return 'bg-gray-500/20 text-gray-400';
      }
    };

    // Source result component
    const SourceResult = ({ source, index }: { source: ResearchSource; index: number }) => {
      const glassClasses = getGlassClasses('card', {
        intensity: 'light',
        depth: 'surface',
        animated: true,
        interactive: true
      });

      const TypeIcon = getSourceTypeIcon(source.type);
      const typeColor = getSourceTypeColor(source.type);

      return (
        <motion.div
          key={source.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ 
            type: "spring" as const,
            stiffness: 300,
            damping: 30,
            delay: index * 0.05 
          }}
          className={cn(
            "group relative cursor-pointer",
            glassClasses,
            "hover:scale-[1.02] transition-transform duration-200"
          )}
          onClick={() => onSelectSource(source)}
        >
          <div className="p-4">
            {/* Header */}
            <div className="flex items-start justify-between gap-3 mb-3">
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <div className={cn("p-1.5 rounded-md", typeColor)}>
                  <TypeIcon className="w-4 h-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <LuminarText 
                    text={source.title}
                    className="font-medium truncate"
                  />
                  {source.metadata?.author && (
                    <LuminarText 
                      text={`by ${source.metadata.author}`}
                      className="text-xs text-gray-400 truncate"
                    />
                  )}
                </div>
              </div>
              
              {/* Actions */}
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <LuminarTooltip content="Add to sources">
                  <LuminarButton
                    size="xs"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      onAddSource(source);
                    }}
                  >
                    <Plus className="w-3 h-3" />
                  </LuminarButton>
                </LuminarTooltip>
                
                <LuminarDropdown
                  trigger={
                    <LuminarButton
                      size="xs"
                      variant="ghost"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreVertical className="w-3 h-3" />
                    </LuminarButton>
                  }
                  items={[
                    {
                      id: 'save',
                      label: 'Save for later',
                      icon: Star,
                      onClick: () => {}
                    },
                    {
                      id: 'download',
                      label: 'Download',
                      icon: Download,
                      onClick: () => {}
                    },
                    {
                      id: 'share',
                      label: 'Share',
                      icon: Share2,
                      onClick: () => {}
                    }
                  ]}
                />
              </div>
            </div>

            {/* Content */}
            <div className="mb-3">
              <LuminarText 
                text={source.snippet}
                className="text-sm text-gray-300 line-clamp-2"
              />
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between gap-2 text-xs text-gray-500">
              <div className="flex items-center gap-3">
                <LuminarBadge variant="default" className="capitalize">
                  {source.type}
                </LuminarBadge>
                
                <div className="flex items-center gap-1">
                  <Star className="w-3 h-3" />
                  <span>{(source.relevanceScore * 100).toFixed(0)}%</span>
                </div>
                
                {source.metadata?.publishDate && (
                  <span>
                    {source.metadata.publishDate.toLocaleDateString()}
                  </span>
                )}
              </div>
              
              {source.url && (
                <div className="flex items-center gap-1">
                  <ExternalLink className="w-3 h-3" />
                  <span className="truncate max-w-24">
                    {source.metadata?.domain || new URL(source.url).hostname}
                  </span>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      );
    };

    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col h-full",
          className
        )}
        {...props}
      >
        {/* Search Header */}
        <div className="p-4 border-b border-white/10">
          <form onSubmit={handleSubmit} className="relative mb-4">
            <div className="relative">
              <LuminarInput
                value={query}
                onChange={(e) => onQueryChange(e.target.value)}
                onKeyDown={handleKeyDown}
                onFocus={() => setShowHistory(true)}
                placeholder={placeholder}
                disabled={disabled}
                size={size}
                variant="input"
                className="w-full pl-10 pr-10"
              />
              
              {/* Search icon */}
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              
              {/* Search button */}
              <LuminarButton
                type="submit"
                size="xs"
                variant="ghost"
                disabled={disabled || isSearching || !query.trim()}
                className="absolute right-1 top-1/2 transform -translate-y-1/2"
              >
                {isSearching ? <LoadingSpinner size="sm" /> : <Search className="w-4 h-4" />}
              </LuminarButton>
            </div>

            {/* Search history dropdown */}
            <AnimatePresence>
              {showHistory && searchHistory.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full left-0 right-0 z-10 mt-2 bg-gray-900/90 backdrop-blur-md border border-white/20 rounded-lg shadow-lg max-h-40 overflow-y-auto"
                >
                  <div className="p-2">
                    <LuminarText 
                      text="Recent searches"
                      className="text-xs text-gray-400 mb-2 px-2"
                    />
                    {searchHistory.map((historyQuery, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          onQueryChange(historyQuery);
                          onSearch(historyQuery);
                          setShowHistory(false);
                        }}
                        className="w-full text-left px-2 py-1 text-sm hover:bg-white/10 rounded flex items-center gap-2"
                      >
                        <Clock className="w-3 h-3 text-gray-400" />
                        {historyQuery}
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </form>

          {/* Filters */}
          {showFilters && (
            <div className="flex items-center gap-2 flex-wrap">
              <LuminarSelect
                value={activeFilters.type}
                onChange={(e) => setActiveFilters(prev => ({ ...prev, type: e.target.value as any }))}
                options={[
                  { value: 'all', label: 'All types' },
                  { value: 'web', label: 'Web' },
                  { value: 'academic', label: 'Academic' },
                  { value: 'database', label: 'Database' },
                  { value: 'pdf', label: 'PDF' },
                  { value: 'doc', label: 'Document' }
                ]}
              />

              <LuminarSelect
                value={activeFilters.dateRange}
                onChange={(e) => setActiveFilters(prev => ({ ...prev, dateRange: e.target.value as any }))}
                options={[
                  { value: 'all', label: 'All dates' },
                  { value: 'recent', label: 'Past week' },
                  { value: 'year', label: 'Past year' }
                ]}
              />

              <LuminarSelect
                value={activeFilters.sortBy}
                onChange={(e) => setActiveFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}
                options={[
                  { value: 'relevance', label: 'Relevance' },
                  { value: 'date', label: 'Date' },
                  { value: 'title', label: 'Title' }
                ]}
              />

              {(activeFilters.type !== 'all' || activeFilters.dateRange !== 'all') && (
                <LuminarButton
                  size="sm"
                  variant="ghost"
                  onClick={() => setActiveFilters({ type: 'all', dateRange: 'all', sortBy: 'relevance' })}
                  className="text-xs"
                >
                  Clear filters
                </LuminarButton>
              )}
            </div>
          )}
        </div>

        {/* Results */}
        <div className="flex-1 overflow-y-auto p-4">
          <AnimatePresence mode="wait">
            {isSearching ? (
              <motion.div
                key="loading"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="flex flex-col items-center justify-center py-12"
              >
                <LoadingSpinner size="lg" />
                <LuminarText 
                  text={`Searching for "${query}"...`}
                  className="mt-4 text-gray-400"
                />
              </motion.div>
            ) : filteredResults.length > 0 ? (
              <motion.div
                key="results"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="space-y-4"
              >
                {/* Results header */}
                <div className="flex items-center justify-between">
                  <LuminarText 
                    text={`${filteredResults.length} result${filteredResults.length !== 1 ? 's' : ''}${query ? ` for "${query}"` : ''}`}
                    className="text-sm text-gray-400"
                  />
                  
                  {results.length > maxResults && (
                    <LuminarText 
                      text={`Showing first ${maxResults} results`}
                      className="text-xs text-gray-500"
                    />
                  )}
                </div>

                {/* Results list */}
                <div className="space-y-3">
                  {filteredResults.map((source, index) => (
                    <SourceResult key={source.id} source={source} index={index} />
                  ))}
                </div>
              </motion.div>
            ) : query ? (
              <motion.div
                key="no-results"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="flex flex-col items-center justify-center py-12 text-center"
              >
                <Search className="w-12 h-12 text-gray-400 mb-4" />
                <LuminarText 
                  text="No results found"
                  className="text-lg font-medium text-gray-300 mb-2"
                />
                <LuminarText 
                  text="Try adjusting your search terms or filters to find what you're looking for."
                  className="text-sm text-gray-500 max-w-md"
                />
                <LuminarButton
                  size="sm"
                  variant="outline"
                  onClick={onClearSearch}
                  className="mt-4"
                >
                  Clear search
                </LuminarButton>
              </motion.div>
            ) : (
              <motion.div
                key="empty"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="flex flex-col items-center justify-center py-12 text-center"
              >
                <Search className="w-12 h-12 text-gray-400 mb-4" />
                <LuminarText 
                  text="Start searching for sources"
                  className="text-lg font-medium text-gray-300 mb-2"
                />
                <LuminarText 
                  text="Enter your research topic or keywords to find relevant sources and information."
                  className="text-sm text-gray-500 max-w-md"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    );
  }
);
ResearchSearch.displayName = "ResearchSearch";

export { ResearchSearch };