import React, { useRef, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePredictiveUI, type PredictionResult } from '../../lib/predictive-ui-engine';
import { cn } from '../../../lib/utils';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

interface PredictiveElementProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
  onPrediction?: (prediction: PredictionResult) => void;
  enabled?: boolean;
}

const PredictiveElement: React.FC<PredictiveElementProps> = ({
  children,
  className = '',
  id,
  onPrediction,
  enabled = true
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const { prediction, isPreAnimating, setTaskContext } = usePredictiveUI(elementRef, enabled);
  const [hovered, setIsHovered] = useState(false);
  const [isClicked, setIsClicked] = useState(false);

  useEffect(() => {
    if (prediction && onPrediction) {
      onPrediction(prediction);
    }
  }, [prediction, onPrediction]);

  const getPreAnimationStyles = () => {
    if (!prediction || !isPreAnimating) return {};

    switch (prediction.animationType) {
      case 'hover':
        return {
          scale: 1.02,
          y: -2,
          boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'
        };
      case 'focus':
        return {
          scale: 1.01,
          boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
          borderColor: 'rgba(59, 130, 246, 0.3)'
        };
      case 'expand':
        return {
          scale: 1.05,
          y: -4
        };
      case 'highlight':
        return {
          backgroundColor: 'rgba(59, 130, 246, 0.05)',
          borderColor: 'rgba(59, 130, 246, 0.2)'
        };
      case 'prepare':
        return {
          opacity: 0.9,
          scale: 0.98
        };
      default:
        return {};
    }
  };

  return (
    <motion.div
      ref={elementRef}
      id={id}
      className={cn(
        'relative transition-all duration-300 cursor-pointer',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{
        opacity: 1,
        y: 0,
        ...getPreAnimationStyles()
      }}
      whileHover={{
        scale: 1.02,
        y: -2,
        transition: { duration: 0.2 }
      }}
      whileTap={{
        scale: 0.98,
        transition: { duration: 0.1 }
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => {
        setIsClicked(true);
        setTimeout(() => setIsClicked(false), 200);
      }}
    >
      {children}
      
      {/* Prediction indicator */}
      <AnimatePresence>
        {isPreAnimating && (
          <motion.div
            className="absolute -top-2 -right-2 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Confidence indicator */}
      {prediction && isPreAnimating && (
        <div className="absolute top-0 left-0 w-full h-1 bg-gray-200 rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-blue-500 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${prediction.intent.confidence * 100}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      )}
    </motion.div>
  );
};

interface PredictiveUIDemoProps {
  className?: string;
}

export const PredictiveUIDemo: React.FC<PredictiveUIDemoProps> = ({ className = '' }) => {
  const [predictions, setPredictions] = useState<(PredictionResult & { elementId: string })[]>([]);
  const [totalPredictions, setTotalPredictions] = useState(0);
  const [accuratePredictions, setAccuratePredictions] = useState(0);
  const [currentTask, setCurrentTask] = useState('exploring');

  const handlePrediction = (elementId: string) => (prediction: PredictionResult) => {
    setPredictions(prev => {
      const newPredictions = [...prev, { ...prediction, elementId }].slice(-10); // Keep last 10
      setTotalPredictions(prev => prev + 1);
      return newPredictions;
    });
  };

  const handleElementClick = (elementId: string) => {
    // Simulate accuracy tracking (in real implementation, this would be more sophisticated)
    const recentPrediction = predictions.find(p => p.elementId === elementId);
    if (recentPrediction && recentPrediction.intent.confidence > 0.7) {
      setAccuratePredictions(prev => prev + 1);
    }
  };

  const taskContexts = [
    { id: 'exploring', label: '🔍 Exploring', description: 'Browsing and discovering content' },
    { id: 'shopping', label: '🛒 Shopping', description: 'Looking for products to purchase' },
    { id: 'reading', label: '📖 Reading', description: 'Focused on consuming content' },
    { id: 'creating', label: '✏️ Creating', description: 'Writing or designing content' },
    { id: 'gaming', label: '🎮 Gaming', description: 'Playing interactive content' }
  ];

  const sampleElements = [
    { id: 'nav-home', label: 'Home', type: 'navigation', icon: '🏠' },
    { id: 'nav-products', label: 'Products', type: 'navigation', icon: '📦' },
    { id: 'nav-about', label: 'About', type: 'navigation', icon: 'ℹ️' },
    { id: 'cta-primary', label: 'Get Started', type: 'action', icon: '🚀' },
    { id: 'cta-secondary', label: 'Learn More', type: 'action', icon: '📚' },
    { id: 'card-1', label: 'Featured Product', type: 'content', icon: '⭐' },
    { id: 'card-2', label: 'Latest News', type: 'content', icon: '📰' },
    { id: 'card-3', label: 'User Reviews', type: 'content', icon: '💬' },
    { id: 'input-search', label: 'Search...', type: 'input', icon: '🔍' },
    { id: 'button-subscribe', label: 'Subscribe', type: 'action', icon: '✉️' }
  ];

  const accuracy = totalPredictions > 0 ? (accuratePredictions / totalPredictions * 100) : 0;

  return (
    <div className={cn('space-y-8 p-6', className)}>
      {/* Header */}
      <div className="text-center space-y-4">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
          🧠 AI-Powered Predictive UI
        </h2>
        <p className="text-muted-foreground max-w-3xl mx-auto">
          Watch as the AI engine analyzes your mouse movements, predicts your intentions, 
          and pre-animates interface elements before you even interact with them.
        </p>
      </div>

      {/* Analytics Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{totalPredictions}</div>
          <div className="text-sm text-muted-foreground">Total Predictions</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{accuracy.toFixed(1)}%</div>
          <div className="text-sm text-muted-foreground">Accuracy Rate</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">{predictions.length}</div>
          <div className="text-sm text-muted-foreground">Recent Predictions</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">{currentTask}</div>
          <div className="text-sm text-muted-foreground">Current Context</div>
        </div>
      </div>

      {/* Task Context Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">🎯 Current Task Context</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
          {taskContexts.map((task) => (
            <button
              key={task.id}
              onClick={() => setCurrentTask(task.id)}
              className={cn(
                'p-3 rounded-lg border-2 transition-all duration-300 text-left',
                currentTask === task.id
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300 bg-white text-gray-700'
              )}
            >
              <div className="font-medium">{task.label}</div>
              <div className="text-xs text-muted-foreground">{task.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Interactive Elements Grid */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">🎮 Interactive Elements</h3>
        <p className="text-sm text-muted-foreground">
          Move your mouse around these elements. The AI will predict your intentions and pre-animate elements 
          with blue dots indicating high-confidence predictions.
        </p>
        
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {sampleElements.map((element) => (
            <PredictiveElement
              key={element.id}
              id={element.id}
              className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all"
              onPrediction={handlePrediction(element.id)}
              enabled={true}
            >
              <div 
                className="text-center space-y-2"
                onClick={() => handleElementClick(element.id)}
              >
                <div className="text-2xl">{element.icon}</div>
                <div className="text-sm font-medium">{element.label}</div>
                <div className="text-xs text-muted-foreground capitalize">{element.type}</div>
              </div>
            </PredictiveElement>
          ))}
        </div>
      </div>

      {/* Navigation Bar Example */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">🧭 Navigation Example</h3>
        <div className="flex space-x-1 p-2 bg-gray-100 rounded-lg">
          {['Home', 'Products', 'Services', 'About', 'Contact'].map((item, index) => (
            <PredictiveElement
              key={`nav-${index}`}
              id={`nav-${item.toLowerCase()}`}
              className="px-4 py-2 rounded-md text-sm font-medium transition-all"
              onPrediction={handlePrediction(`nav-${item.toLowerCase()}`)}
            >
              <div onClick={() => handleElementClick(`nav-${item.toLowerCase()}`)}>
                {item}
              </div>
            </PredictiveElement>
          ))}
        </div>
      </div>

      {/* Button Group Example */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">🔘 Action Buttons</h3>
        <div className="flex flex-wrap gap-3">
          {[
            { label: 'Primary Action', variant: 'primary', icon: '🚀' },
            { label: 'Secondary Action', variant: 'secondary', icon: '📊' },
            { label: 'Danger Action', variant: 'danger', icon: '⚠️' },
            { label: 'Success Action', variant: 'success', icon: '✅' }
          ].map((button, index) => (
            <PredictiveElement
              key={`btn-${index}`}
              id={`btn-${button.variant}`}
              className={cn(
                'px-6 py-3 rounded-lg font-medium transition-all text-white',
                button.variant === 'primary' && 'bg-blue-600 hover:bg-blue-700',
                button.variant === 'secondary' && 'bg-gray-600 hover:bg-gray-700',
                button.variant === 'danger' && 'bg-red-600 hover:bg-red-700',
                button.variant === 'success' && 'bg-green-600 hover:bg-green-700'
              )}
              onPrediction={handlePrediction(`btn-${button.variant}`)}
            >
              <div 
                className="flex items-center space-x-2"
                onClick={() => handleElementClick(`btn-${button.variant}`)}
              >
                <span>{button.icon}</span>
                <span>{button.label}</span>
              </div>
            </PredictiveElement>
          ))}
        </div>
      </div>

      {/* Recent Predictions Log */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">📊 Prediction Log</h3>
        <div className="max-h-64 overflow-y-auto space-y-2">
          <AnimatePresence>
            {predictions.slice(-5).reverse().map((prediction, index) => (
              <motion.div
                key={`${prediction.elementId}-${prediction.intent.timestamp}`}
                className="p-3 bg-gray-50 rounded-lg border"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <div className="font-medium text-sm">
                      Element: <code className="text-blue-600">{prediction.elementId}</code>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Intent: {prediction.intent.type} • 
                      Confidence: {(prediction.intent.confidence * 100).toFixed(1)}% • 
                      Animation: {prediction.animationType}
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(prediction.intent.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {predictions.length === 0 && (
            <div className="text-center text-muted-foreground py-8">
              Start moving your mouse around to see predictions appear here
            </div>
          )}
        </div>
      </div>

      {/* How It Works */}
      <div className="space-y-4 p-6 bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl">
        <h3 className="text-lg font-semibold">🔬 How It Works</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="space-y-2">
            <div className="font-medium text-purple-600">1. Behavior Analysis</div>
            <div className="text-muted-foreground">
              Tracks mouse velocity, trajectory, and interaction patterns to understand user intent
            </div>
          </div>
          <div className="space-y-2">
            <div className="font-medium text-blue-600">2. Intent Prediction</div>
            <div className="text-muted-foreground">
              Uses machine learning to predict which element you're likely to interact with next
            </div>
          </div>
          <div className="space-y-2">
            <div className="font-medium text-green-600">3. Pre-Animation</div>
            <div className="text-muted-foreground">
              Starts animations 200-500ms before predicted interaction for smoother experience
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};