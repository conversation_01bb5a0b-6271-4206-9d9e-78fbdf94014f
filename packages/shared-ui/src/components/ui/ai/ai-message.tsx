import { motion, AnimatePresence } from "framer-motion";
import { forwardRef, useEffect, useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON>r, <PERSON><PERSON>, <PERSON>hum<PERSON>Up, <PERSON>otateCcw, <PERSON><PERSON>, Clock, Cpu } from "lucide-react";
import { cn } from '../../../lib/utils';
import { LuminarButton } from "../actions/button-advanced";
import { LuminarText } from "../display/text";
import { LuminarTooltip } from "../feedback/tooltip";
import { useTheme } from "../../../providers/theme-provider";
import { getGlassClasses } from '../../../design-system';
import { Message } from "ai";
import type { AIMessage } from '../../../types/ai';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface AIMessageProps {
  message: AIMessage;
  index: number;
  isStreaming?: boolean;
  size?: ComponentSize;
  showTimestamps?: boolean;
  showMetadata?: boolean;
  onCopy?: (content: string) => void;
  onRegenerate?: () => void;
  onRate?: (rating: 'up' | 'down') => void;
  className?: string;
}

// Advanced streaming text component with realistic typing patterns
const StreamingText = ({ 
  text, 
  isComplete,
  speed = 'normal',
  showCursor = true 
}: { 
  text: string; 
  isComplete: boolean;
  speed?: 'slow' | 'normal' | 'fast';
  showCursor?: boolean;
}) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  
  // Speed configurations
  const speeds = {
    slow: { base: 80, variation: 40 },
    normal: { base: 30, variation: 20 },
    fast: { base: 10, variation: 10 }
  };
  
  const { base, variation } = speeds[speed];
  
  useEffect(() => {
    if (isComplete) {
      setDisplayText(text);
      setIsTyping(false);
      return;
    }
    
    setIsTyping(true);
    
    if (currentIndex < text.length) {
      const char = text[currentIndex];
      
      // Simulate realistic typing patterns
      let delay = base + Math.random() * variation;
      
      // Longer pauses for punctuation
      if (['.', '!', '?', '\n'].includes(char)) {
        delay += 200;
      } else if ([',', ';', ':'].includes(char)) {
        delay += 100;
      } else if (char === ' ') {
        delay += 50;
      }
      
      const timeout = setTimeout(() => {
        setDisplayText(text.slice(0, currentIndex + 1));
        setCurrentIndex(prev => prev + 1);
      }, delay);
      
      return () => clearTimeout(timeout);
    } else {
      setIsTyping(false);
    }
  }, [text, currentIndex, isComplete, base, variation]);
  
  return (
    <span className="relative">
      {displayText}
      {showCursor && (isTyping || !isComplete) && (
        <motion.span
          animate={{ opacity: [0, 1, 0] }}
          transition={{ duration: 0.8, repeat: Infinity }}
          className="inline-block w-0.5 h-4 bg-blue-400 ml-1 align-middle"
        />
      )}
    </span>
  );
};

// Progress indicator for streaming
const StreamingProgress = ({ 
  text, 
  targetLength,
  processingTime 
}: { 
  text: string; 
  targetLength?: number;
  processingTime?: number;
}) => {
  const progress = targetLength ? (text.length / targetLength) * 100 : 0;
  
  return (
    <div className="flex items-center gap-2 text-xs text-gray-500 mt-2">
      <Zap className="w-3 h-3 text-blue-400" />
      <span>Streaming...</span>
      {targetLength && (
        <div className="flex-1 max-w-20 h-1 bg-gray-700 rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-blue-400 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${Math.min(progress, 100)}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
      )}
      {processingTime && (
        <span className="flex items-center gap-1">
          <Clock className="w-3 h-3" />
          {processingTime}ms
        </span>
      )}
    </div>
  );
};

const AIMessage = forwardRef<HTMLDivElement, AIMessageProps>(
  ({
    message,
    index,
    isStreaming = false,
    size = "md",
    showTimestamps = true,
    showMetadata = true,
    onCopy,
    onRegenerate,
    onRate,
    className,
    ...props
  }, ref) => {
    const [copiedRecently, setCopiedRecently] = useState(false);
    const [hovered, setIsHovered] = useState(false);
    const { resolvedTheme } = useTheme();
    const messageRef = useRef<HTMLDivElement>(null);
    
    const isUser = message.role === 'user';
    const isAssistant = message.role === 'assistant';
    
    const glassClasses = getGlassClasses(
      isUser ? 'button' : 'card',
      {
        intensity: isUser ? 'md' : 'light',
        depth: 'surface',
        animated: true,
        interactive: false
      }
    );

    // Handle copy functionality
    const handleCopy = async () => {
      if (onCopy) {
        onCopy(message.content);
      } else {
        try {
          await navigator.clipboard.writeText(message.content);
          setCopiedRecently(true);
          setTimeout(() => setCopiedRecently(false), 2000);
        } catch (error) {
          console.error('Failed to copy message:', error);
        }
      }
    };

    // Enhanced streaming visual effects
    const streamingGlow = isStreaming ? {
      boxShadow: [
        "0 0 0 0 rgba(59, 130, 246, 0.1)",
        "0 0 0 4px rgba(59, 130, 246, 0.2)",
        "0 0 0 8px rgba(59, 130, 246, 0.1)",
        "0 0 0 0 rgba(59, 130, 246, 0.1)"
      ]
    } : {};

    return (
      <motion.div
        ref={ref}
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ 
          type: "spring",
          stiffness: 300,
          damping: 30,
          delay: index * 0.1 
        }}
        className={cn(
          "flex gap-3 max-w-[85%] group",
          isUser ? "ml-auto flex-row-reverse" : "mr-auto",
          className
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        {...props}
      >
        {/* Avatar */}
        <motion.div 
          className={cn(
            "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",
            glassClasses,
            isUser ? "bg-blue-500/20" : "bg-gray-500/20"
          )}
          animate={isStreaming ? { 
            scale: [1, 1.1, 1],
            rotate: [0, 360]
          } : {}}
          transition={{
            duration: isStreaming ? 2 : 0,
            repeat: isStreaming ? Infinity : 0,
            ease: "easeInOut"
          }}
        >
          {isUser ? (
            <User className="w-4 h-4 text-blue-400" />
          ) : (
            <Bot className="w-4 h-4 text-gray-400" />
          )}
        </motion.div>

        {/* Message Content */}
        <div className="flex flex-col gap-2 flex-1">
          {/* Message bubble */}
          <motion.div 
            ref={messageRef}
            className={cn(
              "relative p-3 rounded-lg transition-all duration-300",
              glassClasses,
              isUser && "bg-blue-500/10",
              isStreaming && "bg-gradient-to-r from-blue-500/5 to-purple-500/5"
            )}
            animate={isStreaming ? streamingGlow : {}}
            transition={{
              duration: 1.5,
              repeat: isStreaming ? Infinity : 0,
              ease: "easeInOut"
            }}
          >
            {/* Message text */}
            <LuminarText 
              text=""
              className={cn(
                "text-sm whitespace-pre-wrap leading-relaxed",
                isUser ? "text-blue-100" : "text-gray-100"
              )}
            >
              {isStreaming ? (
                <StreamingText 
                  text={message.content} 
                  isComplete={false}
                  speed="normal"
                  showCursor={true}
                />
              ) : (
                message.content
              )}
            </LuminarText>

            {/* Streaming indicators */}
            {isStreaming && (
              <div className="mt-2">
                <StreamingProgress 
                  text={message.content}
                  processingTime={message.metadata?.processingTime}
                />
              </div>
            )}

            {/* Streaming pulse indicator */}
            {isStreaming && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="absolute -top-2 -right-2"
              >
                <motion.div
                  className="w-4 h-4 rounded-full bg-blue-500/20 flex items-center justify-center"
                  animate={{ 
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <Zap className="w-2 h-2 text-blue-400" />
                </motion.div>
              </motion.div>
            )}

            {/* Message actions */}
            <AnimatePresence>
              {(hovered || copiedRecently) && !isStreaming && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="absolute -top-2 -right-2"
                >
                  <div className="flex gap-1">
                    <LuminarTooltip content={copiedRecently ? "Copied!" : "Copy message"}>
                      <LuminarButton
                        size="xs"
                        variant="glass"
                        onClick={handleCopy}
                        className={copiedRecently ? "text-green-400" : ""}
                      >
                        <Copy className="w-3 h-3" />
                      </LuminarButton>
                    </LuminarTooltip>
                    
                    {isAssistant && (
                      <>
                        <LuminarTooltip content="Good response">
                          <LuminarButton
                            size="xs"
                            variant="glass"
                            onClick={() => onRate?.('up')}
                          >
                            <ThumbsUp className="w-3 h-3" />
                          </LuminarButton>
                        </LuminarTooltip>
                        
                        <LuminarTooltip content="Regenerate">
                          <LuminarButton
                            size="xs"
                            variant="glass"
                            onClick={onRegenerate}
                          >
                            <RotateCcw className="w-3 h-3" />
                          </LuminarButton>
                        </LuminarTooltip>
                      </>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Metadata and timestamps */}
          {(showTimestamps || showMetadata) && (
            <div className={cn(
              "text-xs text-gray-400 px-2 flex items-center gap-2 flex-wrap",
              isUser ? "justify-end" : "justify-start"
            )}>
              {showTimestamps && message.createdAt && (
                <span className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  {new Date(message.createdAt).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </span>
              )}
              
              {showMetadata && message.metadata && (
                <>
                  {message.metadata.processingTime && (
                    <span className="opacity-60">
                      ({message.metadata.processingTime}ms)
                    </span>
                  )}
                  
                  {message.metadata.tokenCount && (
                    <span className="opacity-60">
                      ~{message.metadata.tokenCount.total} tokens
                    </span>
                  )}
                  
                  {message.metadata.model && (
                    <span className="opacity-60 text-xs bg-gray-500/20 px-1 rounded flex items-center gap-1">
                      <Cpu className="w-2 h-2" />
                      {message.metadata.model}
                    </span>
                  )}
                  
                  {message.metadata.confidence && (
                    <span className="opacity-60 text-xs bg-green-500/20 px-1 rounded">
                      {Math.round(message.metadata.confidence * 100)}% confident
                    </span>
                  )}
                </>
              )}
            </div>
          )}

          {/* Sources preview */}
          {message.sources && message.sources.length > 0 && (
            <div className="flex gap-2 mt-2">
              {message.sources.slice(0, 3).map((source, idx) => (
                <motion.div
                  key={idx}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: idx * 0.1 }}
                  className="text-xs px-2 py-1 rounded bg-gray-500/20 text-gray-300 truncate max-w-32"
                >
                  {source.title}
                </motion.div>
              ))}
              {message.sources.length > 3 && (
                <div className="text-xs px-2 py-1 rounded bg-gray-500/20 text-gray-300">
                  +{message.sources.length - 3} more
                </div>
              )}
            </div>
          )}
        </div>
      </motion.div>
    );
  }
);
AIMessage.displayName = "AIMessage";

export { AIMessage, StreamingText, StreamingProgress };