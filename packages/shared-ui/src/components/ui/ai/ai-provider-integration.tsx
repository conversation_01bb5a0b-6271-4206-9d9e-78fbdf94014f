import { motion } from "framer-motion";
import { forwardRef, useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, AlertCircle, CheckCircle, XCircle } from "lucide-react";
import { cn } from '../../../lib/utils';
import { LuminarButton } from "../actions/button-advanced";
import { LuminarInput } from "../forms/input";
import { LuminarCard } from "../display/card";
import { LuminarText } from "../display/text";
import { LuminarTooltip } from "../feedback/tooltip";
import { useAI } from "../../../providers/ai-provider";
import { useTheme, getThemedGlassClasses, getThemedTextClasses, getThemedBackgroundClasses } from "../../../providers/theme-provider";
import { getGlassClasses } from '../../../design-system';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface AIProviderIntegrationProps {
  className?: string;
  size?: ComponentSize;
  showAdvancedSettings?: boolean;
  onConfigurationChange?: (config: any) => void;
}

const AIProviderIntegration = forwardRef<HTMLDivElement, AIProviderIntegrationProps>(
  ({
    className,
    size = "md",
    showAdvancedSettings = false,
    onConfigurationChange,
    ...props
  }, ref) => {
    const { config, updateConfig, isConfigured, models } = useAI();
    const { resolvedTheme } = useTheme();
    const [isExpanded, setIsExpanded] = useState(false);
    const [localConfig, setLocalConfig] = useState(config);
    const [testStatus, setTestStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle');
    
    // Sync local config with provider config
    useEffect(() => {
      setLocalConfig(config);
    }, [config]);
    
    // Get themed classes
    const textClasses = getThemedTextClasses(resolvedTheme);
    const backgroundClasses = getThemedBackgroundClasses(resolvedTheme);
    const glassClasses = getThemedGlassClasses('md', resolvedTheme);
    
    // Handle config updates
    const handleConfigUpdate = (updates: Partial<typeof config>) => {
      const newConfig = { ...localConfig, ...updates };
      setLocalConfig(newConfig);
      updateConfig(updates);
      onConfigurationChange?.(newConfig);
    };
    
    // Test API connection
    const testConnection = async () => {
      setTestStatus('testing');
      try {
        // Simulate API test
        await new Promise(resolve => setTimeout(resolve, 1000));
        setTestStatus('success');
        setTimeout(() => setTestStatus('idle'), 3000);
      } catch (error) {
        setTestStatus('error');
        setTimeout(() => setTestStatus('idle'), 3000);
      }
    };
    
    // Status indicator
    const StatusIndicator = ({ status }: { status: typeof testStatus }) => {
      const indicators = {
        idle: { icon: Bot, color: textClasses.muted, label: 'Ready' },
        testing: { icon: Zap, color: textClasses.warning, label: 'Testing...' },
        success: { icon: CheckCircle, color: textClasses.success, label: 'Connected' },
        error: { icon: XCircle, color: textClasses.error, label: 'Failed' }
      };
      
      const { icon: Icon, color, label } = indicators[status];
      
      return (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex items-center gap-2"
        >
          <motion.div
            animate={status === 'testing' ? { rotate: 360 } : {}}
            transition={{ duration: 1, repeat: status === 'testing' ? Infinity : 0 }}
          >
            <Icon className={cn("w-4 h-4", color)} />
          </motion.div>
          <span className={cn("text-sm", color)}>{label}</span>
        </motion.div>
      );
    };
    
    return (
      <div
        ref={ref}
        className={cn("w-full", className)}
        {...props}
      >
        <LuminarCard
          className={cn(
            "p-4 space-y-4",
            glassClasses,
            backgroundClasses.primary
          )}
        >
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bot className={cn("w-5 h-5", textClasses.accent)} />
              <LuminarText
                text="AI Configuration"
                className={cn("font-semibold", textClasses.primary)}
              />
            </div>
            
            <div className="flex items-center gap-2">
              <StatusIndicator status={testStatus} />
              <LuminarButton
                size="sm"
                variant="ghost"
                onClick={() => setIsExpanded(!isExpanded)}
                icon={Settings}
              />
            </div>
          </div>
          
          {/* Basic Configuration */}
          <div className="space-y-3">
            {/* API Key */}
            <div>
              <label className={cn("block text-sm font-medium mb-2", textClasses.secondary)}>
                API Key
              </label>
              <LuminarInput
                type="password"
                value={localConfig.apiKey || ''}
                onChange={(e) => handleConfigUpdate({ apiKey: e.target.value })}
                placeholder="Enter your OpenAI API key"
                variant="input"
                size={size}
                className="w-full"
              />
            </div>
            
            {/* Model Selection */}
            <div>
              <label className={cn("block text-sm font-medium mb-2", textClasses.secondary)}>
                Model
              </label>
              <select
                value={localConfig.model}
                onChange={(e) => handleConfigUpdate({ model: e.target.value })}
                className={cn(
                  "w-full p-2 rounded-lg border",
                  glassClasses,
                  backgroundClasses.secondary,
                  textClasses.primary,
                  "focus:outline-none focus:ring-2 focus:ring-blue-500"
                )}
              >
                {models.map(model => (
                  <option key={model} value={model}>
                    {model}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Test Connection Button */}
            <LuminarButton
              variant="glass"
              size={size}
              onClick={testConnection}
              disabled={!localConfig.apiKey || testStatus === 'testing'}
              loading={testStatus === 'testing'}
              className="w-full"
            >
              Test Connection
            </LuminarButton>
          </div>
          
          {/* Advanced Settings */}
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-3 pt-4 border-t border-white/10"
            >
              {/* Temperature */}
              <div>
                <label className={cn("block text-sm font-medium mb-2", textClasses.secondary)}>
                  Temperature ({localConfig.temperature})
                </label>
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={localConfig.temperature}
                  onChange={(e) => handleConfigUpdate({ temperature: parseFloat(e.target.value) })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>Conservative</span>
                  <span>Balanced</span>
                  <span>Creative</span>
                </div>
              </div>
              
              {/* Max Tokens */}
              <div>
                <label className={cn("block text-sm font-medium mb-2", textClasses.secondary)}>
                  Max Tokens
                </label>
                <LuminarInput
                  type="number"
                  value={localConfig.maxTokens}
                  onChange={(e) => handleConfigUpdate({ maxTokens: parseInt(e.target.value) })}
                  min="1"
                  max="4000"
                  variant="input"
                  size="sm"
                  className="w-full"
                />
              </div>
              
              {/* System Prompt */}
              <div>
                <label className={cn("block text-sm font-medium mb-2", textClasses.secondary)}>
                  System Prompt
                </label>
                <textarea
                  value={localConfig.systemPrompt}
                  onChange={(e) => handleConfigUpdate({ systemPrompt: e.target.value })}
                  placeholder="Enter system prompt..."
                  className={cn(
                    "w-full p-2 rounded-lg border min-h-[80px] resize-none",
                    glassClasses,
                    backgroundClasses.secondary,
                    textClasses.primary,
                    "focus:outline-none focus:ring-2 focus:ring-blue-500"
                  )}
                />
              </div>
              
              {/* Configuration Actions */}
              <div className="flex gap-2">
                <LuminarButton
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setLocalConfig({
                      ...localConfig,
                      temperature: 0.7,
                      maxTokens: 1000,
                      systemPrompt: 'You are a helpful AI assistant.'
                    });
                    updateConfig({
                      temperature: 0.7,
                      maxTokens: 1000,
                      systemPrompt: 'You are a helpful AI assistant.'
                    });
                  }}
                  className="flex-1"
                >
                  Reset to Defaults
                </LuminarButton>
                
                <LuminarButton
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const configJson = JSON.stringify(localConfig, null, 2);
                    const blob = new Blob([configJson], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'ai-config.json';
                    a.click();
                    URL.revokeObjectURL(url);
                  }}
                  className="flex-1"
                >
                  Export Config
                </LuminarButton>
              </div>
            </motion.div>
          )}
          
          {/* Configuration Status */}
          <div className={cn(
            "p-3 rounded-lg text-sm",
            isConfigured ? backgroundClasses.success : backgroundClasses.warning
          )}>
            <div className="flex items-center gap-2">
              {isConfigured ? (
                <CheckCircle className={cn("w-4 h-4", textClasses.success)} />
              ) : (
                <AlertCircle className={cn("w-4 h-4", textClasses.warning)} />
              )}
              <span className={isConfigured ? textClasses.success : textClasses.warning}>
                {isConfigured ? 'AI is configured and ready' : 'AI configuration required'}
              </span>
            </div>
          </div>
        </LuminarCard>
      </div>
    );
  }
);
AIProviderIntegration.displayName = "AIProviderIntegration";

export { AIProviderIntegration };