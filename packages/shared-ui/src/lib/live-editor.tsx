import React, { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';

export interface ComponentEdit {
  id: string;
  componentPath: string;
  property: string;
  oldValue: any;
  newValue: any;
  timestamp: number;
}

export interface VisualFeedback {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
  position?: { x: number; y: number };
}

export interface LiveEditorConfig {
  enabled: boolean;
  autoSave: boolean;
  saveDelay: number;
  showVisualFeedback: boolean;
  allowedProperties: string[];
}

export class LiveComponentEditor {
  private edits: Map<string, ComponentEdit> = new Map();
  private config: LiveEditorConfig;
  private feedbackCallbacks: Set<(feedback: VisualFeedback) => void> = new Set();
  private saveCallback?: (edits: ComponentEdit[]) => Promise<void>;
  private debouncedSave: ReturnType<typeof debounce>;

  constructor(config: Partial<LiveEditorConfig> = {}) {
    this.config = {
      enabled: true,
      autoSave: true,
      saveDelay: 1000,
      showVisualFeedback: true,
      allowedProperties: [
        'className',
        'style',
        'children',
        'variant',
        'size',
        'color',
        'disabled',
        'loading',
      ],
      ...config,
    };

    this.debouncedSave = debounce(this.saveEdits.bind(this), this.config.saveDelay);
  }

  setConfig(config: Partial<LiveEditorConfig>): void {
    this.config = { ...this.config, ...config };
    this.debouncedSave.cancel();
    this.debouncedSave = debounce(this.saveEdits.bind(this), this.config.saveDelay);
  }

  setSaveCallback(callback: (edits: ComponentEdit[]) => Promise<void>): void {
    this.saveCallback = callback;
  }

  addFeedbackCallback(callback: (feedback: VisualFeedback) => void): void {
    this.feedbackCallbacks.add(callback);
  }

  removeFeedbackCallback(callback: (feedback: VisualFeedback) => void): void {
    this.feedbackCallbacks.delete(callback);
  }

  private showFeedback(feedback: VisualFeedback): void {
    if (!this.config.showVisualFeedback) return;
    
    this.feedbackCallbacks.forEach(callback => callback(feedback));
  }

  editProperty(
    componentId: string,
    componentPath: string,
    property: string,
    newValue: any,
    oldValue?: any
  ): boolean {
    if (!this.config.enabled) {
      this.showFeedback({
        type: 'warning',
        message: 'Live editing is disabled',
      });
      return false;
    }

    if (!this.config.allowedProperties.includes(property)) {
      this.showFeedback({
        type: 'error',
        message: `Property '${property}' is not allowed for live editing`,
      });
      return false;
    }

    const edit: ComponentEdit = {
      id: `${componentId}-${property}`,
      componentPath,
      property,
      oldValue,
      newValue,
      timestamp: Date.now(),
    };

    this.edits.set(edit.id, edit);

    this.showFeedback({
      type: 'info',
      message: `Updated ${property}`,
      duration: 2000,
    });

    if (this.config.autoSave) {
      this.debouncedSave();
    }

    return true;
  }

  getEdits(): ComponentEdit[] {
    return Array.from(this.edits.values());
  }

  clearEdits(): void {
    this.edits.clear();
    this.showFeedback({
      type: 'info',
      message: 'Cleared all edits',
    });
  }

  undoEdit(editId: string): boolean {
    const edit = this.edits.get(editId);
    if (!edit) return false;

    this.edits.delete(editId);
    
    this.showFeedback({
      type: 'success',
      message: `Undid change to ${edit.property}`,
    });

    if (this.config.autoSave) {
      this.debouncedSave();
    }

    return true;
  }

  private async saveEdits(): Promise<void> {
    if (!this.saveCallback || this.edits.size === 0) return;

    try {
      await this.saveCallback(this.getEdits());
      this.showFeedback({
        type: 'success',
        message: `Saved ${this.edits.size} changes`,
      });
    } catch (error) {
      this.showFeedback({
        type: 'error',
        message: `Failed to save changes: ${error instanceof Error ? error.message : String(error)}`,
      });
    }
  }

  async forceSave(): Promise<void> {
    this.debouncedSave.cancel();
    await this.saveEdits();
  }

  exportEdits(): string {
    return JSON.stringify(this.getEdits(), null, 2);
  }

  importEdits(editsJson: string): boolean {
    try {
      const edits: ComponentEdit[] = JSON.parse(editsJson);
      
      this.edits.clear();
      edits.forEach(edit => {
        this.edits.set(edit.id, edit);
      });

      this.showFeedback({
        type: 'success',
        message: `Imported ${edits.length} edits`,
      });

      return true;
    } catch (error) {
      this.showFeedback({
        type: 'error',
        message: `Failed to import edits: ${error instanceof Error ? error.message : String(error)}`,
      });
      return false;
    }
  }
}

// React hook for live editing
export function useLiveEditor(
  componentId: string,
  componentPath: string,
  initialProps: Record<string, any> = {}
) {
  const [props, setProps] = useState(initialProps);
  const [editor] = useState(() => new LiveComponentEditor());
  const [feedback, setFeedback] = useState<VisualFeedback | null>(null);

  useEffect(() => {
    const handleFeedback = (feedback: VisualFeedback) => {
      setFeedback(feedback);
      
      const duration = feedback.duration || 3000;
      setTimeout(() => setFeedback(null), duration);
    };

    editor.addFeedbackCallback(handleFeedback);
    
    return () => {
      editor.removeFeedbackCallback(handleFeedback);
    };
  }, [editor]);

  const updateProp = useCallback((property: string, newValue: any) => {
    const oldValue = props[property];
    
    if (editor.editProperty(componentId, componentPath, property, newValue, oldValue)) {
      setProps(prev => ({ ...prev, [property]: newValue }));
    }
  }, [componentId, componentPath, props, editor]);

  const undoLastEdit = useCallback(() => {
    const edits = editor.getEdits();
    const lastEdit = edits[edits.length - 1];
    
    if (lastEdit && editor.undoEdit(lastEdit.id)) {
      setProps(prev => ({ ...prev, [lastEdit.property]: lastEdit.oldValue }));
    }
  }, [editor]);

  const resetProps = useCallback(() => {
    editor.clearEdits();
    setProps(initialProps);
  }, [editor, initialProps]);

  return {
    props,
    updateProp,
    undoLastEdit,
    resetProps,
    editor,
    feedback,
    hasChanges: editor.getEdits().length > 0,
  };
}

// Visual feedback component
export function VisualFeedback({ feedback }: { feedback: VisualFeedback | null }) {
  if (!feedback) return null;

  const typeStyles = {
    success: 'bg-green-500 text-white',
    error: 'bg-red-500 text-white',
    warning: 'bg-yellow-500 text-black',
    info: 'bg-blue-500 text-white',
  };

  const style: React.CSSProperties = feedback.position
    ? {
        position: 'fixed',
        left: feedback.position.x,
        top: feedback.position.y,
        zIndex: 9999,
      }
    : {
        position: 'fixed',
        top: 20,
        right: 20,
        zIndex: 9999,
      };

  return (
    <div
      style={style}
      className={`px-4 py-2 rounded-md shadow-lg transition-all duration-300 ${
        typeStyles[feedback.type]
      }`}
    >
      {feedback.message}
    </div>
  );
}

// Component wrapper that enables live editing
export function LiveEditableComponent({
  componentId,
  componentPath,
  children,
  initialProps = {},
  onPropsChange,
}: {
  componentId: string;
  componentPath: string;
  children: (props: any, updateProp: (prop: string, value: any) => void) => React.ReactNode;
  initialProps?: Record<string, any>;
  onPropsChange?: (props: Record<string, any>) => void;
}) {
  const { props, updateProp, feedback } = useLiveEditor(
    componentId,
    componentPath,
    initialProps
  );

  useEffect(() => {
    onPropsChange?.(props);
  }, [props, onPropsChange]);

  return (
    <div style={{ position: 'relative' }}>
      {children(props, updateProp)}
      <VisualFeedback feedback={feedback} />
    </div>
  );
}

export const liveComponentEditor = new LiveComponentEditor();