import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes, useState, useEffect } from "react"
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react"
import {
  type GlassVariant, 
  type GlassIntensity,
  type ColorTheme,
  getGlassClasses, 
  animationPresets, 
  transitions,
  type ComponentSize,
  componentSizes
} from '../../../design-system'
import { getThemedGlassClasses } from '../../../providers/theme-provider'
import { microInteractions } from '../../../lib/micro-interactions'
import { performanceMonitor } from '../../../lib/performance-monitor'
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface LuminarCalendarProps {
  // Core functionality
  mode?: "single" | "multiple" | "range"
  selected?: Date | Date[] | { from: Date; to?: Date }
  onSelect?: (date: Date | Date[] | { from: Date; to?: Date } | undefined) => void
  
  // Basic HTML attributes
  className?: string
  id?: string
  
  // Date constraints
  fromDate?: Date
  toDate?: Date
  disabled?: (date: Date) => boolean | Date[]
  
  // Display options
  defaultMonth?: Date
  numberOfMonths?: number
  showWeekNumbers?: boolean
  showOutsideDays?: boolean
  
  // Luminar enhancements
  variant?: GlassVariant
  size?: ComponentSize
  glassIntensity?: GlassIntensity
  colorTheme?: ColorTheme
  animation?: keyof typeof animationPresets
  disableAnimation?: boolean
  
  // Interactive features
  interactive?: boolean
  hoverable?: boolean
  
  // Performance
  enablePerformanceMonitoring?: boolean
  
  // Customization
  weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6
  locale?: string
  formatters?: {
    formatDay?: (date: Date) => string
    formatWeekdayName?: (date: Date) => string
    formatMonthCaption?: (date: Date) => string
  }
  
  // Events
  onMonthChange?: (month: Date) => void
  onYearChange?: (year: number) => void
  onDayClick?: (date: Date, selectedDates: Date | Date[] | { from: Date; to?: Date } | undefined) => void
  onDayMouseEnter?: (date: Date) => void
  onDayMouseLeave?: (date: Date) => void
}

// Utility functions
const formatDate = (date: Date, format: string = 'MM/DD/YYYY'): string => {
  const pad = (n: number) => n.toString().padStart(2, '0')
  
  const day = pad(date.getDate())
  const month = pad(date.getMonth() + 1)
  const year = date.getFullYear()
  
  return format
    .replace('DD', day)
    .replace('MM', month)
    .replace('YYYY', year.toString())
    .replace('YY', year.toString().slice(-2))
}

const getDaysInMonth = (date: Date): number => {
  return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
}

const getFirstDayOfMonth = (date: Date): number => {
  return new Date(date.getFullYear(), date.getMonth(), 1).getDay()
}

const isSameDay = (date1: Date, date2: Date): boolean => {
  return date1.getDate() === date2.getDate() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getFullYear() === date2.getFullYear()
}

const isDateDisabled = (date: Date, disabled?: (date: Date) => boolean | Date[], fromDate?: Date, toDate?: Date): boolean => {
  if (fromDate && date < fromDate) return true
  if (toDate && date > toDate) return true
  if (typeof disabled === 'function') {
    const result = disabled(date)
    if (typeof result === 'boolean') return result
    if (Array.isArray(result)) return result.some((d: Date) => isSameDay(d, date))
  }
  if (Array.isArray(disabled)) return disabled.some((d: Date) => isSameDay(d, date))
  return false
}

const isDateSelected = (date: Date, selected?: Date | Date[] | { from: Date; to?: Date }): boolean => {
  if (!selected) return false
  
  if (selected instanceof Date) {
    return isSameDay(date, selected)
  }
  
  if (Array.isArray(selected)) {
    return selected.some(s => isSameDay(date, s))
  }
  
  // Range selection
  if (selected.from && selected.to) {
    return date >= selected.from && date <= selected.to
  }
  
  if (selected.from) {
    return isSameDay(date, selected.from)
  }
  
  return false
}

const isDateInRange = (date: Date, selected?: Date | Date[] | { from: Date; to?: Date }): boolean => {
  if (!selected || typeof selected !== 'object' || Array.isArray(selected) || selected instanceof Date) return false
  
  if (selected.from && selected.to) {
    return date > selected.from && date < selected.to
  }
  
  return false
}

const LuminarCalendar = forwardRef<HTMLDivElement, LuminarCalendarProps>(
  ({ 
    // Core functionality
    mode = "single",
    selected,
    onSelect,
    
    // Date constraints
    fromDate,
    toDate,
    disabled,
    
    // Display options
    defaultMonth = new Date(),
    numberOfMonths = 1,
    showWeekNumbers = false,
    showOutsideDays = true,
    
    // Luminar enhancements
    variant = "card",
    size = defaultComponentProps.size,
    glassIntensity = "medium",
    colorTheme = "neutral",
    animation = "fadeIn",
    disableAnimation = false,
    
    // Interactive features
    interactive = true,
    hoverable = true,
    
    // Performance
    enablePerformanceMonitoring = false,
    
    // Customization
    weekStartsOn = 0,
    locale = "en-US",
    formatters = {},
    
    // Events
    onMonthChange,
    onYearChange,
    onDayClick,
    onDayMouseEnter,
    onDayMouseLeave,
    
    className,
    ...props 
  }, ref) => {
    const [currentMonth, setCurrentMonth] = useState(defaultMonth)
    const [hoveredDate, setHoveredDate] = useState<Date | null>(null)
    
    // Generate glass classes
    const glassClasses = colorTheme !== "neutral" 
      ? getThemedGlassClasses(glassIntensity)
      : getGlassClasses(variant, {
          intensity: glassIntensity,
          depth: "elevated",
          animated: !disableAnimation,
          interactive: interactive || hoverable
        })

    const sizeClasses = componentSizes[size]
    const animationProps = disableAnimation ? {} : animationPresets[animation]

    // Month names and day names
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ]

    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

    // Adjust day names based on weekStartsOn
    const adjustedDayNames = [...dayNames.slice(weekStartsOn), ...dayNames.slice(0, weekStartsOn)]

    useEffect(() => {
      onMonthChange?.(currentMonth)
    }, [currentMonth, onMonthChange])

    const handleDateSelect = (date: Date) => {
      if (isDateDisabled(date, disabled, fromDate, toDate)) return
      
      let newSelected: Date | Date[] | { from: Date; to?: Date } | undefined
      
      if (mode === "single") {
        newSelected = date
      } else if (mode === "multiple") {
        const currentArray = Array.isArray(selected) ? selected : []
        const existingIndex = currentArray.findIndex(d => isSameDay(d, date))
        
        if (existingIndex >= 0) {
          newSelected = currentArray.filter((_, i) => i !== existingIndex)
        } else {
          newSelected = [...currentArray, date]
        }
      } else if (mode === "range") {
        const currentRange = typeof selected === 'object' && !Array.isArray(selected) && !(selected instanceof Date) ? selected : { from: new Date(), to: undefined }
        
        if (!currentRange.from || (currentRange.from && currentRange.to)) {
          newSelected = { from: date, to: undefined }
        } else {
          if (date < currentRange.from) {
            newSelected = { from: date, to: currentRange.from }
          } else {
            newSelected = { from: currentRange.from, to: date }
          }
        }
      }
      
      onSelect?.(newSelected)
      onDayClick?.(date, newSelected)
    }

    const navigateMonth = (direction: 'prev' | 'next' | 'prevYear' | 'nextYear') => {
      const newMonth = new Date(currentMonth)
      switch (direction) {
        case 'prev':
          newMonth.setMonth(newMonth.getMonth() - 1)
          break
        case 'next':
          newMonth.setMonth(newMonth.getMonth() + 1)
          break
        case 'prevYear':
          newMonth.setFullYear(newMonth.getFullYear() - 1)
          onYearChange?.(newMonth.getFullYear())
          break
        case 'nextYear':
          newMonth.setFullYear(newMonth.getFullYear() + 1)
          onYearChange?.(newMonth.getFullYear())
          break
      }
      setCurrentMonth(newMonth)
    }

    const renderCalendar = () => {
      const daysInMonth = getDaysInMonth(currentMonth)
      const firstDayOfMonth = getFirstDayOfMonth(currentMonth)
      const adjustedFirstDay = (firstDayOfMonth - weekStartsOn + 7) % 7
      const days = []

      // Previous month's days (if showOutsideDays is true)
      if (showOutsideDays) {
        const prevMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 0)
        const prevMonthDays = getDaysInMonth(prevMonth)
        
        for (let i = adjustedFirstDay - 1; i >= 0; i--) {
          const day = prevMonthDays - i
          const date = new Date(prevMonth.getFullYear(), prevMonth.getMonth(), day)
          
          days.push(
            <motion.button
              key={`prev-${day}`}
              disabled={isDateDisabled(date, disabled, fromDate, toDate)}
              onClick={() => handleDateSelect(date)}
              onMouseEnter={() => {
                setHoveredDate(date)
                onDayMouseEnter?.(date)
              }}
              onMouseLeave={() => {
                setHoveredDate(null)
                onDayMouseLeave?.(date)
              }}
              className={cn(
                "h-10 rounded-lg text-sm font-medium transition-all",
                "opacity-50 text-muted-foreground",
                sizeClasses.text,
                !isDateDisabled(date, disabled, fromDate, toDate) && "hover:bg-white/10",
                isDateSelected(date, selected) && "bg-primary text-primary-foreground",
                isDateInRange(date, selected) && "bg-primary/20"
              )}
              {...(!disableAnimation && { whileTap: microInteractions.buttonPress })}
            >
              {day}
            </motion.button>
          )
        }
      } else {
        // Empty cells for days before month starts
        for (let i = 0; i < adjustedFirstDay; i++) {
          days.push(<div key={`empty-${i}`} className="h-10" />)
        }
      }

      // Current month's days
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day)
        const disabled = isDateDisabled(date, disabled, fromDate, toDate)
        const isSelected = isDateSelected(date, selected)
        const isInRange = isDateInRange(date, selected)
        const isToday = isSameDay(date, new Date())
        const hovered = hoveredDate && isSameDay(date, hoveredDate)

        days.push(
          <motion.button
            key={day}
            disabled={disabled}
            onClick={() => handleDateSelect(date)}
            onMouseEnter={() => {
              setHoveredDate(date)
              onDayMouseEnter?.(date)
            }}
            onMouseLeave={() => {
              setHoveredDate(null)
              onDayMouseLeave?.(date)
            }}
            className={cn(
              "h-10 rounded-lg text-sm font-medium transition-all relative",
              sizeClasses.text,
              disabled && "opacity-50 cursor-not-allowed",
              !disabled && !isSelected && "hover:bg-white/10",
              isSelected && "bg-primary text-primary-foreground",
              isInRange && "bg-primary/20",
              isToday && !isSelected && "border border-primary/50",
              hovered && !isSelected && "bg-white/20"
            )}
            {...(!disableAnimation && !disabled && { whileTap: microInteractions.buttonPress })}
          >
            {formatters.formatDay?.(date) || day}
          </motion.button>
        )
      }

      // Next month's days (if showOutsideDays is true)
      if (showOutsideDays) {
        const remainingCells = 42 - days.length // 6 weeks * 7 days
        const nextMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1)
        
        for (let day = 1; day <= remainingCells; day++) {
          const date = new Date(nextMonth.getFullYear(), nextMonth.getMonth(), day)
          
          days.push(
            <motion.button
              key={`next-${day}`}
              disabled={isDateDisabled(date, disabled, fromDate, toDate)}
              onClick={() => handleDateSelect(date)}
              onMouseEnter={() => {
                setHoveredDate(date)
                onDayMouseEnter?.(date)
              }}
              onMouseLeave={() => {
                setHoveredDate(null)
                onDayMouseLeave?.(date)
              }}
              className={cn(
                "h-10 rounded-lg text-sm font-medium transition-all",
                "opacity-50 text-muted-foreground",
                sizeClasses.text,
                !isDateDisabled(date, disabled, fromDate, toDate) && "hover:bg-white/10",
                isDateSelected(date, selected) && "bg-primary text-primary-foreground",
                isDateInRange(date, selected) && "bg-primary/20"
              )}
              {...(!disableAnimation && { whileTap: microInteractions.buttonPress })}
            >
              {day}
            </motion.button>
          )
        }
      }

      return days
    }

    const calendarContent = (
      <motion.div
        ref={ref}
        className={cn(
          "p-4 rounded-xl",
          glassClasses,
          className
        )}
        {...animationProps}
        transition={transitions.default as any}
        {...props}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-1">
            <motion.button
              onClick={() => navigateMonth('prevYear')}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              aria-label="Previous year"
              {...(!disableAnimation && { whileTap: microInteractions.buttonPress })}
            >
              <ChevronsLeft className="w-4 h-4" />
            </motion.button>
            <motion.button
              onClick={() => navigateMonth('prev')}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              aria-label="Previous month"
              {...(!disableAnimation && { whileTap: microInteractions.buttonPress })}
            >
              <ChevronLeft className="w-4 h-4" />
            </motion.button>
          </div>

          <div className="text-sm font-medium">
            {formatters.formatMonthCaption?.(currentMonth) || 
             `${monthNames[currentMonth.getMonth()]} ${currentMonth.getFullYear()}`}
          </div>

          <div className="flex items-center space-x-1">
            <motion.button
              onClick={() => navigateMonth('next')}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              aria-label="Next month"
              {...(!disableAnimation && { whileTap: microInteractions.buttonPress })}
            >
              <ChevronRight className="w-4 h-4" />
            </motion.button>
            <motion.button
              onClick={() => navigateMonth('nextYear')}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              aria-label="Next year"
              {...(!disableAnimation && { whileTap: microInteractions.buttonPress })}
            >
              <ChevronsRight className="w-4 h-4" />
            </motion.button>
          </div>
        </div>

        {/* Weekday headers */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {adjustedDayNames.map(day => (
            <div key={day} className="h-8 flex items-center justify-center text-xs font-medium text-muted-foreground">
              {formatters.formatWeekdayName?.(new Date()) || day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7 gap-1">
          {renderCalendar()}
        </div>
      </motion.div>
    )

    // Wrap with performance monitoring if enabled
    return enablePerformanceMonitoring 
      ? performanceMonitor.measureRender('LuminarCalendar', () => calendarContent)
      : calendarContent
  }
)
LuminarCalendar.displayName = "LuminarCalendar"

export { LuminarCalendar }

// Simplified API for quick usage (shadcn/ui style)
export const Calendar = LuminarCalendar