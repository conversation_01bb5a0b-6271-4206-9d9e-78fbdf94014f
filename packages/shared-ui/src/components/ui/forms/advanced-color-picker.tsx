"use client";

import React, { useState, useCallback, useMemo } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON>, Co<PERSON>, Check } from "lucide-react";
import { cn } from '../../../lib/utils';
import { ColorConverter, type ColorFormat } from '../../../lib/theme-manager';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

// Tailwind color palette for quick selection
const TAILWIND_COLORS = {
  slate: ["#f8fafc", "#f1f5f9", "#e2e8f0", "#cbd5e1", "#94a3b8", "#64748b", "#475569", "#334155", "#1e293b", "#0f172a"],
  gray: ["#f9fafb", "#f3f4f6", "#e5e7eb", "#d1d5db", "#9ca3af", "#6b7280", "#4b5563", "#374151", "#1f2937", "#111827"],
  zinc: ["#fafafa", "#f4f4f5", "#e4e4e7", "#d4d4d8", "#a1a1aa", "#71717a", "#52525b", "#3f3f46", "#27272a", "#18181b"],
  red: ["#fef2f2", "#fee2e2", "#fecaca", "#fca5a5", "#f87171", "#ef4444", "#dc2626", "#b91c1c", "#991b1b", "#7f1d1d"],
  orange: ["#fff7ed", "#ffedd5", "#fed7aa", "#fdba74", "#fb923c", "#f97316", "#ea580c", "#c2410c", "#9a3412", "#7c2d12"],
  amber: ["#fffbeb", "#fef3c7", "#fde68a", "#fcd34d", "#fbbf24", "#f59e0b", "#d97706", "#b45309", "#92400e", "#78350f"],
  yellow: ["#fefce8", "#fef9c3", "#fef08a", "#fde047", "#facc15", "#eab308", "#ca8a04", "#a16207", "#854d0e", "#713f12"],
  lime: ["#f7fee7", "#ecfccb", "#d9f99d", "#bef264", "#a3e635", "#84cc16", "#65a30d", "#4d7c0f", "#365314", "#1a2e05"],
  green: ["#f0fdf4", "#dcfce7", "#bbf7d0", "#86efac", "#4ade80", "#22c55e", "#16a34a", "#15803d", "#166534", "#14532d"],
  emerald: ["#ecfdf5", "#d1fae5", "#a7f3d0", "#6ee7b7", "#34d399", "#10b981", "#059669", "#047857", "#065f46", "#064e3b"],
  teal: ["#f0fdfa", "#ccfbf1", "#99f6e4", "#5eead4", "#2dd4bf", "#14b8a6", "#0d9488", "#0f766e", "#115e59", "#134e4a"],
  cyan: ["#ecfeff", "#cffafe", "#a5f3fc", "#67e8f9", "#22d3ee", "#06b6d4", "#0891b2", "#0e7490", "#155e75", "#164e63"],
  sky: ["#f0f9ff", "#e0f2fe", "#bae6fd", "#7dd3fc", "#38bdf8", "#0ea5e9", "#0284c7", "#0369a1", "#075985", "#0c4a6e"],
  blue: ["#eff6ff", "#dbeafe", "#bfdbfe", "#93c5fd", "#60a5fa", "#3b82f6", "#2563eb", "#1d4ed8", "#1e40af", "#1e3a8a"],
  indigo: ["#eef2ff", "#e0e7ff", "#c7d2fe", "#a5b4fc", "#818cf8", "#6366f1", "#4f46e5", "#4338ca", "#3730a3", "#312e81"],
  violet: ["#f5f3ff", "#ede9fe", "#ddd6fe", "#c4b5fd", "#a78bfa", "#8b5cf6", "#7c3aed", "#6d28d9", "#5b21b6", "#4c1d95"],
  purple: ["#faf5ff", "#f3e8ff", "#e9d5ff", "#d8b4fe", "#c084fc", "#a855f7", "#9333ea", "#7e22ce", "#6b21a8", "#581c87"],
  fuchsia: ["#fdf4ff", "#fae8ff", "#f5d0fe", "#f0abfc", "#e879f9", "#d946ef", "#c026d3", "#a21caf", "#86198f", "#701a75"],
  pink: ["#fdf2f8", "#fce7f3", "#fbcfe8", "#f9a8d4", "#f472b6", "#ec4899", "#db2777", "#be185d", "#9d174d", "#831843"],
  rose: ["#fff1f2", "#ffe4e6", "#fecdd3", "#fda4af", "#fb7185", "#f43f5e", "#e11d48", "#be123c", "#9f1239", "#881337"],
};

interface AdvancedColorPickerProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  className?: string;
  showFormats?: boolean;
  showPresets?: boolean;
  presetColors?: string[];
  disabled?: boolean;
}

export const AdvancedColorPicker: React.FC<AdvancedColorPickerProps> = ({
  value,
  onChange,
  label,
  className,
  showFormats = true,
  showPresets = true,
  presetColors,
  disabled = false,
}) => {
  const [open, setIsOpen] = useState(false);
  const [activeFormat, setActiveFormat] = useState<ColorFormat>('hex');
  const [copiedFormat, setCopiedFormat] = useState<ColorFormat | null>(null);

  const colorFormats = useMemo(() => {
    if (!value) return {};
    
    return {
      hex: value,
      rgb: ColorConverter.formatColor(value, 'rgb'),
      hsl: ColorConverter.formatColor(value, 'hsl'),
      oklch: ColorConverter.formatColor(value, 'oklch'),
    };
  }, [value]);

  const handleColorChange = useCallback((newColor: string) => {
    onChange(newColor);
  }, [onChange]);

  const handleCopyFormat = useCallback(async (format: ColorFormat) => {
    const colorValue = colorFormats[format];
    if (colorValue) {
      try {
        await navigator.clipboard.writeText(colorValue);
        setCopiedFormat(format);
        setTimeout(() => setCopiedFormat(null), 2000);
      } catch (err) {
        console.error('Failed to copy color:', err);
      }
    }
  }, [colorFormats]);

  const defaultPresets = useMemo(() => {
    if (presetColors) return presetColors;
    
    // Return a curated selection of Tailwind colors
    return [
      ...TAILWIND_COLORS.slate.slice(3, 8),
      ...TAILWIND_COLORS.red.slice(3, 8),
      ...TAILWIND_COLORS.orange.slice(3, 8),
      ...TAILWIND_COLORS.yellow.slice(3, 8),
      ...TAILWIND_COLORS.green.slice(3, 8),
      ...TAILWIND_COLORS.blue.slice(3, 8),
      ...TAILWIND_COLORS.purple.slice(3, 8),
      ...TAILWIND_COLORS.pink.slice(3, 8),
    ];
  }, [presetColors]);

  const allTailwindColors = useMemo(() => {
    return Object.entries(TAILWIND_COLORS).flatMap(([name, colors]) => 
      colors.map((color, index) => ({
        name: `${name}-${(index + 1) * 100}`,
        value: color,
        category: name,
      }))
    );
  }, []);

  return (
    <div className={cn("relative", className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {label}
        </label>
      )}
      
      {/* Color Trigger */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!open)}
        disabled={disabled}
        className={cn(
          "relative flex items-center gap-3 w-full p-3 rounded-lg border transition-all",
          "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",
          "hover:border-gray-300 dark:hover:border-gray-600",
          "focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",
          disabled && "opacity-50 cursor-not-allowed"
        )}
      >
        <div
          className="w-8 h-8 rounded-md border border-gray-200 dark:border-gray-600 shadow-sm"
          style={{ backgroundColor: value }}
        />
        <div className="flex-1 text-left">
          <div className="text-sm font-medium text-gray-900 dark:text-white">
            {value.toUpperCase()}
          </div>
          {showFormats && (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {colorFormats[activeFormat]}
            </div>
          )}
        </div>
        <Palette className="w-4 h-4 text-gray-500 dark:text-gray-400" />
      </button>

      {/* Color Picker Dropdown */}
      {open && (
        <motion.div
          initial={{ opacity: 0, y: 8, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 8, scale: 0.95 }}
          className="absolute top-full left-0 mt-2 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl z-50 min-w-[320px]"
        >
          {/* Native Color Input */}
          <div className="mb-4">
            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
              Color Picker
            </label>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={value}
                onChange={(e) => handleColorChange(e.target.value)}
                className="w-12 h-12 rounded border border-gray-200 dark:border-gray-600 cursor-pointer"
              />
              <input
                type="text"
                value={value}
                onChange={(e) => handleColorChange(e.target.value)}
                className="flex-1 px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="#000000"
              />
            </div>
          </div>

          {/* Color Formats */}
          {showFormats && (
            <div className="mb-4">
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                Color Formats
              </label>
              <div className="space-y-2">
                {Object.entries(colorFormats).map(([format, colorValue]) => (
                  <div key={format} className="flex items-center gap-2">
                    <button
                      onClick={() => setActiveFormat(format as ColorFormat)}
                      className={cn(
                        "px-2 py-1 text-xs rounded border transition-colors",
                        activeFormat === format
                          ? "bg-purple-100 dark:bg-purple-900 border-purple-300 dark:border-purple-700 text-purple-700 dark:text-purple-300"
                          : "bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400"
                      )}
                    >
                      {format.toUpperCase()}
                    </button>
                    <code className="flex-1 px-2 py-1 text-xs bg-gray-50 dark:bg-gray-700 rounded font-mono text-gray-800 dark:text-gray-200">
                      {colorValue}
                    </code>
                    <button
                      onClick={() => handleCopyFormat(format as ColorFormat)}
                      className="p-1 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors"
                    >
                      {copiedFormat === format ? (
                        <Check className="w-3 h-3 text-green-500" />
                      ) : (
                        <Copy className="w-3 h-3 text-gray-500 dark:text-gray-400" />
                      )}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Preset Colors */}
          {showPresets && (
            <div className="mb-4">
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                Quick Colors
              </label>
              <div className="grid grid-cols-8 gap-1 mb-3">
                {defaultPresets.map((color, index) => (
                  <button
                    key={`preset-${index}`}
                    onClick={() => handleColorChange(color)}
                    className={cn(
                      "w-8 h-8 rounded border-2 transition-all hover:scale-110",
                      value === color 
                        ? "border-purple-500 shadow-lg" 
                        : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                    )}
                    style={{ backgroundColor: color }}
                    title={color}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Tailwind Color Palette */}
          <div>
            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
              Tailwind Colors
            </label>
            <div className="max-h-32 overflow-y-auto">
              {Object.entries(TAILWIND_COLORS).map(([colorName, shades]) => (
                <div key={colorName} className="mb-2">
                  <div className="text-xs text-gray-500 dark:text-gray-400 mb-1 capitalize">
                    {colorName}
                  </div>
                  <div className="grid grid-cols-10 gap-1">
                    {shades.map((shade, index) => (
                      <button
                        key={`${colorName}-${index}`}
                        onClick={() => handleColorChange(shade)}
                        className={cn(
                          "w-6 h-6 rounded border transition-all hover:scale-110",
                          value === shade
                            ? "border-purple-500 shadow-lg border-2"
                            : "border-gray-200 dark:border-gray-600"
                        )}
                        style={{ backgroundColor: shade }}
                        title={`${colorName}-${(index + 1) * 100}: ${shade}`}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Close Button */}
          <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={() => setIsOpen(false)}
              className="w-full px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors"
            >
              Done
            </button>
          </div>
        </motion.div>
      )}

      {/* Backdrop */}
      {open && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};