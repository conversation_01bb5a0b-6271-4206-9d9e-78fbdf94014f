import React from 'react';
import { cn } from '../../../lib/utils';
import { type VariantProps, cva } from 'class-variance-authority';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

const testButtonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
  {
    variants: {
      variant: {
        default: 'bg-luminar-primary text-luminar-primary-foreground hover:bg-luminar-primary/90',
        secondary: 'bg-luminar-secondary text-luminar-secondary-foreground hover:bg-luminar-secondary/80',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-9 px-3 rounded-md',
        lg: 'h-11 px-8 rounded-md',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface TestButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof testButtonVariants> {}

const TestButton = React.forwardRef<HTMLButtonElement, TestButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <button
        className={cn(testButtonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
TestButton.displayName = 'TestButton';

export { TestButton };