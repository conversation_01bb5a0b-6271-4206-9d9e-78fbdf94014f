import { motion, AnimatePresence } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes, useState, useCallback, useRef, useEffect, memo, useMemo } from "react"
import {
  Edit2,
  Save,
  X,
  Plus,
  Trash2,
  Copy,
  Check,
  ChevronUp,
  ChevronDown,
  Filter,
  Download
} from "lucide-react"
import { Button } from "../actions/button"
import { LuminarInput } from "../forms/input"
import { LuminarSelect } from "../forms/select"
import { LuminarCheckbox } from "../forms/checkbox"

export type CellType = 'text' | 'number' | 'select' | 'boolean' | 'date' | 'custom'

export interface GridColumn<T = any> {
  key: string
  header: string
  type?: CellType
  width?: number | string
  minWidth?: number
  maxWidth?: number
  editable?: boolean
  sortable?: boolean
  resizable?: boolean
  frozen?: boolean
  validator?: (value: any, row: T) => boolean | string
  formatter?: (value: any, row: T) => React.ReactNode
  editor?: (props: CellEditorProps<T>) => React.ReactNode
  selectOptions?: Array<{ value: string; label: string }>
  align?: 'left' | 'center' | 'right'
}

export interface CellEditorProps<T = any> {
  value: any
  row: T
  column: GridColumn<T>
  onChange: (value: any) => void
  onCancel: () => void
  onSave: () => void
}

export interface LuminarDataGridProps<T = any> extends HTMLAttributes<HTMLDivElement> {
  data: T[]
  columns: GridColumn<T>[]
  onCellEdit?: (rowIndex: number, columnKey: string, value: any) => void
  onRowAdd?: (row: T) => void
  onRowDelete?: (rowIndex: number) => void
  onRowsChange?: (rows: T[]) => void
  rowKey?: keyof T | ((row: T) => string | number)
  defaultRowData?: Partial<T>
  enableAddRow?: boolean
  enableDeleteRow?: boolean
  enableCopyRow?: boolean
  glass?: boolean
  animated?: boolean
  striped?: boolean
  hoverable?: boolean
  height?: number | string
  virtualized?: boolean
  loading?: boolean
  emptyMessage?: string
}

interface EditingCell {
  rowIndex: number
  columnKey: string
  value: any
}

const CellEditor = memo<CellEditorProps>(({ 
  value, 
  column, 
  onChange, 
  onSave, 
  onCancel 
}) => {
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    inputRef.current?.focus()
    inputRef.current?.select()
  }, [])

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      onSave()
    } else if (e.key === 'Escape') {
      onCancel()
    }
  }

  if (column.editor) {
    return <>{column.editor({ value, row: {}, column, onChange, onCancel, onSave })}</>
  }

  switch (column.type) {
    case 'number':
      return (
        <LuminarInput
          ref={inputRef}
          type="number"
          value={value}
          onChange={(value) => onChange(parseFloat(value) || 0)}
          onKeyDown={handleKeyDown}
          size="sm"
          className="w-full"
        />
      )
    
    case 'select':
      return (
        <LuminarSelect
          value={value}
          onChange={(e) => onChange((e.target as HTMLSelectElement).value)}
          options={column.selectOptions || []}
          className="w-full"
        />
      )
    
    case 'boolean':
      return (
        <LuminarCheckbox
          checked={value}
          onChange={onChange}
        />
      )
    
    case 'date':
      return (
        <input
          ref={inputRef}
          type="date"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          className="w-full px-2 py-1 text-sm border border-white/20 rounded bg-white/5 text-white"
        />
      )
    
    default:
      return (
        <LuminarInput
          ref={inputRef}
          value={value}
          onChange={(value) => onChange(value)}
          onKeyDown={handleKeyDown}
          size="sm"
          className="w-full"
        />
      )
  }
})
CellEditor.displayName = "CellEditor"

function LuminarDataGridInner<T extends Record<string, any>>(
  { 
    data,
    columns,
    onCellEdit,
    onRowAdd,
    onRowDelete,
    onRowsChange,
    rowKey,
    defaultRowData = {},
    enableAddRow = true,
    enableDeleteRow = true,
    enableCopyRow = true,
    glass = true,
    animated = true,
    striped = false,
    hoverable = true,
    height = 'auto',
    virtualized = false,
    loading = false,
    emptyMessage = "No data available",
    className,
    ...props 
  }: LuminarDataGridProps<T>,
  ref: React.Ref<HTMLDivElement>
) {
  const [editingCell, setEditingCell] = useState<EditingCell | null>(null)
  const [newRow, setNewRow] = useState<Partial<T> | null>(null)
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null)
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({})
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set())
  
  const gridRef = useRef<HTMLDivElement>(null)
  const resizeRef = useRef<{ columnKey: string; startX: number; startWidth: number } | null>(null)

  const getRowKey = useCallback((row: T, index: number): string | number => {
    if (typeof rowKey === 'function') {
      return rowKey(row)
    } else if (rowKey) {
      return row[rowKey] as string | number
    }
    return index
  }, [rowKey])

  const handleCellDoubleClick = useCallback((rowIndex: number, columnKey: string) => {
    const column = columns.find(col => col.key === columnKey)
    if (!column?.editable) return

    const value = data[rowIndex][columnKey]
    setEditingCell({ rowIndex, columnKey, value })
  }, [data, columns])

  const handleCellEdit = useCallback((value: any) => {
    if (!editingCell) return
    setEditingCell({ ...editingCell, value })
  }, [editingCell])

  const handleSaveEdit = useCallback(() => {
    if (!editingCell) return

    const column = columns.find(col => col.key === editingCell.columnKey)
    if (column?.validator) {
      const validationResult = column.validator(editingCell.value, data[editingCell.rowIndex])
      if (validationResult !== true) {
        // Show validation error
        return
      }
    }

    onCellEdit?.(editingCell.rowIndex, editingCell.columnKey, editingCell.value)
    
    if (onRowsChange) {
      const newData = [...data]
      newData[editingCell.rowIndex] = {
        ...newData[editingCell.rowIndex],
        [editingCell.columnKey]: editingCell.value
      }
      onRowsChange(newData)
    }

    setEditingCell(null)
  }, [editingCell, data, columns, onCellEdit, onRowsChange])

  const handleCancelEdit = useCallback(() => {
    setEditingCell(null)
  }, [])

  const handleAddRow = useCallback(() => {
    setNewRow({ ...defaultRowData } as Partial<T>)
  }, [defaultRowData])

  const handleSaveNewRow = useCallback(() => {
    if (!newRow) return
    
    onRowAdd?.(newRow as T)
    
    if (onRowsChange) {
      onRowsChange([...data, newRow as T])
    }
    
    setNewRow(null)
  }, [newRow, data, onRowAdd, onRowsChange])

  const handleDeleteRow = useCallback((rowIndex: number) => {
    onRowDelete?.(rowIndex)
    
    if (onRowsChange) {
      const newData = data.filter((_, index) => index !== rowIndex)
      onRowsChange(newData)
    }
  }, [data, onRowDelete, onRowsChange])

  const handleCopyRow = useCallback((rowIndex: number) => {
    const rowToCopy = { ...data[rowIndex] }
    
    if (onRowAdd) {
      onRowAdd(rowToCopy)
    }
    
    if (onRowsChange) {
      onRowsChange([...data, rowToCopy])
    }
  }, [data, onRowAdd, onRowsChange])

  const handleSort = useCallback((columnKey: string) => {
    const column = columns.find(col => col.key === columnKey)
    if (!column?.sortable) return

    setSortConfig(prev => {
      if (prev?.key === columnKey) {
        return prev.direction === 'asc' 
          ? { key: columnKey, direction: 'desc' }
          : null
      }
      return { key: columnKey, direction: 'asc' }
    })
  }, [columns])

  // Sort data
  const sortedData = useMemo(() => {
    if (!sortConfig) return data

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key]
      const bValue = b[sortConfig.key]

      if (aValue === null || aValue === undefined) return 1
      if (bValue === null || bValue === undefined) return -1

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }, [data, sortConfig])

  // Handle column resize
  const handleResizeStart = useCallback((e: React.MouseEvent, columnKey: string) => {
    e.preventDefault()
    const column = columns.find(col => col.key === columnKey)
    if (!column?.resizable) return

    const startX = e.clientX
    const startWidth = columnWidths[columnKey] || 150

    resizeRef.current = { columnKey, startX, startWidth }

    const handleMouseMove = (e: MouseEvent) => {
      if (!resizeRef.current) return
      
      const diff = e.clientX - resizeRef.current.startX
      const newWidth = Math.max(50, resizeRef.current.startWidth + diff)
      
      setColumnWidths(prev => ({
        ...prev,
        [resizeRef.current!.columnKey]: newWidth
      }))
    }

    const handleMouseUp = () => {
      resizeRef.current = null
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [columns, columnWidths])

  const renderCell = (row: T, rowIndex: number, column: GridColumn<T>) => {
    const isEditing = editingCell?.rowIndex === rowIndex && editingCell?.columnKey === column.key
    const value = row[column.key]

    if (isEditing) {
      return (
        <CellEditor
          value={editingCell.value}
          row={row}
          column={column}
          onChange={handleCellEdit}
          onSave={handleSaveEdit}
          onCancel={handleCancelEdit}
        />
      )
    }

    if (column.formatter) {
      return column.formatter(value, row)
    }

    if (column.type === 'boolean') {
      return <Check className={cn("w-4 h-4", value ? "text-green-400" : "text-gray-600")} />
    }

    return <span>{value?.toString() || ''}</span>
  }

  return (
    <div
      ref={ref}
      className={cn(
        "relative rounded-xl overflow-hidden",
        glass && "backdrop-blur-md bg-white/5 border border-white/20",
        className
      )}
      style={{ height }}
      {...props}
    >
      {/* Toolbar */}
      {(enableAddRow || enableDeleteRow) && (
        <div className="flex items-center justify-between p-3 border-b border-white/10">
          <div className="flex items-center gap-2">
            {enableAddRow && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAddRow}
                disabled={newRow !== null}
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Row
              </Button>
            )}
            
            {enableDeleteRow && selectedRows.size > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  selectedRows.forEach(index => handleDeleteRow(index))
                  setSelectedRows(new Set())
                }}
              >
                <Trash2 className="w-4 h-4 mr-1" />
                Delete ({selectedRows.size})
              </Button>
            )}
          </div>

          <Button
            variant="ghost"
            size="sm"
          >
            <Download className="w-4 h-4" />
          </Button>
        </div>
      )}

      {/* Grid */}
      <div className="overflow-auto" style={{ maxHeight: height === 'auto' ? undefined : height }}>
        <table className="w-full">
          <thead className={cn(
            "border-b border-white/10",
            glass && "backdrop-blur-sm bg-white/5"
          )}>
            <tr>
              {(enableDeleteRow || enableCopyRow) && (
                <th className="p-3 w-12">
                  <LuminarCheckbox
                    checked={selectedRows.size === sortedData.length && sortedData.length > 0}
                    onChange={(checked) => {
                      if (checked) {
                        setSelectedRows(new Set(sortedData.map((_, i) => i)))
                      } else {
                        setSelectedRows(new Set())
                      }
                    }}
                  />
                </th>
              )}
              
              {columns.map(column => (
                <th
                  key={column.key}
                  className={cn(
                    "p-3 text-left font-medium relative",
                    column.sortable && "cursor-pointer hover:bg-white/5",
                    column.align === 'center' && "text-center",
                    column.align === 'right' && "text-right"
                  )}
                  style={{ 
                    width: columnWidths[column.key] || column.width,
                    minWidth: column.minWidth,
                    maxWidth: column.maxWidth
                  }}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center justify-between">
                    <span>{column.header}</span>
                    
                    {column.sortable && (
                      <div className="ml-2">
                        {sortConfig?.key === column.key && (
                          sortConfig.direction === 'asc' ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
                        )}
                      </div>
                    )}
                  </div>
                  
                  {column.resizable && (
                    <div
                      className="absolute right-0 top-0 bottom-0 w-1 cursor-col-resize hover:bg-blue-400"
                      onMouseDown={(e) => handleResizeStart(e, column.key)}
                    />
                  )}
                </th>
              ))}
              
              {(enableDeleteRow || enableCopyRow) && (
                <th className="p-3 w-24">Actions</th>
              )}
            </tr>
          </thead>

          <tbody>
            {loading ? (
              <tr>
                <td colSpan={columns.length + 2} className="p-8 text-center">
                  <div className="flex items-center justify-center">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full"
                    />
                  </div>
                </td>
              </tr>
            ) : sortedData.length === 0 && !newRow ? (
              <tr>
                <td colSpan={columns.length + 2} className="p-8 text-center text-gray-500">
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              <>
                {/* New row */}
                {newRow && (
                  <motion.tr
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-blue-500/10 border-b border-white/10"
                  >
                    {(enableDeleteRow || enableCopyRow) && <td />}
                    
                    {columns.map(column => (
                      <td key={column.key} className="p-2">
                        <CellEditor
                          value={newRow[column.key] || ''}
                          row={newRow as T}
                          column={column}
                          onChange={(value) => setNewRow({ ...newRow, [column.key]: value })}
                          onSave={handleSaveNewRow}
                          onCancel={() => setNewRow(null)}
                        />
                      </td>
                    ))}
                    
                    <td className="p-2">
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleSaveNewRow}
                        >
                          <Save className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setNewRow(null)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </motion.tr>
                )}

                {/* Data rows */}
                {sortedData.map((row, rowIndex) => {
                  const isSelected = selectedRows.has(rowIndex)
                  
                  return (
                    <motion.tr
                      key={getRowKey(row, rowIndex)}
                      initial={animated ? { opacity: 0 } : {}}
                      animate={animated ? { opacity: 1 } : {}}
                      transition={{ delay: rowIndex * 0.05 }}
                      className={cn(
                        "border-b border-white/5 transition-colors",
                        striped && rowIndex % 2 === 1 && "bg-white/[0.02]",
                        hoverable && "hover:bg-white/5",
                        isSelected && "bg-blue-500/10"
                      )}
                    >
                      {(enableDeleteRow || enableCopyRow) && (
                        <td className="p-3">
                          <LuminarCheckbox
                            checked={isSelected}
                            onChange={(checked) => {
                              const newSelection = new Set(selectedRows)
                              if (checked) {
                                newSelection.add(rowIndex)
                              } else {
                                newSelection.delete(rowIndex)
                              }
                              setSelectedRows(newSelection)
                            }}
                          />
                        </td>
                      )}
                      
                      {columns.map(column => (
                        <td
                          key={column.key}
                          className={cn(
                            "p-3",
                            column.align === 'center' && "text-center",
                            column.align === 'right' && "text-right",
                            column.editable && "cursor-pointer hover:bg-white/5"
                          )}
                          onDoubleClick={() => handleCellDoubleClick(rowIndex, column.key)}
                        >
                          {renderCell(row, rowIndex, column)}
                        </td>
                      ))}
                      
                      {(enableDeleteRow || enableCopyRow) && (
                        <td className="p-3">
                          <div className="flex items-center gap-1">
                            {enableCopyRow && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCopyRow(rowIndex)}
                              >
                                <Copy className="w-3 h-3" />
                              </Button>
                            )}
                            {enableDeleteRow && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteRow(rowIndex)}
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            )}
                          </div>
                        </td>
                      )}
                    </motion.tr>
                  )
                })}
              </>
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}

const LuminarDataGrid = forwardRef(LuminarDataGridInner) as <T extends Record<string, any>>(
  props: LuminarDataGridProps<T> & { ref?: React.Ref<HTMLDivElement> }
) => React.ReactElement

export { LuminarDataGrid }