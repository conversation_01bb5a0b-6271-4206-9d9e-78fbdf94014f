import { QueryClient, QueryClientConfig } from '@tanstack/react-query';
import { toast } from '../toast';

// Query client configuration
const queryClientConfig: QueryClientConfig = {
  defaultOptions: {
    queries: {
      // Stale time - how long data is considered fresh
      staleTime: 5 * 60 * 1000, // 5 minutes
      
      // Cache time - how long data stays in cache after component unmount
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      
      // Retry configuration
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx client errors
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      
      // Retry delay with exponential backoff
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      
      // Refetch on window focus for critical data
      refetchOnWindowFocus: true,
      
      // Refetch on reconnect
      refetchOnReconnect: true,
      
      // Don't refetch on mount if data is fresh
      refetchOnMount: true,
      
      // Network mode
      networkMode: 'online',
    },
    mutations: {
      // Retry failed mutations
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx client errors
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        // Retry up to 2 times for other errors
        return failureCount < 2;
      },
      
      // Global error handler for mutations
      onError: (error: any) => {
        // Don't show toast for authentication errors (handled by API client)
        if (error?.category !== 'AUTHENTICATION') {
          toast.error(error?.userMessage || 'An error occurred');
        }
      },
      
      // Network mode
      networkMode: 'online',
    },
  },
};

// Create query client instance
export const queryClient = new QueryClient(queryClientConfig);

// Query key factories for consistent key generation
export const queryKeys = {
  // Auth queries
  auth: {
    user: () => ['auth', 'user'] as const,
    permissions: () => ['auth', 'permissions'] as const,
  },
  
  // User queries
  users: {
    all: () => ['users'] as const,
    lists: () => [...queryKeys.users.all(), 'list'] as const,
    list: (filters?: any) => [...queryKeys.users.lists(), filters] as const,
    details: () => [...queryKeys.users.all(), 'detail'] as const,
    detail: (id: string) => [...queryKeys.users.details(), id] as const,
    current: () => [...queryKeys.users.all(), 'current'] as const,
    activity: (userId?: string) => [...queryKeys.users.all(), 'activity', userId] as const,
    sessions: () => [...queryKeys.users.all(), 'sessions'] as const,
    notifications: () => [...queryKeys.users.all(), 'notifications'] as const,
    stats: (filters?: any) => [...queryKeys.users.all(), 'stats', filters] as const,
    growth: (filters?: any) => [...queryKeys.users.all(), 'growth', filters] as const,
    search: (query: string, filters?: any) => [...queryKeys.users.all(), 'search', query, filters] as const,
    suggestions: (context?: any) => [...queryKeys.users.all(), 'suggestions', context] as const,
  },
  
  // Role queries
  roles: {
    all: () => ['roles'] as const,
    lists: () => [...queryKeys.roles.all(), 'list'] as const,
    list: () => [...queryKeys.roles.lists()] as const,
    details: () => [...queryKeys.roles.all(), 'detail'] as const,
    detail: (id: string) => [...queryKeys.roles.details(), id] as const,
    permissions: () => [...queryKeys.roles.all(), 'permissions'] as const,
  },
  
  // Department queries
  departments: {
    all: () => ['departments'] as const,
    lists: () => [...queryKeys.departments.all(), 'list'] as const,
    list: () => [...queryKeys.departments.lists()] as const,
    details: () => [...queryKeys.departments.all(), 'detail'] as const,
    detail: (id: string) => [...queryKeys.departments.details(), id] as const,
    members: (id: string) => [...queryKeys.departments.all(), 'members', id] as const,
  },
  
  // Training queries
  training: {
    all: () => ['training'] as const,
    courses: {
      all: () => [...queryKeys.training.all(), 'courses'] as const,
      lists: () => [...queryKeys.training.courses.all(), 'list'] as const,
      list: (filters?: any) => [...queryKeys.training.courses.lists(), filters] as const,
      details: () => [...queryKeys.training.courses.all(), 'detail'] as const,
      detail: (id: string) => [...queryKeys.training.courses.details(), id] as const,
      analytics: (id: string) => [...queryKeys.training.courses.all(), 'analytics', id] as const,
    },
    enrollments: {
      all: () => [...queryKeys.training.all(), 'enrollments'] as const,
      lists: () => [...queryKeys.training.enrollments.all(), 'list'] as const,
      list: (filters?: any) => [...queryKeys.training.enrollments.lists(), filters] as const,
      details: () => [...queryKeys.training.enrollments.all(), 'detail'] as const,
      detail: (id: string) => [...queryKeys.training.enrollments.details(), id] as const,
      progress: (id: string) => [...queryKeys.training.enrollments.all(), 'progress', id] as const,
    },
    skills: {
      all: () => [...queryKeys.training.all(), 'skills'] as const,
      lists: () => [...queryKeys.training.skills.all(), 'list'] as const,
      list: (filters?: any) => [...queryKeys.training.skills.lists(), filters] as const,
      gaps: (userId?: string) => [...queryKeys.training.skills.all(), 'gaps', userId] as const,
      ratings: (userId?: string) => [...queryKeys.training.skills.all(), 'ratings', userId] as const,
      recommendations: (userId?: string) => [...queryKeys.training.skills.all(), 'recommendations', userId] as const,
    },
    assessments: {
      all: () => [...queryKeys.training.all(), 'assessments'] as const,
      lists: () => [...queryKeys.training.assessments.all(), 'list'] as const,
      list: (filters?: any) => [...queryKeys.training.assessments.lists(), filters] as const,
      details: () => [...queryKeys.training.assessments.all(), 'detail'] as const,
      detail: (id: string) => [...queryKeys.training.assessments.details(), id] as const,
      responses: (id: string) => [...queryKeys.training.assessments.all(), 'responses', id] as const,
    },
    analytics: {
      all: () => [...queryKeys.training.all(), 'analytics'] as const,
      dashboard: (filters?: any) => [...queryKeys.training.analytics.all(), 'dashboard', filters] as const,
      metrics: (filters?: any) => [...queryKeys.training.analytics.all(), 'metrics', filters] as const,
      reports: (filters?: any) => [...queryKeys.training.analytics.all(), 'reports', filters] as const,
    },
  },
  
  // Vendor queries
  vendors: {
    all: () => ['vendors'] as const,
    lists: () => [...queryKeys.vendors.all(), 'list'] as const,
    list: (filters?: any) => [...queryKeys.vendors.lists(), filters] as const,
    details: () => [...queryKeys.vendors.all(), 'detail'] as const,
    detail: (id: string) => [...queryKeys.vendors.details(), id] as const,
    performance: (id: string) => [...queryKeys.vendors.all(), 'performance', id] as const,
    proposals: {
      all: () => [...queryKeys.vendors.all(), 'proposals'] as const,
      lists: () => [...queryKeys.vendors.proposals.all(), 'list'] as const,
      list: (filters?: any) => [...queryKeys.vendors.proposals.lists(), filters] as const,
      details: () => [...queryKeys.vendors.proposals.all(), 'detail'] as const,
      detail: (id: string) => [...queryKeys.vendors.proposals.details(), id] as const,
    },
    reviews: {
      all: () => [...queryKeys.vendors.all(), 'reviews'] as const,
      lists: () => [...queryKeys.vendors.reviews.all(), 'list'] as const,
      list: (filters?: any) => [...queryKeys.vendors.reviews.lists(), filters] as const,
      vendor: (vendorId: string) => [...queryKeys.vendors.reviews.all(), 'vendor', vendorId] as const,
    },
    analytics: {
      all: () => [...queryKeys.vendors.all(), 'analytics'] as const,
      dashboard: (filters?: any) => [...queryKeys.vendors.analytics.all(), 'dashboard', filters] as const,
      metrics: (filters?: any) => [...queryKeys.vendors.analytics.all(), 'metrics', filters] as const,
    },
  },
  
  // Wins queries
  wins: {
    all: () => ['wins'] as const,
    submissions: {
      all: () => [...queryKeys.wins.all(), 'submissions'] as const,
      lists: () => [...queryKeys.wins.submissions.all(), 'list'] as const,
      list: (filters?: any) => [...queryKeys.wins.submissions.lists(), filters] as const,
      details: () => [...queryKeys.wins.submissions.all(), 'detail'] as const,
      detail: (id: string) => [...queryKeys.wins.submissions.details(), id] as const,
      metrics: (id: string) => [...queryKeys.wins.submissions.all(), 'metrics', id] as const,
    },
    analytics: {
      all: () => [...queryKeys.wins.all(), 'analytics'] as const,
      dashboard: (filters?: any) => [...queryKeys.wins.analytics.all(), 'dashboard', filters] as const,
      metrics: (filters?: any) => [...queryKeys.wins.analytics.all(), 'metrics', filters] as const,
      team: (filters?: any) => [...queryKeys.wins.analytics.all(), 'team', filters] as const,
    },
  },
  
  // Email queries
  email: {
    all: () => ['email'] as const,
    templates: {
      all: () => [...queryKeys.email.all(), 'templates'] as const,
      lists: () => [...queryKeys.email.templates.all(), 'list'] as const,
      list: (filters?: any) => [...queryKeys.email.templates.lists(), filters] as const,
      details: () => [...queryKeys.email.templates.all(), 'detail'] as const,
      detail: (id: string) => [...queryKeys.email.templates.details(), id] as const,
      preview: (id: string, variables?: any) => [...queryKeys.email.templates.all(), 'preview', id, variables] as const,
    },
    campaigns: {
      all: () => [...queryKeys.email.all(), 'campaigns'] as const,
      lists: () => [...queryKeys.email.campaigns.all(), 'list'] as const,
      list: (filters?: any) => [...queryKeys.email.campaigns.lists(), filters] as const,
      details: () => [...queryKeys.email.campaigns.all(), 'detail'] as const,
      detail: (id: string) => [...queryKeys.email.campaigns.details(), id] as const,
      metrics: (id: string) => [...queryKeys.email.campaigns.all(), 'metrics', id] as const,
    },
    logs: {
      all: () => [...queryKeys.email.all(), 'logs'] as const,
      lists: () => [...queryKeys.email.logs.all(), 'list'] as const,
      list: (filters?: any) => [...queryKeys.email.logs.lists(), filters] as const,
      details: () => [...queryKeys.email.logs.all(), 'detail'] as const,
      detail: (id: string) => [...queryKeys.email.logs.details(), id] as const,
    },
    recipients: {
      all: () => [...queryKeys.email.all(), 'recipients'] as const,
      lists: () => [...queryKeys.email.recipients.all(), 'list'] as const,
      list: (filters?: any) => [...queryKeys.email.recipients.lists(), filters] as const,
    },
    settings: () => [...queryKeys.email.all(), 'settings'] as const,
    analytics: (filters?: any) => [...queryKeys.email.all(), 'analytics', filters] as const,
    queue: () => [...queryKeys.email.all(), 'queue'] as const,
  },
} as const;

// Utility functions for cache management
export const cacheUtils = {
  // Invalidate all queries matching a pattern
  invalidateQueries: (pattern: readonly any[]) => {
    return queryClient.invalidateQueries({ queryKey: pattern as any[] });
  },
  
  // Remove specific query from cache
  removeQueries: (pattern: any[]) => {
    return queryClient.removeQueries({ queryKey: pattern });
  },
  
  // Get cached data
  getQueryData: <T>(key: any[]) => {
    return queryClient.getQueryData<T>(key);
  },
  
  // Set cached data
  setQueryData: <T>(key: any[], data: T) => {
    return queryClient.setQueryData<T>(key, data);
  },
  
  // Clear all cache
  clear: () => {
    return queryClient.clear();
  },
  
  // Cancel ongoing queries
  cancelQueries: (pattern: any[]) => {
    return queryClient.cancelQueries({ queryKey: pattern });
  },
  
  // Prefetch query
  prefetchQuery: (key: any[], queryFn: () => Promise<any>) => {
    return queryClient.prefetchQuery({ queryKey: key, queryFn });
  },
};

// React Query devtools configuration
export const devToolsConfig = {
  initialIsOpen: false,
  position: 'bottom-right' as any, // TODO: Fix with correct DevtoolsPosition type
  toggleButtonProps: {
    style: {
      marginLeft: '5px',
      transform: 'scale(0.8)',
    },
  },
};

export default queryClient;