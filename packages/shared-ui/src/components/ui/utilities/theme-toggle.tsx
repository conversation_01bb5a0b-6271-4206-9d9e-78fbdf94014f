import { motion, AnimatePresence } from "framer-motion"
import { Moon, Sun, Monitor } from "lucide-react"
import { cn } from '../../../lib/utils'
import { forwardRef, ButtonHTMLAttributes } from "react"
import { useTheme } from "../../../providers/theme-provider"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface ThemeToggleProps extends Omit<ButtonHTMLAttributes<HTMLButtonElement>, "onClick"> {
  variant?: ComponentVariant | "glass" | "minimal"
  showLabel?: boolean
  showSystemOption?: boolean
  size?: ComponentSize | "xs" | "sm" | "md" | "lg"
}

const ThemeToggle = forwardRef<HTMLButtonElement, ThemeToggleProps>(
  ({
    className,
    variant = defaultComponentProps.variant,
    showLabel = false,
    showSystemOption = false,
    size = defaultComponentProps.size,
    disabled,
    ...props
  }, ref) => {
    const { theme, resolvedTheme, setTheme, toggleTheme } = useTheme()

    const cycleTheme = () => {
      if (showSystemOption) {
        // Cycle through: light -> dark -> system -> light
        if (theme === "light") {
          setTheme("dark")
        } else if (theme === "dark") {
          setTheme("system")
        } else {
          setTheme("light")
        }
      } else {
        // Simple toggle between light and dark
        toggleTheme()
      }
    }

    const sizeClasses = {
      xs: "w-3 h-3 p-1",
      sm: "w-4 h-4 p-1.5",
      md: "w-5 h-5 p-2",
      lg: "w-6 h-6 p-2.5"
    }

    const variantClasses = {
      default: "bg-gray-200 dark:bg-gray-800 hover:bg-gray-300 dark:hover:bg-gray-700 text-gray-900 dark:text-gray-100",
      glass: "backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30 hover:bg-white/20 dark:hover:bg-gray-900/20 text-gray-900 dark:text-gray-100",
      minimal: "hover:bg-gray-100 dark:hover:bg-gray-900 text-gray-700 dark:text-gray-300",
    }

    const getIcon = () => {
      if (showSystemOption && theme === "system") {
        return Monitor
      }
      return resolvedTheme === "dark" ? Moon : Sun
    }

    const getLabel = () => {
      if (showSystemOption && theme === "system") {
        return "System"
      }
      return resolvedTheme === "dark" ? "Dark" : "Light"
    }

    const Icon = getIcon()

    return (
      <motion.button
        ref={ref}
        className={cn(
          "relative inline-flex items-center justify-center rounded-lg transition-all duration-200",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",
          "focus-visible:ring-offset-background",
          variantClasses[variant],
          showLabel && "px-4 gap-2",
          sizeClasses[size],
          className
        )}
        onClick={cycleTheme}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
        title={showSystemOption ? `Current: ${getLabel()}. Click to cycle themes.` : `Switch to ${resolvedTheme === "dark" ? "light" : "dark"} mode`}
        disabled={disabled}
        aria-label={`Toggle theme: ${getLabel()}`}
      >
        <div className={cn("relative", sizeClasses[size])}>
          <AnimatePresence mode="wait">
            <motion.div
              key={`${theme}-${resolvedTheme}`}
              initial={{ scale: 0, rotate: theme === "system" ? 0 : (resolvedTheme === "dark" ? -180 : 180), opacity: 0 }}
              animate={{ scale: 1, rotate: 0, opacity: 1 }}
              exit={{ scale: 0, rotate: theme === "system" ? 0 : (resolvedTheme === "dark" ? 180 : -180), opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="absolute inset-0 flex items-center justify-center"
            >
              <Icon className={sizeClasses[size]} />
            </motion.div>
          </AnimatePresence>
        </div>

        {showLabel && (
          <AnimatePresence mode="wait">
            <motion.span
              key={`${theme}-${resolvedTheme}-label`}
              initial={{ opacity: 0, x: 10 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -10 }}
              transition={{ duration: 0.2 }}
              className="text-sm font-medium"
            >
              {getLabel()}
            </motion.span>
          </AnimatePresence>
        )}
      </motion.button>
    )
  }
)
ThemeToggle.displayName = "ThemeToggle"

export { ThemeToggle }
export { ThemeToggle as LuminarThemeToggle }