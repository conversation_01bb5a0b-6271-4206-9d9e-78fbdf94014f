import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes, useState } from "react"
import { Star } from "lucide-react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface LuminarRatingProps extends HTMLAttributes<HTMLDivElement> {
  value?: number
  max?: number
  size?: ComponentSize | "md" | "lg"
  readonly?: boolean
  allowHalf?: boolean
  allowHalfStar?: boolean
  glass?: boolean
  onValueChange?: (value: number) => void
}

const LuminarRating = forwardRef<HTMLDivElement, LuminarRatingProps>(
  ({ 
    value = 0,
    max = 5,
    size = "md",
    readonly = false,
    allowHalf = false,
    allowHalfStar = false,
    glass = true,
    onValueChange,
    className,
    ...props 
  }, ref) => {
    const [hoverValue, setHoverValue] = useState<number | null>(null)
    const displayValue = hoverValue !== null ? hoverValue : value

    const sizeClasses = {
      xs: "w-3 h-3",
      sm: "w-4 h-4",
      md: "w-5 h-5",
      lg: "w-6 h-6"
    }

    const handleClick = (starValue: number) => {
      if (!readonly) {
        onValueChange?.(starValue)
      }
    }

    const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>, index: number) => {
      if (readonly) return
      
      const rect = e.currentTarget.getBoundingClientRect()
      const x = e.clientX - rect.left
      const width = rect.width
      const isHalf = (allowHalf || allowHalfStar) && x < width / 2
      
      setHoverValue(index + (isHalf ? 0.5 : 1))
    }

    const handleMouseLeave = () => {
      setHoverValue(null)
    }

    return (
      <div
        ref={ref}
        className={cn(
          "inline-flex items-center gap-1",
          glass && "p-2 rounded-lg backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30",
          !readonly && "cursor-pointer",
          className
        )}
        onMouseLeave={handleMouseLeave}
        {...props}
      >
        {Array.from({ length: max }, (_, index) => {
          const starValue = index + 1
          const isFilled = displayValue >= starValue
          const isHalfFilled = displayValue > index && displayValue < starValue

          return (
            <motion.div
              key={index}
              className="relative"
              onClick={() => handleClick(starValue)}
              onMouseMove={(e) => handleMouseMove(e, index)}
              whileHover={!readonly ? { scale: 1.2 } : {}}
              whileTap={!readonly ? { scale: 0.9 } : {}}
              initial={{ rotate: -180, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              transition={{ delay: index * 0.1 }}
            >
              {/* Background star */}
              <Star 
                className={cn(
                  sizeClasses[size],
                  "text-gray-300 dark:text-gray-600 fill-gray-300 dark:fill-gray-600"
                )}
              />
              
              {/* Filled star */}
              <motion.div
                className="absolute inset-0 overflow-hidden"
                animate={{
                  width: isHalfFilled ? "50%" : isFilled ? "100%" : "0%"
                }}
                transition={{ duration: 0.2 }}
              >
                <Star 
                  className={cn(
                    sizeClasses[size],
                    "text-yellow-500 fill-yellow-500"
                  )}
                />
              </motion.div>
              
              {/* Hover effect */}
              {!readonly && hoverValue !== null && (
                <motion.div
                  className="absolute inset-0"
                  initial={{ scale: 0 }}
                  animate={{ scale: [1, 1.5, 1] }}
                  transition={{ duration: 0.3 }}
                >
                  <Star 
                    className={cn(
                      sizeClasses[size],
                      "text-yellow-400 fill-transparent opacity-50"
                    )}
                  />
                </motion.div>
              )}
            </motion.div>
          )
        })}
        
        {/* Value display */}
        {displayValue > 0 && (
          <motion.span
            className="ml-2 text-sm font-medium"
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
          >
            {displayValue.toFixed((allowHalf || allowHalfStar) && displayValue % 1 !== 0 ? 1 : 0)}
          </motion.span>
        )}
      </div>
    )
  }
)
LuminarRating.displayName = "LuminarRating"

export { LuminarRating }

// Simplified API for quick usage (shadcn/ui style)
export const Rating = LuminarRating