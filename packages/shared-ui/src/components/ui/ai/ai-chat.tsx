import { motion, AnimatePresence } from "framer-motion";
import { forwardRef, useEffect, useRef, useState } from "react";
import { Send, Bo<PERSON>, User, Co<PERSON>, ThumbsUp, ThumbsDown, RotateCcw, Square, Zap } from "lucide-react";
import { cn } from '../../../lib/utils';
import { LuminarButton } from "../actions/button-advanced";
import { LuminarInput } from "../forms/input";
import { LuminarText } from "../display/text";
import { LuminarTooltip } from "../feedback/tooltip";
import { LoadingSpinner } from "../display/loading-spinner";
import { useAIChat } from "../../../hooks/use-ai-chat";
import { useTheme } from "../../../providers/theme-provider";
import { getGlassClasses, animationPresets } from '../../../design-system';
import { Message } from "ai";
import type { AIMessage } from '../../../types/ai';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface AIChatProps {
  className?: string;
  size?: ComponentSize;
  placeholder?: string;
  showTimestamps?: boolean;
  maxHeight?: string;
  systemPrompt?: string;
  model?: string;
  temperature?: number;
  onMessageSent?: (message: string) => void;
  onResponseReceived?: (response: string) => void;
  onError?: (error: Error) => void;
}

const AIChat = forwardRef<HTMLDivElement, AIChatProps>(
  ({
    className,
    size = "md",
    placeholder = "Ask me anything...",
    showTimestamps = true,
    maxHeight = "600px",
    systemPrompt,
    model,
    temperature,
    onMessageSent,
    onResponseReceived,
    onError,
    ...props
  }, ref) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);
    const { resolvedTheme } = useTheme();
    
    // Initialize AI chat with configuration
    const aiChat = useAIChat();
    
    // Update configuration when props change
    useEffect(() => {
      if (systemPrompt || model || temperature) {
        aiChat.updateConfig({
          systemPrompt: systemPrompt || aiChat.systemPrompt,
          model: model || aiChat.model,
          temperature: temperature || aiChat.temperature,
        });
      }
    }, [systemPrompt, model, temperature]);

    // Auto-scroll to bottom when new messages arrive
    useEffect(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [aiChat.messages, aiChat.isStreaming]);

    // Handle callbacks
    useEffect(() => {
      if (aiChat.error && onError) {
        onError(aiChat.error);
      }
    }, [aiChat.error, onError]);

    // Handle message submission
    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (aiChat.input.trim() && aiChat.isReady && !aiChat.loading) {
        const message = aiChat.input.trim();
        aiChat.sendMessage(message);
        onMessageSent?.(message);
      }
    };

    // Handle keyboard shortcuts
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSubmit(e);
      }
    };

    // Copy message content
    const handleCopyMessage = async (messageId: string, content: string) => {
      try {
        await navigator.clipboard.writeText(content);
        setCopiedMessageId(messageId);
        setTimeout(() => setCopiedMessageId(null), 2000);
      } catch (error) {
        console.error('Failed to copy message:', error);
      }
    };

    // Streaming text component with typewriter effect
    const StreamingText = ({ text, isComplete }: { text: string; isComplete: boolean }) => {
      const [displayText, setDisplayText] = useState('');
      const [currentIndex, setCurrentIndex] = useState(0);
      
      useEffect(() => {
        if (isComplete) {
          setDisplayText(text);
          return;
        }
        
        if (currentIndex < text.length) {
          const timeout = setTimeout(() => {
            setDisplayText(text.slice(0, currentIndex + 1));
            setCurrentIndex(prev => prev + 1);
          }, 10 + Math.random() * 20); // Variable speed for natural feel
          
          return () => clearTimeout(timeout);
        }
      }, [text, currentIndex, isComplete]);
      
      return (
        <span className="relative">
          {displayText}
          {!isComplete && (
            <motion.span
              animate={{ opacity: [0, 1, 0] }}
              transition={{ duration: 0.8, repeat: Infinity }}
              className="inline-block w-0.5 h-4 bg-blue-400 ml-1 align-middle"
            />
          )}
        </span>
      );
    };

    // Enhanced message component with streaming support
    const MessageBubble = ({ message, index }: { message: Message; index: number }) => {
      const isUser = message.role === 'user';
      const isAssistant = message.role === 'assistant';
      const isStreaming = aiChat.isStreaming && index === aiChat.messages.length - 1 && isAssistant;
      
      const glassClasses = getGlassClasses(
        isUser ? 'button' : 'card',
        {
          intensity: isUser ? 'md' : 'light',
          depth: 'surface',
          animated: true,
          interactive: false
        }
      );

      return (
        <motion.div
          key={message.id}
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ 
            type: "spring",
            stiffness: 300,
            damping: 30,
            delay: index * 0.1 
          }}
          className={cn(
            "flex gap-3 max-w-[85%]",
            isUser ? "ml-auto flex-row-reverse" : "mr-auto"
          )}
        >
          {/* Avatar */}
          <div className={cn(
            "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",
            glassClasses,
            isUser ? "bg-blue-500/20" : "bg-gray-500/20"
          )}>
            {isUser ? (
              <User className="w-4 h-4 text-blue-400" />
            ) : (
              <motion.div
                animate={isStreaming ? { 
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                } : {}}
                transition={{
                  duration: 2,
                  repeat: isStreaming ? Infinity : 0,
                  ease: "easeInOut"
                }}
              >
                <Bot className="w-4 h-4 text-gray-400" />
              </motion.div>
            )}
          </div>

          {/* Message Content */}
          <div className="flex flex-col gap-2 flex-1">
            {/* Message bubble */}
            <motion.div 
              className={cn(
                "relative p-3 rounded-lg",
                glassClasses,
                isUser && "bg-blue-500/10",
                isStreaming && "bg-gradient-to-r from-blue-500/5 to-purple-500/5"
              )}
              animate={isStreaming ? { 
                boxShadow: [
                  "0 0 0 0 rgba(59, 130, 246, 0.1)",
                  "0 0 0 4px rgba(59, 130, 246, 0.1)",
                  "0 0 0 0 rgba(59, 130, 246, 0.1)"
                ]
              } : {}}
              transition={{
                duration: 1.5,
                repeat: isStreaming ? Infinity : 0,
                ease: "easeInOut"
              }}
            >
              {/* Message text */}
              <LuminarText 
                text=""
                className={cn(
                  "text-sm whitespace-pre-wrap",
                  isUser ? "text-blue-100" : "text-gray-100"
                )}
              >
                {isStreaming ? (
                  <StreamingText text={message.content} isComplete={false} />
                ) : (
                  message.content
                )}
              </LuminarText>

              {/* Streaming indicator */}
              {isStreaming && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="absolute -top-2 -right-2"
                >
                  <div className="w-4 h-4 rounded-full bg-blue-500/20 flex items-center justify-center">
                    <Zap className="w-2 h-2 text-blue-400" />
                  </div>
                </motion.div>
              )}

              {/* Message actions */}
              <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="flex gap-1">
                  <LuminarTooltip content={copiedMessageId === message.id ? "Copied!" : "Copy message"}>
                    <LuminarButton
                      size="xs"
                      variant="glass"
                      onClick={() => handleCopyMessage(message.id, message.content)}
                    >
                      <Copy className="w-3 h-3" />
                    </LuminarButton>
                  </LuminarTooltip>
                  
                  {isAssistant && (
                    <>
                      <LuminarTooltip content="Good response">
                        <LuminarButton
                          size="xs"
                          variant="glass"
                        >
                          <ThumbsUp className="w-3 h-3" />
                        </LuminarButton>
                      </LuminarTooltip>
                      
                      <LuminarTooltip content="Regenerate">
                        <LuminarButton
                          size="xs"
                          variant="glass"
                          onClick={() => aiChat.regenerateLastMessage()}
                          disabled={aiChat.loading}
                        >
                          <RotateCcw className="w-3 h-3" />
                        </LuminarButton>
                      </LuminarTooltip>
                    </>
                  )}
                </div>
              </div>
            </motion.div>

            {/* Timestamp and metadata */}
            {showTimestamps && (
              <div className={cn(
                "text-xs text-gray-400 px-2 flex items-center gap-2",
                isUser ? "justify-end" : "justify-start"
              )}>
                {message.createdAt && (
                  <span>
                    {new Date(message.createdAt).toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </span>
                )}
                {(message as AIMessage).metadata?.processingTime && (
                  <span className="opacity-60">
                    ({(message as AIMessage).metadata?.processingTime}ms)
                  </span>
                )}
                {(message as AIMessage).metadata?.tokenCount && (
                  <span className="opacity-60">
                    ~{(message as AIMessage).metadata?.tokenCount?.total} tokens
                  </span>
                )}
                {(message as AIMessage).metadata?.model && (
                  <span className="opacity-60 text-xs bg-gray-500/20 px-1 rounded">
                    {(message as AIMessage).metadata?.model}
                  </span>
                )}
              </div>
            )}
          </div>
        </motion.div>
      );
    };

    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col h-full",
          className
        )}
        {...props}
      >
        {/* Configuration display */}
        {aiChat.isConfigured && (
          <div className="px-4 py-2 border-b border-white/10">
            <div className="flex items-center gap-4 text-xs text-gray-500">
              <span>Model: {aiChat.model}</span>
              <span>Temperature: {aiChat.temperature}</span>
              <span>Tokens: {aiChat.stats.totalTokens}</span>
              <span>Messages: {aiChat.stats.totalMessages}</span>
            </div>
          </div>
        )}

        {/* Messages Container */}
        <div
          className="flex-1 overflow-y-auto p-4 space-y-4"
          style={{ maxHeight }}
        >
          <AnimatePresence mode="popLayout">
            {aiChat.messages.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex flex-col items-center justify-center h-full text-center"
              >
                <motion.div
                  animate={{ 
                    scale: [1, 1.1, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="mb-4"
                >
                  <Bot className="w-12 h-12 text-gray-400" />
                </motion.div>
                <LuminarText 
                  text="Start an AI Conversation"
                  className="text-lg font-medium text-gray-300 mb-2"
                />
                <LuminarText 
                  text="Ask me anything and I'll respond with streaming AI-powered answers using the latest language models."
                  className="text-sm text-gray-500 max-w-md"
                />
              </motion.div>
            ) : (
              <>
                {aiChat.messages.map((message, index) => (
                  <div key={message.id} className="group">
                    <MessageBubble message={message} index={index} />
                  </div>
                ))}
              </>
            )}
          </AnimatePresence>
          
          {/* Scroll anchor */}
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 border-t border-white/10"
        >
          <form onSubmit={handleSubmit} className="flex gap-2">
            <div className="flex-1">
              <LuminarInput
                value={aiChat.input}
                onChange={(e) => aiChat.setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={placeholder}
                disabled={!aiChat.isReady}
                size={size}
                variant="input"
                className="w-full"
              />
            </div>
            
            {aiChat.loading ? (
              <LuminarButton
                variant="glass"
                size={size}
                onClick={aiChat.stopGeneration}
                icon={Square}
                animation="scale"
                className="text-red-400"
              />
            ) : (
              <LuminarButton
                type="submit"
                variant="glass"
                size={size}
                disabled={!aiChat.isReady || !aiChat.input.trim()}
                icon={Send}
                animation="scale"
                magnetic
              />
            )}
          </form>

          {/* Chat actions */}
          <div className="flex justify-between items-center mt-2">
            <LuminarText 
              text="Press Enter to send, Shift+Enter for new line"
              className="text-xs text-gray-500"
            />
            
            <div className="flex items-center gap-2">
              {aiChat.messages.length > 0 && (
                <LuminarButton
                  size="xs"
                  variant="ghost"
                  onClick={aiChat.clearConversation}
                  className="text-gray-500 hover:text-gray-300"
                >
                  Clear chat
                </LuminarButton>
              )}
              
              <LuminarButton
                size="xs"
                variant="ghost"
                onClick={aiChat.exportConversation}
                className="text-gray-500 hover:text-gray-300"
                disabled={aiChat.messages.length === 0}
              >
                Export
              </LuminarButton>
            </div>
          </div>

          {/* Error display */}
          {aiChat.error && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-2 p-2 rounded bg-red-500/10 border border-red-500/20"
            >
              <LuminarText 
                text={`Error: ${aiChat.error.message}`}
                className="text-xs text-red-400"
              />
            </motion.div>
          )}
        </motion.div>
      </div>
    );
  }
);
AIChat.displayName = "AIChat";

export { AIChat };