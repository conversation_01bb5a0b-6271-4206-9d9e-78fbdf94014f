import { motion } from "framer-motion"
import { cn } from '../../../lib/utils'
import { forwardRef, HTMLAttributes } from "react"
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react"
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../../types/component-props';
import { defaultComponentProps } from '../../../types/component-props';

export interface LuminarPaginationProps extends HTMLAttributes<HTMLDivElement> {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  showFirstLast?: boolean
  showPrevNext?: boolean
  maxVisiblePages?: number
  glass?: boolean
  animated?: boolean
  size?: ComponentSize | "xs" | "sm" | "md" | "lg"
}

const LuminarPagination = forwardRef<HTMLDivElement, LuminarPaginationProps>(
  ({ 
    currentPage,
    totalPages,
    onPageChange,
    showFirstLast = true,
    showPrevNext = true,
    maxVisiblePages = 5,
    glass = true,
    animated = true,
    size = "md",
    className,
    ...props 
  }, ref) => {
    const sizeClasses = {
      xs: "h-6 px-1.5 text-xs",
      sm: "h-8 px-2 text-xs",
      md: "h-10 px-3 text-sm",
      lg: "h-12 px-4 text-base"
    }

    const iconSizes = {
      xs: 12,
      sm: 14,
      md: 16,
      lg: 18
    }

    // Generate page numbers to display
    const getVisiblePages = () => {
      const pages: (number | 'ellipsis')[] = []
      
      if (totalPages <= maxVisiblePages) {
        // Show all pages if total is less than max
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i)
        }
      } else {
        // Calculate start and end pages
        let start = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))
        let end = Math.min(totalPages, start + maxVisiblePages - 1)
        
        // Adjust start if end is at the boundary
        if (end === totalPages) {
          start = Math.max(1, end - maxVisiblePages + 1)
        }
        
        // Add first page and ellipsis if needed
        if (start > 1) {
          pages.push(1)
          if (start > 2) {
            pages.push('ellipsis')
          }
        }
        
        // Add visible pages
        for (let i = start; i <= end; i++) {
          pages.push(i)
        }
        
        // Add ellipsis and last page if needed
        if (end < totalPages) {
          if (end < totalPages - 1) {
            pages.push('ellipsis')
          }
          pages.push(totalPages)
        }
      }
      
      return pages
    }

    const visiblePages = getVisiblePages()

    const handlePageChange = (page: number) => {
      if (page >= 1 && page <= totalPages && page !== currentPage) {
        onPageChange(page)
      }
    }

    const PaginationButton = ({ 
      page, 
      active, 
      disabled, 
      children, 
      onClick 
    }: {
      page?: number
      active?: boolean
      disabled?: boolean
      children: React.ReactNode
      onClick?: () => void
    }) => (
      <motion.button
        className={cn(
          "relative flex items-center justify-center rounded-lg font-medium transition-all duration-200",
          sizeClasses[size],
          glass && "backdrop-blur-md",
          active 
            ? "bg-primary text-primary-foreground shadow-lg" 
            : glass 
              ? "bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/30 hover:bg-white/20 dark:hover:bg-gray-700/30"
              : "bg-background border border-border hover:bg-accent",
          disabled && "opacity-50 cursor-not-allowed hover:bg-transparent",
          !disabled && !active && "hover:scale-105"
        )}
        onClick={onClick}
        disabled={disabled}
        whileHover={!disabled && !active ? { scale: 1.05 } : {}}
        whileTap={!disabled ? { scale: 0.95 } : {}}
        initial={animated ? { opacity: 0, scale: 0.8 } : {}}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ type: "spring", stiffness: 400, damping: 30 }}
      >
        {children}
        
        {/* Active indicator */}
        {active && (
          <motion.div
            className="absolute inset-0 rounded-lg border-2 border-primary/50"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.2 }}
          />
        )}
      </motion.button>
    )

    if (totalPages <= 1) {
      return null
    }

    return (
      <nav
        ref={ref}
        className={cn(
          "flex items-center justify-center gap-1",
          className
        )}
        aria-label="Pagination Navigation"
        {...props}
      >
        {/* First page button */}
        {showFirstLast && currentPage > 1 && (
          <PaginationButton
            onClick={() => handlePageChange(1)}
            disabled={currentPage === 1}
          >
            <span className="sr-only">Go to first page</span>
            <div className="flex items-center gap-1">
              <ChevronLeft size={iconSizes[size]} />
              <ChevronLeft size={iconSizes[size]} className="-ml-2" />
            </div>
          </PaginationButton>
        )}

        {/* Previous page button */}
        {showPrevNext && (
          <PaginationButton
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeft size={iconSizes[size]} />
          </PaginationButton>
        )}

        {/* Page numbers */}
        <div className="flex items-center gap-1">
          {visiblePages.map((page, index) => {
            if (page === 'ellipsis') {
              return (
                <motion.div
                  key={`ellipsis-${index}`}
                  className={cn(
                    "flex items-center justify-center",
                    sizeClasses[size]
                  )}
                  initial={animated ? { opacity: 0 } : {}}
                  animate={{ opacity: 1 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <MoreHorizontal size={iconSizes[size]} />
                </motion.div>
              )
            }

            return (
              <motion.div
                key={page}
                initial={animated ? { opacity: 0, scale: 0.8 } : {}}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
              >
                <PaginationButton
                  page={page}
                  active={page === currentPage}
                  onClick={() => handlePageChange(page)}
                >
                  <span className="sr-only">
                    {page === currentPage ? `Current page, page ${page}` : `Go to page ${page}`}
                  </span>
                  {page}
                </PaginationButton>
              </motion.div>
            )
          })}
        </div>

        {/* Next page button */}
        {showPrevNext && (
          <PaginationButton
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRight size={iconSizes[size]} />
          </PaginationButton>
        )}

        {/* Last page button */}
        {showFirstLast && currentPage < totalPages && (
          <PaginationButton
            onClick={() => handlePageChange(totalPages)}
            disabled={currentPage === totalPages}
          >
            <span className="sr-only">Go to last page</span>
            <div className="flex items-center gap-1">
              <ChevronRight size={iconSizes[size]} />
              <ChevronRight size={iconSizes[size]} className="-ml-2" />
            </div>
          </PaginationButton>
        )}
      </nav>
    )
  }
)
LuminarPagination.displayName = "LuminarPagination"

export { LuminarPagination }