"use client";

import React, { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Calendar,
  CheckCircle2,
  Circle,
  Code,
  Megaphone,
  Users,
  Shield,
  Rocket,
  UserPlus,
  ShoppingCart,
  ChevronDown,
  ChevronUp,
  Filter,
  BarChart3,
  Grid3x3,
  <PERSON>,
  Kanban,
  Clock,
} from "lucide-react";
import { cn } from '../../lib/utils';
import type {
  StandardComponentProps,
  StandardFormComponentProps,
  ComponentSize,
  ComponentVariant
} from '../../types/component-props';
import { defaultComponentProps } from '../../types/component-props';

// Event Types/Categories
export type EventCategory = 
  | "Development"
  | "Marketing"
  | "Partnerships"
  | "Security"
  | "Deployment"
  | "Community"
  | "E-commerce";

export type ViewMode = "vertical" | "horizontal" | "modern" | "kanban" | "gantt";

export interface TimelineEvent {
  id: string;
  title: string;
  description?: string;
  category: EventCategory;
  date: string;
  period: string; // e.g., "Q1 2024", "H2 2024"
  isCompleted: boolean;
  dependencies?: string[]; // IDs of dependent events
  assignee?: string;
  progress?: number; // 0-100
}

interface EventTimelineRoadmapProps {
  events: TimelineEvent[];
  viewMode?: ViewMode;
  onEventClick?: (event: TimelineEvent) => void;
  onEventUpdate?: (event: TimelineEvent) => void;
  className?: string;
  showFilters?: boolean;
  showAnalytics?: boolean;
}

// Category configuration with colors and icons
const CATEGORY_CONFIG: Record<EventCategory, {
  icon: React.ElementType;
  color: string;
  bgColor: string;
  borderColor: string;
}> = {
  Development: {
    icon: Code,
    color: "text-blue-600 dark:text-blue-400",
    bgColor: "bg-blue-100 dark:bg-blue-900/30",
    borderColor: "border-blue-200 dark:border-blue-800",
  },
  Marketing: {
    icon: Megaphone,
    color: "text-purple-600 dark:text-purple-400",
    bgColor: "bg-purple-100 dark:bg-purple-900/30",
    borderColor: "border-purple-200 dark:border-purple-800",
  },
  Partnerships: {
    icon: Users,
    color: "text-green-600 dark:text-green-400",
    bgColor: "bg-green-100 dark:bg-green-900/30",
    borderColor: "border-green-200 dark:border-green-800",
  },
  Security: {
    icon: Shield,
    color: "text-red-600 dark:text-red-400",
    bgColor: "bg-red-100 dark:bg-red-900/30",
    borderColor: "border-red-200 dark:border-red-800",
  },
  Deployment: {
    icon: Rocket,
    color: "text-orange-600 dark:text-orange-400",
    bgColor: "bg-orange-100 dark:bg-orange-900/30",
    borderColor: "border-orange-200 dark:border-orange-800",
  },
  Community: {
    icon: UserPlus,
    color: "text-cyan-600 dark:text-cyan-400",
    bgColor: "bg-cyan-100 dark:bg-cyan-900/30",
    borderColor: "border-cyan-200 dark:border-cyan-800",
  },
  "E-commerce": {
    icon: ShoppingCart,
    color: "text-pink-600 dark:text-pink-400",
    bgColor: "bg-pink-100 dark:bg-pink-900/30",
    borderColor: "border-pink-200 dark:border-pink-800",
  },
};

// View mode icons
const VIEW_MODES = [
  { mode: "vertical" as ViewMode, icon: List, label: "Vertical" },
  { mode: "horizontal" as ViewMode, icon: Grid3x3, label: "Horizontal" },
  { mode: "modern" as ViewMode, icon: BarChart3, label: "Modern" },
  { mode: "kanban" as ViewMode, icon: Kanban, label: "Kanban" },
  { mode: "gantt" as ViewMode, icon: Clock, label: "Gantt" },
];

export const EventTimelineRoadmap: React.FC<EventTimelineRoadmapProps> = ({
  events,
  viewMode = "vertical",
  onEventClick,
  onEventUpdate,
  className,
  showFilters = true,
  showAnalytics = false,
}) => {
  const [selectedCategories, setSelectedCategories] = useState<EventCategory[]>([]);
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set());
  const [currentViewMode, setCurrentViewMode] = useState<ViewMode>(viewMode);

  // Filter events based on selected categories
  const filteredEvents = useMemo(() => {
    if (selectedCategories.length === 0) return events;
    return events.filter(event => selectedCategories.includes(event.category));
  }, [events, selectedCategories]);

  // Group events by period
  const groupedEvents = useMemo(() => {
    return filteredEvents.reduce((acc, event) => {
      if (!acc[event.period]) {
        acc[event.period] = [];
      }
      acc[event.period].push(event);
      return acc;
    }, {} as Record<string, TimelineEvent[]>);
  }, [filteredEvents]);

  const toggleCategory = (category: EventCategory) => {
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const toggleEventExpansion = (eventId: string) => {
    setExpandedEvents(prev => {
      const newSet = new Set(prev);
      if (newSet.has(eventId)) {
        newSet.delete(eventId);
      } else {
        newSet.add(eventId);
      }
      return newSet;
    });
  };

  const renderEventCard = (event: TimelineEvent, index: number) => {
    const config = CATEGORY_CONFIG[event.category];
    const Icon = config.icon;
    const isExpanded = expandedEvents.has(event.id);

    return (
      <motion.div
        key={event.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
        className={cn(
          "relative p-4 rounded-lg border transition-all duration-200",
          config.borderColor,
          "hover:shadow-lg hover:scale-[1.02]",
          "cursor-pointer"
        )}
        onClick={() => onEventClick?.(event)}
      >
        {/* Completion indicator */}
        <div className="absolute -left-3 top-6">
          {event.isCompleted ? (
            <CheckCircle2 className="w-6 h-6 text-green-500" />
          ) : (
            <Circle className="w-6 h-6 text-gray-400" />
          )}
        </div>

        {/* Category badge */}
        <div className={cn(
          "inline-flex items-center gap-1.5 px-3 py-1 rounded-full text-xs font-medium mb-3",
          config.bgColor,
          config.color
        )}>
          <Icon className="w-3 h-3" />
          {event.category}
        </div>

        {/* Event title and date */}
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
          {event.title}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
          {event.date}
        </p>

        {/* Progress bar (if applicable) */}
        {event.progress !== undefined && (
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-3">
            <div
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${event.progress}%` }}
            />
          </div>
        )}

        {/* Expandable content */}
        <AnimatePresence>
          {isExpanded && event.description && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <p className="text-sm text-gray-700 dark:text-gray-300 mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                {event.description}
              </p>
              {event.assignee && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  Assigned to: {event.assignee}
                </p>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Expand/collapse button */}
        {event.description && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              toggleEventExpansion(event.id);
            }}
            className="absolute bottom-2 right-2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            {isExpanded ? (
              <ChevronUp className="w-4 h-4 text-gray-500" />
            ) : (
              <ChevronDown className="w-4 h-4 text-gray-500" />
            )}
          </button>
        )}
      </motion.div>
    );
  };

  const renderVerticalTimeline = () => (
    <div className="relative">
      {/* Timeline line */}
      <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-purple-500 to-blue-500" />

      {/* Events grouped by period */}
      {Object.entries(groupedEvents).map(([period, periodEvents], periodIndex) => (
        <motion.div
          key={period}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: periodIndex * 0.1 }}
          className="mb-12"
        >
          {/* Period header */}
          <div className="flex items-center gap-3 mb-6">
            <div className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              {period}
            </div>
          </div>

          {/* Events in this period */}
          <div className="space-y-4 ml-16">
            {periodEvents.map((event, index) => renderEventCard(event, index))}
          </div>
        </motion.div>
      ))}
    </div>
  );

  const renderFilters = () => (
    <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
      <div className="flex items-center gap-2 mb-3">
        <Filter className="w-5 h-5 text-gray-600 dark:text-gray-400" />
        <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">
          Filter by Category
        </h3>
      </div>
      <div className="flex flex-wrap gap-2">
        {Object.entries(CATEGORY_CONFIG).map(([category, config]) => {
          const Icon = config.icon;
          const isSelected = selectedCategories.includes(category as EventCategory);
          return (
            <button
              key={category}
              onClick={() => toggleCategory(category as EventCategory)}
              className={cn(
                "inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-medium transition-all",
                isSelected ? cn(config.bgColor, config.color) : "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400",
                "hover:scale-105"
              )}
            >
              <Icon className="w-3 h-3" />
              {category}
            </button>
          );
        })}
      </div>
    </div>
  );

  const renderViewModeSelector = () => (
    <div className="flex items-center gap-2 mb-6">
      {VIEW_MODES.map(({ mode, icon: Icon, label }) => (
        <button
          key={mode}
          onClick={() => setCurrentViewMode(mode)}
          className={cn(
            "p-2 rounded-lg transition-all",
            currentViewMode === mode
              ? "bg-gradient-to-r from-purple-500 to-blue-500 text-white"
              : "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
          )}
          title={label}
        >
          <Icon className="w-5 h-5" />
        </button>
      ))}
    </div>
  );

  return (
    <div className={cn("w-full", className)}>
      {/* View mode selector */}
      {renderViewModeSelector()}

      {/* Filters */}
      {showFilters && renderFilters()}

      {/* Timeline content */}
      <div className="mt-8">
        {currentViewMode === "vertical" && renderVerticalTimeline()}
        {/* Other view modes can be implemented similarly */}
        {currentViewMode !== "vertical" && (
          <div className="text-center py-12 text-gray-500">
            {currentViewMode.charAt(0).toUpperCase() + currentViewMode.slice(1)} view coming soon...
          </div>
        )}
      </div>
    </div>
  );
};