import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useAuthStore } from '../lib/stores/auth.store';
import { User } from '../lib/api/client';

// RBAC Types
export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

export interface Role {
  id: string;
  name: string;
  displayName: string;
  permissions: Permission[];
  description?: string;
}

export interface AuthContextType {
  // User state
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Authentication methods
  login: (credentials: { email: string; password: string }) => Promise<boolean>;
  logout: () => Promise<void>;
  register: (data: { email: string; password: string; firstName?: string; lastName?: string }) => Promise<boolean>;
  
  // Permission & Role checking
  hasPermission: (permission: string, resource?: string) => boolean;
  hasRole: (roleName: string) => boolean;
  hasAnyRole: (roleNames: string[]) => boolean;
  hasAllRoles: (roleNames: string[]) => boolean;
  
  // Permission checking with resources
  canRead: (resource: string) => boolean;
  canWrite: (resource: string) => boolean;
  canDelete: (resource: string) => boolean;
  canAdmin: (resource: string) => boolean;
  
  // Utility functions
  getUserRoles: () => Role[];
  getUserPermissions: () => Permission[];
  clearError: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
  enableRBAC?: boolean;
  autoRefreshInterval?: number; // in milliseconds
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ 
  children, 
  enableRBAC = true,
  autoRefreshInterval = 5 * 60 * 1000 // 5 minutes
}) => {
  const {
    user,
    isAuthenticated,
    isLoading,
    error,
    login: storeLogin,
    logout: storeLogout,
    register: storeRegister,
    refreshUser: storeRefreshUser,
    clearError: storeClearError,
    checkAuth
  } = useAuthStore();

  const [initializationComplete, setInitializationComplete] = useState(false);

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        await checkAuth();
      } catch (error) {
        console.error('Auth initialization failed:', error);
      } finally {
        setInitializationComplete(true);
      }
    };

    initializeAuth();
  }, [checkAuth]);

  // Auto-refresh user data
  useEffect(() => {
    if (!isAuthenticated || !autoRefreshInterval) return;

    const interval = setInterval(async () => {
      try {
        await storeRefreshUser();
      } catch (error) {
        console.error('Auto-refresh failed:', error);
      }
    }, autoRefreshInterval);

    return () => clearInterval(interval);
  }, [isAuthenticated, autoRefreshInterval, storeRefreshUser]);

  // Authentication methods
  const login = useCallback(async (credentials: { email: string; password: string }) => {
    try {
      const response = await storeLogin(credentials);
      return response !== null;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  }, [storeLogin]);

  const logout = useCallback(async () => {
    try {
      await storeLogout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }, [storeLogout]);

  const register = useCallback(async (data: { email: string; password: string; firstName?: string; lastName?: string }) => {
    try {
      return await storeRegister(data);
    } catch (error) {
      console.error('Registration failed:', error);
      return false;
    }
  }, [storeRegister]);

  const refreshUser = useCallback(async () => {
    try {
      await storeRefreshUser();
    } catch (error) {
      console.error('User refresh failed:', error);
    }
  }, [storeRefreshUser]);

  // RBAC Helper functions
  const getUserRoles = useCallback((): Role[] => {
    if (!enableRBAC || !user?.roles) return [];
    return user.roles.map(role => ({
      id: role.id,
      name: role.name,
      displayName: role.name,
      permissions: role.permissions.map(permission => ({
        id: permission,
        name: permission,
        resource: permission.split(':')[0] || '',
        action: permission.split(':')[1] || '',
        description: `${permission.split(':')[1] || 'access'} permission for ${permission.split(':')[0] || 'resource'}`
      }))
    }));
  }, [enableRBAC, user]);

  const getUserPermissions = useCallback((): Permission[] => {
    if (!enableRBAC || !user?.roles) return [];
    const allPermissions: Permission[] = [];
    
    user.roles.forEach(role => {
      role.permissions.forEach(permission => {
        const permissionObj: Permission = {
          id: permission,
          name: permission,
          resource: permission.split(':')[0] || '',
          action: permission.split(':')[1] || '',
          description: `${permission.split(':')[1] || 'access'} permission for ${permission.split(':')[0] || 'resource'}`
        };
        
        // Avoid duplicates
        if (!allPermissions.some(p => p.id === permissionObj.id)) {
          allPermissions.push(permissionObj);
        }
      });
    });
    
    return allPermissions;
  }, [enableRBAC, user]);

  // Permission checking
  const hasPermission = useCallback((permission: string, resource?: string): boolean => {
    if (!enableRBAC || !user?.roles) return false;
    
    const fullPermission = resource ? `${resource}:${permission}` : permission;
    
    return user.roles.some(role => 
      role.permissions.includes(fullPermission) || 
      role.permissions.includes('*:*') || // Super admin
      role.permissions.includes(`${resource}:*`) || // Resource admin
      role.permissions.includes(`*:${permission}`) // Action admin
    );
  }, [enableRBAC, user]);

  const hasRole = useCallback((roleName: string): boolean => {
    if (!enableRBAC || !user?.roles) return false;
    return user.roles.some(role => role.name === roleName);
  }, [enableRBAC, user]);

  const hasAnyRole = useCallback((roleNames: string[]): boolean => {
    if (!enableRBAC || !user?.roles) return false;
    return user.roles.some(role => roleNames.includes(role.name));
  }, [enableRBAC, user]);

  const hasAllRoles = useCallback((roleNames: string[]): boolean => {
    if (!enableRBAC || !user?.roles || !Array.isArray(user.roles)) return false;
    return roleNames.every(roleName => 
      user.roles!.some(role => role.name === roleName)
    );
  }, [enableRBAC, user]);

  // Resource-specific permission checking
  const canRead = useCallback((resource: string): boolean => {
    return hasPermission('read', resource);
  }, [hasPermission]);

  const canWrite = useCallback((resource: string): boolean => {
    return hasPermission('write', resource) || hasPermission('create', resource) || hasPermission('update', resource);
  }, [hasPermission]);

  const canDelete = useCallback((resource: string): boolean => {
    return hasPermission('delete', resource);
  }, [hasPermission]);

  const canAdmin = useCallback((resource: string): boolean => {
    return hasPermission('admin', resource) || hasPermission('*', resource);
  }, [hasPermission]);

  const contextValue: AuthContextType = {
    // User state
    user,
    isAuthenticated,
    isLoading: isLoading || !initializationComplete,
    error,
    
    // Authentication methods
    login,
    logout,
    register,
    
    // Permission & Role checking
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    
    // Resource-specific permissions
    canRead,
    canWrite,
    canDelete,
    canAdmin,
    
    // Utility functions
    getUserRoles,
    getUserPermissions,
    clearError: storeClearError,
    refreshUser
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Higher-order component for auth protection
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  requiredPermissions?: string[],
  requiredRoles?: string[]
) => {
  return function WithAuthComponent(props: P) {
    const { isAuthenticated, hasPermission, hasRole, isLoading } = useAuth();
    
    if (isLoading) {
      return <div>Loading...</div>; // You can replace this with a proper loading component
    }
    
    if (!isAuthenticated) {
      return <div>Please log in to access this content.</div>;
    }
    
    // Check required permissions
    if (requiredPermissions && requiredPermissions.length > 0) {
      const hasRequiredPermissions = requiredPermissions.every(permission => hasPermission(permission));
      if (!hasRequiredPermissions) {
        return <div>You don't have permission to access this content.</div>;
      }
    }
    
    // Check required roles
    if (requiredRoles && requiredRoles.length > 0) {
      const hasRequiredRoles = requiredRoles.some(role => hasRole(role));
      if (!hasRequiredRoles) {
        return <div>You don't have the required role to access this content.</div>;
      }
    }
    
    return <Component {...props} />;
  };
};

export default AuthProvider;