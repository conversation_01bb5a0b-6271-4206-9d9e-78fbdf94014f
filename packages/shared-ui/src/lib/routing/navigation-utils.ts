/**
 * Navigation Utilities - Shared navigation helpers and utilities
 */

import { useN<PERSON><PERSON>, useRouter, useLocation } from '@tanstack/react-router';

// Navigation types
export interface NavigationOptions {
  replace?: boolean;
  resetScroll?: boolean;
  state?: any;
  search?: Record<string, any>;
  hash?: string;
}

export interface BreadcrumbItem {
  label: string;
  path?: string;
  isActive?: boolean;
}

// Navigation utilities
export class NavigationUtils {
  private navigate: ReturnType<typeof useNavigate>;
  private router: ReturnType<typeof useRouter>;

  constructor(navigate: ReturnType<typeof useNavigate>, router: ReturnType<typeof useRouter>) {
    this.navigate = navigate;
    this.router = router;
  }

  // Navigate to a route with options
  async navigateTo(path: string, options: NavigationOptions = {}) {
    try {
      await this.navigate({
        to: path,
        replace: options.replace,
        resetScroll: options.resetScroll,
        search: options.search,
        hash: options.hash,
        state: options.state
      });
    } catch (error) {
      console.error('[Navigation] Failed to navigate to:', path, error);
      throw error;
    }
  }

  // Navigate back in history
  goBack() {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      // Fallback to home if no history
      this.navigateTo('/');
    }
  }

  // Navigate forward in history
  goForward() {
    window.history.forward();
  }

  // Replace current route
  async replace(path: string, options: Omit<NavigationOptions, 'replace'> = {}) {
    return this.navigateTo(path, { ...options, replace: true });
  }

  // Navigate with search params
  async navigateWithSearch(path: string, search: Record<string, any>, options: NavigationOptions = {}) {
    return this.navigateTo(path, { ...options, search });
  }

  // Navigate to external URL
  navigateExternal(url: string, target = '_blank') {
    window.open(url, target);
  }

  // Check if current route matches path
  isCurrentRoute(path: string): boolean {
    return this.router.state.location.pathname === path;
  }

  // Check if current route starts with path (for nested routes)
  isActiveRoute(path: string): boolean {
    return this.router.state.location.pathname.startsWith(path);
  }

  // Get current route info
  getCurrentRoute() {
    return {
      pathname: this.router.state.location.pathname,
      search: this.router.state.location.search,
      hash: this.router.state.location.hash,
      state: this.router.state.location.state
    };
  }
}

// Hook to use navigation utilities
export function useNavigationUtils() {
  const navigate = useNavigate();
  const router = useRouter();
  
  return new NavigationUtils(navigate, router);
}

// Breadcrumb utilities
export function useBreadcrumbs(): BreadcrumbItem[] {
  const location = useLocation();
  const pathSegments = location.pathname.split('/').filter(Boolean);
  
  const breadcrumbs: BreadcrumbItem[] = [
    { label: 'Home', path: '/' }
  ];

  let currentPath = '';
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const isLast = index === pathSegments.length - 1;
    
    breadcrumbs.push({
      label: formatSegmentLabel(segment),
      path: isLast ? undefined : currentPath,
      isActive: isLast
    });
  });

  return breadcrumbs;
}

// Format path segment for display
function formatSegmentLabel(segment: string): string {
  return segment
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Route matching utilities
export const routeUtils = {
  // Check if route matches pattern
  matchesPattern: (pathname: string, pattern: string): boolean => {
    const regex = new RegExp(
      '^' + pattern.replace(/\*/g, '.*').replace(/:\w+/g, '[^/]+') + '$'
    );
    return regex.test(pathname);
  },

  // Extract params from route
  extractParams: (pathname: string, pattern: string): Record<string, string> => {
    const params: Record<string, string> = {};
    const patternParts = pattern.split('/');
    const pathParts = pathname.split('/');

    patternParts.forEach((part, index) => {
      if (part.startsWith(':')) {
        const paramName = part.slice(1);
        params[paramName] = pathParts[index] || '';
      }
    });

    return params;
  },

  // Build route with params
  buildRoute: (pattern: string, params: Record<string, string>): string => {
    let route = pattern;
    Object.entries(params).forEach(([key, value]) => {
      route = route.replace(`:${key}`, value);
    });
    return route;
  },

  // Get route depth
  getDepth: (pathname: string): number => {
    return pathname.split('/').filter(Boolean).length;
  },

  // Check if route is nested under parent
  isNestedUnder: (pathname: string, parent: string): boolean => {
    return pathname.startsWith(parent) && pathname !== parent;
  }
};

// Search params utilities
export function useSearchParams() {
  const location = useLocation();
  const navigate = useNavigate();

  const setSearchParam = (key: string, value: string) => {
    const newSearch = { ...location.search, [key]: value };
    navigate({ search: newSearch });
  };

  const removeSearchParam = (key: string) => {
    const newSearch = { ...location.search };
    delete newSearch[key];
    navigate({ search: newSearch });
  };

  const getSearchParam = (key: string): string | undefined => {
    return location.search[key];
  };

  const clearSearchParams = () => {
    navigate({ search: {} as any });
  };

  return {
    searchParams: location.search,
    setSearchParam,
    removeSearchParam,
    getSearchParam,
    clearSearchParams
  };
}

// Route transition utilities
export const transitionUtils = {
  // Add loading state during navigation
  withLoading: async (navigationFn: () => Promise<void>, loadingCallback?: (loading: boolean) => void) => {
    loadingCallback?.(true);
    try {
      await navigationFn();
    } finally {
      loadingCallback?.(false);
    }
  },

  // Add confirmation before navigation
  withConfirmation: async (
    navigationFn: () => Promise<void>,
    message = 'Are you sure you want to leave this page?'
  ) => {
    if (window.confirm(message)) {
      await navigationFn();
    }
  },

  // Navigate with analytics tracking
  withAnalytics: async (
    navigationFn: () => Promise<void>,
    eventName: string,
    properties?: Record<string, any>
  ) => {
    try {
      // Track navigation event
      if (typeof window !== 'undefined' && (window as any).analytics) {
        (window as any).analytics.track(eventName, properties);
      }
      await navigationFn();
    } catch (error) {
      console.error('[Navigation Analytics] Failed to track navigation:', error);
      throw error;
    }
  }
};

// Cross-app navigation utilities
export const crossAppNavigation = {
  // Navigate to another app
  navigateToApp: (app: string, path = '/') => {
    const appUrls: Record<string, string> = {
      'amna': '/amna',
      'e-connect': '/e-connect',
      'training': '/training',
      'vendors': '/vendors',
      'wins': '/wins',
      'lighthouse': '/lighthouse',
      'shell': '/shell'
    };

    const baseUrl = appUrls[app];
    if (baseUrl) {
      window.location.href = `${baseUrl}${path}`;
    } else {
      console.error(`[Cross-App Navigation] Unknown app: ${app}`);
    }
  },

  // Check if we can navigate to app
  canNavigateToApp: (app: string): boolean => {
    const appUrls = ['amna', 'e-connect', 'training', 'vendors', 'wins', 'lighthouse', 'shell'];
    return appUrls.includes(app);
  },

  // Get current app from URL
  getCurrentApp: (): string | null => {
    const pathname = window.location.pathname;
    const appMatch = pathname.match(/^\/([^\/]+)/);
    return appMatch ? appMatch[1] : null;
  }
};